<template>
	<view class="body-box" :style="loadingStyle">
		<view class="flex flex-col flex-center">
			<view>
				<u--image :src="imageSrc" :fade="true" duration="450" :width="width" :height="height"></u--image>
			</view>
			<text class="text-loading">{{text}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "loading",
		props: {
			loadingType: {
				type: Number | String,
				default: 1
			},
			loadingText: {
				type: String,
				default: '加载中...'
			},
			loadingImage: {
				type: String,
				default: 'https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif'
			},
			noDataText: {
				type: String,
				default: '没有相关数据'
			},
			noDataImage: {
				type: String,
				default: 'https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/04/01/53bcd807f0a644b1a04cd8f8d8f88170.png'
			},
			errorText: {
				type: String,
				default: '处理错误'
			},
			errorImage: {
				type: String,
				default: 'https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/04/01/ddc59e6a94434e6381505055da67be33.png'
			},
			width: {
				type: String,
				default: '100rpx'
			},
			height:{
				type: String,
				default: '100rpx'
			},
			loadingStyle:{
				type: Object,
				default: ()=>{}
			}
		},
		data() {
			return {
				
			}
		},
		computed:{
			imageSrc(){
				return this.loadingType == 1 ? this.loadingImage : this.loadingType == 2 ? this.noDataImage : this.errorImage
			},
			text(){
				return this.loadingType == 1 ? '加载中...' : this.loadingType == 2 ? '没有相关数据' : '处理错误'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.body-box {
		box-sizing: border-box;
		width: 100vw;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 990;
		.text-loading {
			color: #999999;
			font-size: 24rpx;
			margin-top: 20rpx;
		}
	}
</style>