import Vuex from 'vuex'

const state = {
	tabindex: 0,
	isOk:false,
	tabbar:{
		color: "#161616",
		selectedColor: "#FF6900",
		borderStyle: "black",
		backgroundColor: "#ffffff",
		list:[
			{
				pagePath: "pages/home/<USER>",
				iconPath: "static/images/tabbar/shouye.png",
				selectedIconPath: "static/images/tabbar/shouye1.png",
				text: "首页",
				openType: 'switchTab',
			},
			{
				pagePath: "pageg/shop_detail/index",
				// pagePath: "pageg/shop_detail/index?placeId=1891758887364456448",
				iconPath: "https://file.36sm.cn/jaxinfo/2025/06/10/dcd7417ee833404a916343257cb4f7d9.png",
				selectedIconPath: "https://file.36sm.cn/jaxinfo/2025/06/10/dcd7417ee833404a916343257cb4f7d9.png",
				text: "",
				// openType: 'switchTab',
				openType: 'linkTabbar',
			},
			{
				pagePath: "pages/account/user",
				iconPath: "static/images/tabbar/wode.png",
				selectedIconPath: "static/images/tabbar/wode1.png",
				text: "我的",
				openType: 'switchTab',
			}
		]
	},
}

const getters ={
	
}

const mutations = {
	SET_INDEX(state,index){
		state.tabindex = index
	},
	SET_ISOK(state, isOk){
		state.isOk = isOk
	}
}

const actions = {
	setIndex({commit},index){
		commit('SET_INDEX',index)
	},
	setIsOk({commit}, isOk){
		commit("SET_ISOK",isOk)
	}
}
export default {
	state,
	mutations,
	actions,
	getters
}