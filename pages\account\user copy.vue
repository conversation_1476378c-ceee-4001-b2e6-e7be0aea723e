<template>
	<view class="wrapper">
		<newNavBar :title="$t('accountUser.wd')" width='100vw' textSize="40rpx" iconLeft="none" textColor="#000"></newNavBar>
		<view class="theme_view" v-if="visible">
			<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="false"
				:use-page-scroll="true" :auto="false">
				<refresh-loading slot="refresher"></refresh-loading>
				<!-- 顶部内容 -->
				<view class="top-content">
					<!-- 内容 -->
					<view class="content">
						<!-- <view class="user-top"> -->
						<view class="head-base" >
							<!-- <view class="Setting" style="display: block;display: flex;flex-direction: row-reverse;margin: -15rpx -20rpx 0 0">
								<view class="">
									<!- <u-icon name="setting" color="#fff" size="28"></u-icon> ->
									<image class="setting-img" @tap="goSetting" src="https://file.36sm.cn/xtjyjt/images/common/account/setting.png" mode=""></image>
								</view>
								<view style="width: 20rpx;">
								</view>
								<view>
									<!- <u-icon name="grid" color="#fff" size="30" ></u-icon> ->
									<image class="codeImg" src="https://file.36sm.cn/xtjyjt/images/common/account/i.png" mode="" @tap="Qrcode">
									</image>
								</view>
								<view style="width: 20rpx;">
								</view>
								<view class="">
									<image class="setting-img" @tap="goMessage" src="https://file.36sm.cn/xtjyjt/images/common/xiaox.png"
									mode=""></image>
								</view>
								<view style="width: 25rpx;">
								</view>
							</view> -->
							<!-- 左侧头像 -->
							<view class="head-left" style="">
								<view style="display: flex;justify-content: space-between;">
									<image data-value="/pages/personal/personal"  @error="user_avatar_error"
										class="head-avatar circle va-m" :src="avatar" mode="widthFix"></image>
									<view class="va-m dis-inline-block margin-left-lg">
										<view class="flex-col align-b" data-value="/pages/personal/personal"
											>
											<view class="word">
											<view class="flex" style="display: flex;margin-bottom: 20rpx; align-items: center;">
												<text class="va-m fw-b nickname" style="font-size: 36rpx;"
													>{{ nickName }}</text>
														<image data-value="" @tap="sz_url_event" @error="user_avatar_error"
										class="sz" :src="sz" mode="widthFix" style="margin-left: 24rpx;"></image>
										<image data-value="" @tap="cl_url_event" @error="user_avatar_error"
										class="sz" :src="cl" mode="widthFix" style="margin-left: 25rpx;"></image>
											</view>
												

												<!-- <text style="color: #D9D9D9;">我的推荐码：{{invite_code == "" ? "无" : invite_code}}</text> -->
												<text class="tip">{{invite_code == "" ? "无" : invite_code}}</text>
											</view>
											<!-- <view class="head-id">
												<text class="text-size-xs">关注 </text>
												<text class="text-size-xss padding-left-xs pr">999</text>
											</view> -->
										</view>
									</view>
									<!-- <view style="display: flex;flex-direction: column-reverse;margin-left: 100rpx;color:#e3e3e3;margin-bottom: 5rpx;" @tap="to_yq">
										<text >邀请记录</text>
									</view> -->
								</view>
								<!-- <view v-if="nickName!='用户名'"> -->
								<view>
									<image @tap="qrcode" class="qrcode" src="/static/images/qrcode.png" mode="">
									</image>
								</view>
							</view>
						</view>
						<!-- </view> -->
						<view class="page">
							<!-- <view v-if="navigation.length > 0" class="spacing-mt">
								<view class="padding-horizontal-main">
									<view class="backcolor border-radius-main">
										<view class="nav_top" @tap="to_order(null , -1)">
											<text style="margin-left: 22rpx;margin-top: 20rpx;"><text>{{$t('accountUser.ddgl')}}</text></text>
											<navigator class="nav_base">{{$t('accountUser.qb')}}
												<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
													style="margin-left: 6rpx;width: 12rpx;height: 18rpx;" />
											</navigator>
										</view>
										<view class="flex justify-between align-center u-px-40 u-py-20 tabBtn">
											<view class="flex flex-col flex-center btnitem" v-for="(item,index) in navigation" :key="index" @tap="to_order(item,index)" >
												<image style="height: 60rpx;width: 60rpx;" :src="item.imgUrl" mode="aspectFit"></image>
												<text class="u-m-t-10" style="font-size: 24rpx;">{{item.name}}</text>
												<view v-if="item.num" class="num">{{item.num}}</view>
											</view>
										</view>
									</view>
								</view>
							</view> -->
							<view class="user_purse">
								<view class="vip_card" >
									<text>{{ $t('accountUser.hyk') }}:</text>
									<text class="vip_card_num">1</text><text>{{ $t('accountUser.zhang') }}</text>
								</view>
								<view class="my_points_card">
									<view class="my_badge">
									 	<image src="/static/images/vip.png" class="my_badge_image"></image>
									</view>
									<view class="my_points" @tap="to_jf">
										<text>{{ $t('accountUser.jf') }}:&nbsp;</text>
										<text class="my_points_num">{{ my_money.my_intergal == 0 ? 0 : my_money.my_intergal}}</text><text>{{ $t('accountUser.fen') }}</text>
									</view>
								</view>
							</view>
							<!-- <view class="main_middle" style="margin-top: -20rpx;" >
								<view class="topText">
									<text class="middle_text">{{$t('accountUser.wdqb')}}</text>
									<view class="">
										<text class="middle_text" @tap="to_tx">{{$t('accountUser.wytx')}} </text>
										<text class="middle_text" style="color: #1D91FF;margin-left: 100rpx;" @tap="to_cz">{{$t('accountUser.wycz')}}</text>
									</view>
								</view>
								
								<view class="content_middle">
									<view class="middle_word" @tap="to_ye" v-if="my_money.balancename">
										<text style="font-size:28rpx;">
											{{my_money.my_balance == 0 ? 0 : my_money.my_balance}}</text>
										<text style="font-size:22rpx;color:#8B8B8B;margin-top:18rpx;">
										  {{my_money.balancename || $t('accountUser.ye')}}
										</text>
									</view>
									<view class="middle_word" @tap="to_jf" v-if="my_money.intergalname">
										<text style="font-size:28rpx;">
											{{ my_money.my_intergal == 0 ? 0 : my_money.my_intergal}}
											</text>
											
										<text style="font-size:22rpx;color:#8B8B8B;margin-top:18rpx;">
											<!- 积分 ->
											{{my_money.intergalname || $t('accountUser.jf')}}
											</text>
									</view>
									
									<view class="middle_word" @tap="to_gx" v-if="my_money.contributename">
											<text style="font-size:28rpx;">
												{{ my_money.my_contribute == 0 ? 0 : my_money.my_contribute}}
											</text>
										<text style="font-size:22rpx;color:#8B8B8B;margin-top:22rpx;">
											{{my_money.contributename }}
											</text>
									</view>
									
									<view class="middle_word" @tap="to_yj" v-if="my_money.achievementname">
										<text style="font-size:28rpx;">
											{{ my_money.my_achievement == 0 ? 0 : my_money.my_achievement}}
										</text>
										<text style="font-size:22rpx;color:#8B8B8B;margin-top:18rpx;">
											{{my_money.achievementname || $t('accountUser.yj')}}
										</text>
									</view>
								</view>
							</view> -->
							<!-- <view style="margin-top: 8rpx;" v-if="islogin" class="main_middle_s" :class="userType == 1 ? 'main_middle_s' : 'unlogin_s'">
								<view class="items" @tap="to_dz" :class="userType == 1 ? 'items' : 'unloginitems'">
									<view class="item_S">
										<image src="https://file.36sm.cn/mttttxxs/2025/01/15/b388c29df08f4a61affc22e546d1cdcb.png" style="margin-right:18rpx; height:29rpx;width: 26rpx;" alt="" />
										<!- <text>地址管理{{userType}}A</text> ->
										<text>地址管理</text>
									</view>
									<view class="item_b">
										<text>地址管理</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png"
											style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" alt="" />
									</view>
								</view>
								<view class="items" @click="addResource" v-if="userType == 1">
									<view class="item_S">
										<image src="https://file.36sm.cn/mttttxxs/2025/01/15/064116d864084bf8a0d34c9d58b7061f.png" style="margin-right:18rpx; height:29rpx;width: 26rpx;" alt="" />
										<text>资源管理</text>
									</view>
									<view class="item_b">
										<text>资源管理</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png"
											style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" alt="" />
									</view>
								</view>
								<view class="items" @click="goPayment" v-if="userType == 1">
									<view class="item_S">
										<image src="https://file.36sm.cn/mttttxxs/2025/01/15/484427adc91249d6b6cfb30688a521aa.png"
											style="margin-right:18rpx;height:28rpx;width: 29rpx;" alt="" />
										<text>回款管理</text>
									</view>
									<view class="item_b">
										<text>回款管理</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png"
											style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" alt="" />
									</view>
								</view>
							</view> -->
							<view style="margin-top: 8rpx;" v-if="!islogin" class="unlogin_s">
								<view class="unloginitems" @tap="to_dz" style="border-bottom: none;">
									<view class="item_S">
										<image src="https://file.36sm.cn/mttttxxs/2025/01/15/b388c29df08f4a61affc22e546d1cdcb.png" style="margin-right:18rpx; height:29rpx;width: 26rpx;" alt="" />
										<text>地址管理</text>
									</view>
									<view class="item_b">
										<text>地址管理</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png"
											style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" alt="" />
									</view>
								</view>
							</view>
							<view class="main_bottom" style="margin-top: 8rpx;">
								<view class="itemb" @tap="to_order(null , -1)">
									<text>{{$t('accountUser.xfjl')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
									style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
								</view>
								<view class="itemb" @tap="to_tsjy">
									<text>{{$t('accountUser.fxjl')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
									style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
								</view>
								<view class="itemb" @tap="goMessage">
									<text>{{$t('accountUser.xtxx')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
									style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
								</view>
								<view class="itemb" @tap="to_tsjy">
									<text>{{$t('accountUser.fkjy')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
										style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
								</view>
								<view class="itemb" @tap="aboutus">
									<text>{{$t('accountUser.gywm')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
										style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
								</view>
							</view>
							<!-- 结尾 -->
							<!-- <component-bottom-line :propStatus="data_bottom_line_status"></component-bottom-line> -->
							<!-- 版权信息 -->
							<!-- <view v-if="load_status == 1">
								<component-copyright></component-copyright>
							</view> -->
						</view>

					</view>
				</view>
			</z-paging>
			<uni-popup class="loginPop" ref="loginpop" type="center">
				<iconfont name="icon-huiyuan-guanbi" class="closeIcon" @tap="login_pop_close" size="28rpx" color="#999">
				</iconfont>
				<view class="hot-tip">
					<text class="tip-title">温馨提示</text>
					<text class="tip-content">登录或注册后解锁更多精彩内容哦~</text>
					<button class="tip-login" @tap="login_pop">立即登录</button>
				</view>
			</uni-popup>
			
			
			<uni-popup ref="fundPopup" type="bottom" :is-mask-click="false" class="fundPopup">
				<view class="InfoPopup" @touchmove.stop.prevent="moveHandle" style="min-height: 400rpx;max-height: 1200rpx;">
					<view class="PopupCon">
						<view class="topCon">
							<view style="color: #fff;">
								1
							</view>
							<view class="title" style="color: #fff;">
								{{$t('accountUser.zjzr')}}
							</view>
							<view class="topClose" @click="closefundPopup">
								<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 25rpx;height: 25rpx;"></image>
							</view>
						</view>
						<view class="fundcontent">
							<view class="walletitem" v-for="(item,index) in walletTypelist" :key="index">
								<view class="walletdetail" @tap="lentfund(item)">
									<view class="balance">
										{{item.coinName}} : {{item.balance}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</uni-popup>
			
			
			<!-- 语言切换弹窗 -->
			<uni-popup ref="languagePopup" type="center" :is-mask-click="true">
				<view class="languagePopup" @touchmove.stop.prevent="moveHandle">
					<view class="PopupCon">
						<view class="topCon">
							<view style="color: #fff;">
								1
							</view>
							<view class="title" style="color: #000;">
								<!-- 切换语言 -->
								{{$t('accountUser.qhyy')}}
							</view>
							<view style="color: #fff;">
								1
							</view>
						</view>
						<view class="radiocontent">
							<u-radio-group v-model="languagevalue" placement="column" @change="groupChange" iconPlacement="right" borderBottom>
							    <u-radio
							      :customStyle="{marginBottom: '30rpx'}"
								  activeColor="#2A3A88FF"
							      v-for="(item, index) in languageList"
							      :key="index"
							      :label="item.name"
							      :name="item.value"
								  :value="item.value"
							      @change="radioChange"
							    >
							    </u-radio>
							  </u-radio-group>
							
						</view>
						
						<view class="confirmbtn">
							<u-button color="linear-gradient(to right, #2A3A88FF , #2A3A88FF)" :text="$t('search.qd')" :loadingText="$t('accountUser.jzz')" @tap="changeconfirm" :loading="Cloading"></u-button>
						</view>
					</view>
				</view>
			</uni-popup>
			
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
			
			<!-- <tabbar-com path="pages/account/user"></tabbar-com> -->
			<!-- <Tabbar path="pages/account/user"></Tabbar> -->
			
			
	    <uni-popup ref="addestimates" type="bottom" :is-mask-click="false">
	    	<view class="remarksinfo">
	    		<view class="PopupCon">
	    			<view class="topCon">
	    				<view class="delect">
	    					<text>1</text>
	    				</view>
	    				<view class="title">
	    					<text>公司资源管理, 添加资源可获得奖励</text>
	    				</view>
	    				<view class="Close" @click="closePopup">
	    					<text>取消</text>
	    				</view>
	    			</view>
	    			<view class="Ccontent">
	    				<view class="sdtx" @click="addbase">
	    					<text>添加基地</text>
	    				</view>
	    				<view class="khtx" @click="addcoach">
	    					<text>添加教练</text>
	    				</view>
	    				<view class="sdtx" @click="addverdor">
	    					<text>添加供应商</text>
	    				</view>
	    			</view>
	    		</view>
	    	</view>
	    </uni-popup>
		
		<!-- <Tabbar path="/pages/account/user"></Tabbar> -->
	</view>
</template>
<script>
	const app = getApp();
	import componentQuickNav from '../../components/quick-nav/quick-nav';
	import componentCopyright from '../../components/copyright/copyright';
	import componentUserBase from '../../components/user-base/user-base';
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import ZPMixin from "@/subpackageuni/z-paging/components/z-paging/js/z-paging-mixin"
	import { mapState, mapMutations } from 'vuex'
	import { getUserInfo, getOrderNum } from "@/common/request/api/user.js"
	import TabbarCom from '../../components/tabbar/tabbar.vue'
	
	import api from '@/common/request/index.js'
	
  // 状态栏高度
  var bar_height = parseInt(app.globalData.get_system_info('statusBarHeight', 0, true));
  // #ifdef MP-TOUTIAO
  bar_height = 0;
  // #endif
	var common_static_url = app.globalData.get_static_url('common');
	var static_url = app.globalData.get_static_url('user');
	var client_value = app.globalData.application_client();
	export default {
		mixins: [ZPMixin],
		data() {
			return {
				my_money: {
					// 余额
					my_balance: 0,
					balancename: '',
					// 积分
					my_intergal: 0,
					intergalname: '',
					// 贡献值
					my_contribute: 0,
					contributename: '',
					// 业绩
					my_achievement: 0,
					achievementname: ''
				},
				sz:"/static/icon/sz.png",
				cl:"/static/icon/cl.png",
				languageList: [
					{
						name: '简体中文',
						value: 'zh'
					},
					{
						name: 'English',
						value: 'en'
					},
					{
						name: 'ភាសាខ្មែរ',
						value: 'kh'
					}
				],
				language: "简体中文",
				languagevalue: uni.getStorageSync('locale') || 'zh',
				Cloading: false,
				walletTypelist: [],
				visible: false,
				theme_view: app.globalData.get_theme_value_view(),
				common_static_url: common_static_url,
				static_url: static_url,
				// https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/02/17/10a195577d6a4879968a238c1514a932.png
				avatar: '',
				memberId: '',
				nickName: '用户名',
				data_bottom_line_status: true,
				load_status: 1,
				message_total: 0,
				nav_logout_data: {
					name: client_value == 'mp' ? '清除缓存' : '退出账号',
					icon: client_value == 'mp' ? 'cache' : 'logout',
				},
				// 基础配置
				common_app_customer_service_tel: null,
				common_user_center_notice: null,
				common_app_is_online_service: 0,
				common_app_is_head_vice_nav: 0,
				// 会员码地址
				membership_page_url: null,
				// 付款码地址
				payment_page_url: null,
				// 用户中心菜单展示模式
				nav_show_model_type: app.globalData.data.user_center_nav_show_model_type,
				// titleVisible: false,
				identify_status: false,
				invite_code: "",
				statusBarHeight:"",
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				// 是否是登录状态  默认是登录状态
				islogin: true,
				userType: ''
			};
		},

		components: {
			componentQuickNav,
			componentCopyright,
			componentUserBase,
			componentBottomLine,
			TabbarCom,
		},
		props: {},
		computed:{
			...mapState({
				oneUserPage:state => state.user.oneUserPage,
			}),
			token() {
				return uni.getStorageSync("token")
			},
			navigation() {
				return [
					{ name: '待支付', value: 0, orderStatus: 0, key: 'payStatus', num: 0, imgUrl: 'https://file.36sm.cn/xtjyjt/images/common/account/o.png' },
					{ name: '已支付', value: 1, orderStatus: 6, key: 'OrderStatus', num: 0, imgUrl: 'https://file.36sm.cn/xtjyjt/images/common/account/m.png' },
					{ name: '已取消', value: -1, orderStatus: 7, key: 'OrderStatus', num: 0, imgUrl: 'https://file.36sm.cn/mttttxxs/2025/02/05/5b41063286e44871a10b07b00d1fa9d6.png' },
					{ name: '已完成', value: 10, orderStatus: 99, key: 'OrderStatus', num: 0, imgUrl: 'https://file.36sm.cn/xtjyjt/images/common/account/ywc.png' }
				]
			}
		},
		onShow() {
			let token = uni.getStorageSync("token")
			setTimeout(() => {
				if (!token) {
					console.log('未登录的状态!')
					this.setData({
						// avatar: "https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/02/17/10a195577d6a4879968a238c1514a932.png",
						avatar: "",
						nickName: "用户名",
						invite_code: "无",
						my_money: {
							my_intergal: 0,
							my_balance: 0,
							my_contribute: 0,
							my_achievement: 0
						},
						islogin: false,
						userType: ''
					})
				} else {
					console.log('登录的状态!')
					this.setData({
						islogin: true
					})
					this.init();
					this.init_config();
					if(this.$refs.fundPopup) {
					   this.$refs.fundPopup.close()
					}
				}
			}, 100)
		},
		onLoad() {
			setTimeout(() => {
				this.$data.visible = true
			}, 1200)
			this.identify_person()
		},

		methods: {
			...mapMutations(['setOneUserPage', 'setUserInfo']),
			to_ye(){
				uni.navigateTo({
					url: "/pageo/fund_value/fund_value"
				})
			},
			to_jf() {
				uni.navigateTo({
					url: "/pageo/points_record/points_record"
				})
			},
			to_tsjy() {
				// 投诉建议
				uni.navigateTo({
					url: "/pageo/complaint_suggest/complaints"
				})
			},
			aboutus() {
				// 关于我们协议
				uni.navigateTo({
					// url: `/pageo/message_manage/about_us`,
					url: '/pageu/about_us/about_us'
				})
			},
			to_gx() {
				uni.navigateTo({
					url: "/pageo/contri_record/contri_record"
				})
			},
			to_yj() {
				uni.navigateTo({
					url: "/pageo/outstand_record/outstand_record"
				})
			},
			to_tx() {
				uni.navigateTo({
					url: "/pageo/my_Payouts/my_Payouts"
				})
			},
			to_cz() {
				uni.navigateTo({
					url: "/pageo/my_top_ups/my_top_ups"
				})
			},
			to_yq() {
				uni.navigateTo({
					url: "/pageo/invitation/invitation"
				})
			},
			to_yhk() {
				uni.navigateTo({
					url: "/pageo/bank_cards/bank_cards"
				})
			},
			to_dz() {
				uni.navigateTo({
					url: "/pageu/user-address/user-address"
				})
			},
			to_collection() {
				uni.navigateTo({
					url: "/pageu/collection_c/collection_c"
				})
			},
			addResource() {
				this.$refs.addestimates.open()
			},
			goPayment() {
				uni.navigateTo({
					url: "/pageu/payment/index"
				})
			},
			closePopup() {
				this.$refs.addestimates.close()
			},
			// 添加基地
			addbase() {
				uni.navigateTo({
					url: "/pageu/base/index"
				})
				setTimeout(() => {
					this.closePopup()
				}, 2000)
			},
			// 添加教练
			addcoach() {
				uni.navigateTo({
					url: "/pageu/coach/index"
				})
				setTimeout(() => {
					this.closePopup()
				}, 2000)
			},
			// 添加供应商
			addverdor() {
				uni.navigateTo({
					url: "/pageu/verdor/index"
				})
				setTimeout(() => {
					this.closePopup()
				}, 2000)
			},
			changelanage() {
				// 语言切换
				console.log('语言切换')
				this.$refs.languagePopup.open()
			},
			groupChange(e) {
				console.log(e, '父eeee')
				this.languagevalue = e
				console.log(this.languagevalue, '当前选择的语言')
			},
			changeconfirm() {
				this.Cloading = true
				setTimeout(() => {
					this.$refs.languagePopup.close()
					this.$i18n.locale = this.languagevalue
					uni.setStorageSync('locale', this.languagevalue)
					this.$forceUpdate();
					uni.setLocale(this.languagevalue);
					this.Cloading = false
				}, 1500)
			},
			radioChange(e) {
				// console.log(e, '子eeee')
			},
			// 获取支持转让的类型
			getwalletType() {
				api({
				    url: 'mb/wallet/zj/get/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '转让的类型')
						this.walletTypelist = [...res.data.data]
						console.log(this.walletTypelist, '资金转让类型')
					} else {
						uni.showToast({
							title: res.msg,
							// title: '网络超时,请重试',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: "none"
					})
				})
			},
			to_zjzj() {
				this.getwalletType()
				setTimeout(() => {
				  this.$refs.fundPopup.open()
				}, 200)
				
			},
			lentfund(item) {
				console.log(item, '资金转让')
				uni.navigateTo({
					// url: `/pageg/lent_fund/lentfund?coinId=${item.coinId}&balance=${item.balance}`
					url: `/pageg/lent_fund/lentfund?coinId=${item.coinId}`
				})
			},
			closefundPopup() {
				this.$refs.fundPopup.close()
			},
			moveHandle() {	
			},
			login_pop_close() {
				this.$refs.loginpop.close()
			},
			login_pop() {
				uni.navigateTo({
					url: "/pageo/login/login"
				})
				this.$refs.loginpop.close()
			},
			queryList() {
				this.init()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			Qrcode() {
				let token = uni.getStorageSync("token");
				if (token) {
					setTimeout(() => {
						uni.navigateTo({
							url: "/pageo/barcode/barcode"
						})
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			goSetting() {
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_cache_info();
				if (token) {
					setTimeout(() => {
						uni.navigateTo({
							url: "/pageo/setup/setup"
						})
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			goMessage() {
				// 查看消息
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_cache_info();
				if (token) {
					setTimeout(() => {
						uni.navigateTo({
							url: "/pageo/message_manage/message_manage"
						})
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			be_partner() {
				
				uni.navigateTo({
					url: "/pageu/partner/partner"
				})
				
				return
				var user = app.globalData.get_user_info(this, 'init');
				if (user) {
					uni.navigateTo({
						url: "/pageu/partner/partner"
					})
				} else {
					uni.navigateTo({
						url: '/pageo/login/login?event_callback=init',
					})
				}
			},
			open_clone_popup() {
				this.$refs.popup.open("bottom")
			},
			clone_pop_close() {
				this.$refs.popup.close()
			},
			ime_clone_page() {
				uni.navigateTo({
					url: "/pageu/clone/ime-clone/ime-clone"
				})
				this.$refs.popup.close()
			},
			identify_person() {
				api({
				    url: 'mb/member/invite/code',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						this.setData({
							identify_status: true,
							invite_code: res.data.data
						})
					} else {
						if(this.oneUserPage){
							this.setOneUserPage(false)
							uni.showToast({
								title: err.msg,
								icon: 'none'
							})
						}
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			get_money() {
				api({
				    url: 'mb/wallet/get/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						let data = res.data.data
						console.log(data, '钱包信息')
						data.map(item => {
							if (item.coinId == 2) {
								this.my_money.my_intergal = item.balance
								this.my_money.intergalname = item.coinName
							} else if (item.coinId == 1) {
								// this.setData({
								// 	my_money: {
								// 		my_balance: item.balance
								// 	}
								// })
								this.my_money.my_balance = item.balance
								this.my_money.balancename = item.coinName
							} else if (item.coinId == 3) {
								this.my_money.my_contribute = item.balance
								this.my_money.contributename = item.coinName
							} else if (item.coinId == 4) {
								this.my_money.my_achievement = item.balance
								this.my_money.achievementname = item.coinName
							}
						})
						 // 在请求结束后手动更新视图
						this.setData({
							my_money: this.my_money,
						});
						console.log(this.my_money, '钱包信息')
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: 'none'
					})
				})
			},
			// 初始化配置
			init_config(status) {
				if ((status || false) == true) {
					// 会员码及付款码入口
					var membership_page_url = null;
					var payment_page_url = null;
					if (app.globalData.get_config('plugins_base.wallet', null) != null) {
						payment_page_url = '/pages/plugins/wallet/payment-code/payment-code';
					}
					if (app.globalData.get_config('plugins_base.membershiplevelvip', null) != null) {
						membership_page_url = '/pages/plugins/membershiplevelvip/member-code/member-code';
					}
					this.setData({
						common_app_customer_service_tel: app.globalData.get_config(
							'config.common_app_customer_service_tel'),
						common_user_center_notice: app.globalData.get_config('config.common_user_center_notice'),
						common_app_is_online_service: app.globalData.get_config(
							'config.common_app_is_online_service'),
						common_app_is_head_vice_nav: app.globalData.get_config(
							'config.common_app_is_head_vice_nav'),
						membership_page_url: membership_page_url,
						payment_page_url: payment_page_url,
					});
				} else {
					app.globalData.is_config(this, 'init_config');
				}
			},

			// 获取数据
			async init() {
				this.setData({
					data_bottom_line_status: true,
					load_status: 1,
				})
				await this.get_user_base()
				await this.get_money()
				await this.identify_person()
				await this.get_OrderNum()
			},
			
				get_OrderNum() {
					let token = uni.getStorageSync("token");
					if(!token) {
						// 退出登录 需要将其值置为0
						this.navigation.forEach(item => {
							item.num = 0
						})
						return
					}
					api({
					    url: 'goods/order/get/mx',
					    header: {
					        "jarepair-platform": uni.getStorageSync("token")
					    },
					    method: 'get'
					}).then(res => {
						if (res.code == 200) {
							console.log(res.data.data, '订单列表数量')
							if(res.data.data.length > 0) {
								res.data.data.forEach((value, index) => {
									// 检查 this.navigation 是否有对应的对象
									if (this.navigation[index]) {
										this.navigation[index].num = Number(value); // 更新 num 属性
									} else {
										// 如果没有对应的对象，可以选择创建一个新对象
										this.navigation[index] = { num: value };
									}
								});
								console.log(this.navigation, 'navigation')
							}
						} else {
							uni.showToast({
							    title: res.msg || '网络超时, 请重新加载',
							    icon: "none"
							});
						}
					}).catch(err => {
						uni.showToast({
						    title: err.msg || '网络超时, 请重新加载',
						    icon: "none"
						});
					})
					
				},
			// 设置用户基础信息
			get_user_base() {
				getUserInfo().then(res => {
					if(res.code == 200) {
						let data = res.data.data
						console.log(data, '用户信息')
						
						const { logoUrl, nickName, ...resData} = data;
						
						this.setData({
							    avatar: data.logoUrl,
								nickName: data.nickName,
								userType: data.type,
								...resData,
							})
						this.setUserInfo(data)
					} else {
						if(this.oneUserPage){
							this.setOneUserPage(false)
							uni.showToast({
								title: err.msg,
								icon: 'none'
							})
						}
					}
				}).catch(err => {
					if(this.oneUserPage){
						this.setOneUserPage(false)
						uni.showToast({
							title: err.msg,
							icon: 'none'
						})
					}
				})

			},

			// 头像加载错误
			user_avatar_error(e) {
				this.setData({
					avatar: app.globalData.data.default_user_head_src,
				});
			},
			// 远程自定义导航事件
			// navigation_event(e) {
			// 	app.globalData.operation_event(e);
			// },
			code_event() {
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_info(this, 'init');
				if (token) {
					this.identify_person()
					setTimeout(() => {
						if (this.identify_status == true) {
							uni.navigateTo({
								url: "/pageo/barcode/barcode"
							})
						} else {
							uni.navigateTo({
								url: "/pageu/user-identify/user-identify"
							})
						}
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			// url事件
			url_event() {
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_cache_info();
				if (token) {
					this.identify_person()
					setTimeout(() => {
						if (this.identify_status == true) {
							uni.navigateTo({
								url: "/pageo/setup/setup"
							})
						} else {
							// uni.navigateTo({
							// 	url: "/pageu/user-identify/user-identify"
							// })
							uni.navigateTo({
								url: '/pageo/login/login',
							})
						}
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			sz_url_event() {
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_cache_info();
				if (token) {
					this.identify_person()
					setTimeout(() => {
						if (this.identify_status == true) {
							uni.navigateTo({
								url: "/pageo/personal/personal"
							})
						} else {
							// uni.navigateTo({
							// 	url: "/pageu/user-identify/user-identify"
							// })
							uni.navigateTo({
								url: '/pageo/login/login',
							})
						}
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			cl_url_event() {
				let token = uni.getStorageSync("token");
				// var user = app.globalData.get_user_cache_info();
				if (token) {
					this.identify_person()
					setTimeout(() => {
						if (this.identify_status == true) {
							uni.navigateTo({
								url: "/pageo/setup/setup"
							})
						} else {
							// uni.navigateTo({
							// 	url: "/pageu/user-identify/user-identify"
							// })
							uni.navigateTo({
								url: '/pageo/login/login',
							})
						}
					}, 500)
				} else {
					uni.navigateTo({
						url: '/pageo/login/login',
					})
				}
			},
			to_order(item=null,index=-1) {
				// 未登录不能进入订单管理页面
				const token = uni.getStorageSync("token")
				if(token) {
					let data = item
					if(data === null){
						data = {
							name: '全部',
							orderStatus: 100
						}
					}
					this.$goTo('/pageu/user-order/user-order',{item:JSON.stringify(data),index:index + 1})
				} else {
					uni.showToast({
							title: "请先登录!",
							icon: 'none'
						})
					setTimeout(() => {
						uni.navigateTo({
							url: '/pageo/login/login',
						})
					}, 1000);
				}
			}
		},
	};
</script>
<style scoped>
	@import './user.css';
</style>

<style lang="scss" scoped>
	.theme_view {
		width: 100%;
		overflow-x: hidden;
	}
	.qrcode {
		width: 120rpx;
	height: 120rpx;
	}

	.head-base {
		width: 100%;
		box-sizing: border-box;
	}
	.head-base .head-left {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
	}
	.head-base .head-right {
		color: #fff;
	}
	

	.main_top {
		width: 670rpx;
		height: 160rpx;
		// background: url("https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/02/17/447d2d09f53a437788b44cfadabf9609.png") no-repeat;
		background-size: cover;
		background-position: center;
		display: flex;
		align-items: center;
		border-radius: 10rpx;
		padding-left: 30rpx;
		justify-content: space-between;
		
		margin: -10rpx 0 30rpx 0;
		margin-left: 30rpx;
		

		.countrt-par {
			font-size: 33rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 47rpx;
			letter-spacing: 1px;
		}

		.zuhe-image {
			width: 48rpx;
			height: 46rpx;
			margin-right: 17rpx;
		}

		.open-par {
			margin-top: 5rpx;
			font-size: 24rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 31rpx;
			letter-spacing: 1rpx;
		}

		.req-btn {
			width: 214rpx;
			height: 62rpx;
			background: #FFFFFF;
			border-radius: 228rpx 228rpx 228rpx 228rpx;
			opacity: 1;
			font-size: 25rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 500;
			color: #2A3A88FF;
			line-height: 62rpx;
			letter-spacing: 1px;
			margin-right: 26rpx;
		}
	}

	.titleHead {

		/deep/ .uni-navbar__header-btns-right {
			width: 125rpx !important;

		}

		.city {
			margin-right: 20rpx;

			text {
				font-size: 34rpx;
			}
		}
	}
	.codeImg {
		width: 47rpx;
		height: 46rpx;
		margin-right: 15rpx;
	}

	.setting-img {
		width: 50rpx;
		height: 46rpx;
	}

	.loginPop {
		width: 100%;
		height: 100%;

		/deep/ .uni-popup__wrapper {
			background: linear-gradient(180deg, #CFE3FF 0%, #FFFFFF 42%);
			width: 90%;
			border-radius: 18rpx 18rpx 18rpx 18rpx;
			height: 400rpx;

			.iconfont {
				position: absolute;
				right: 26rpx;
				top: 26rpx;
			}
		}

		.hot-tip {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			.tip-title {
				margin-top: 25rpx;
				font-size: 40rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 700;
				color: #3D3D3D;
				line-height: 58rpx;
				letter-spacing: 1px;
			}

			.tip-content {
				margin-top: 25rpx;
				font-size: 29rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #9E9E9E;
				line-height: 42rpx;
				letter-spacing: 1px;
			}

			.tip-login {
				margin-top: 73rpx;
				width: 341rpx;
				height: 83rpx;
				box-shadow: 0rpx 2rpx 5rpx 0rpx rgba(0, 0, 0, 0.1);
				opacity: 1;
				font-size: 33rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 83rpx;
				letter-spacing: 1px;
			}
		}
	}

	.fundPopup {
		z-index: 1000;
		/deep/ .uni-popup__wrapper {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			text-align: center;
			// padding: 45rpx 0;
			border-radius: 18rpx 18rpx 0 0;
		}
		.InfoPopup{
			width: 100%;
			border-radius: 30rpx;
			// margin-bottom: -80rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				padding-top: 30rpx;
				.topCon{
					display: flex;
					justify-content: space-between;
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 32rpx;
						color: #000000;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
				.fundcontent{
					margin-top: 50rpx;
					.walletitem{
						height: 90rpx;
						width: 80%;
						margin: 0 auto;
						line-height: 90rpx;
						border-radius: 15rpx;
						border: 1rpx solid #e7e7e7;
						margin-bottom: 20rpx;
						.walletdetail{
							
						}
					}
				}
			}
		}		
	}
	
	
	.languagePopup{
		width: 580rpx;
		min-height: 520rpx;
		border-radius: 30rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			padding-top: 30rpx;
			.topCon{
				display: flex;
				justify-content: space-between;
				margin-bottom: 60rpx;
				.title{
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #000000;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
			.radiocontent{
				width: 90%;
				height: 240rpx;
				margin: 0 auto;
			}
			.confirmbtn{
				width: 80%;
				margin: 0 auto;
				margin-top: 30rpx;
			}
		}
	}
	
	.remarksinfo{
		width: 100%;
		min-height: 300rpx;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				display: flex;
				justify-content: space-between;
				height: 80rpx;
				line-height: 80rpx;
				// border-bottom: 1rpx solid #ccc;
				.delect{
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #fff;
					font-style: normal;
					text-transform: none;
				}
				.Close{
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #999999;
					font-style: normal;
					text-transform: none;
				}
				.title{
					font-family: PingFang SC, PingFang SC;
					font-size: 28rpx;
					color: #bbbbbb;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
				.Confirm{
					color: #2A3A88FF;
					font-size: 28rpx;
				}
			}
			
			.Ccontent{
				min-height: 200rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				padding-bottom: 50rpx;
				width: 100%;
				font-size: 32rpx;
				.sdtx{
					width: 100%;
					height: 90rpx;
					line-height: 90rpx;
					border-bottom: 1rpx solid #f0f0f0;
					text-align: center;
					text{
						letter-spacing: 3rpx;
						font-weight: 500;
						color: #333;
						// font-size: 36rpx;
					}
				}
				.khtx{
					width: 100%;
					height: 90rpx;
					line-height: 90rpx;
					border-bottom: 1rpx solid #f0f0f0;
					text-align: center;
					text{
						letter-spacing: 3rpx;
						font-weight: 500;
						// color: #0055ff;
						// font-size: 36rpx;
					}
				}
			}
		}
	}
	.sz{
		width: 35rpx;
		height: 35rpx;
	}
</style>
