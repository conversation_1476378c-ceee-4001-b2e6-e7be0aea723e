<template>
	<view class="theme_view">
		<component-popup :propShow="propShow" propPosition="bottom" @onclose="popup_close_event">
			<view class="flex-row jc-sb align-c padding-main">
				<text class="cr-grey" @tap="popup_close_event">取消</text>
				<text class="cr-blue" @tap="sub_ragion_event">确认</text>
			</view>
			<view class="g-dp-ctt-wrapper">
				<picker-view style="width: 32%; height: 240px" :indicator-style="indicatorStyle"
					:value="columns_index[0]" data-column="0" @change="changeHandler">
					<picker-view-column>
						<view class="g-dp-ctt-wp-item" v-for="(item, a) in columns[0]" :key="item.id">{{ item.name }}
						</view>
					</picker-view-column>
				</picker-view>

				<picker-view style="width: 32%; height: 240px" :indicator-style="indicatorStyle"
					:value="columns_index[1]" data-column="1" @change="changeHandler">
					<picker-view-column>
						<view class="g-dp-ctt-wp-item" v-for="(item, b) in columns[1]" :key="item.id">{{ item.name }}
						</view>
					</picker-view-column>
				</picker-view>

				<picker-view style="width: 32%; height: 240px" :indicator-style="indicatorStyle"
					:value="columns_index[2]" data-column="2" @change="changeHandler">
					<picker-view-column>
						<view class="g-dp-ctt-wp-item" v-for="(item, c) in columns[2]" :key="item.id">{{ item.name }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</component-popup>
	</view>
</template>

<script>
	const app = getApp();
	import componentPopup from "@/components/popup/popup";
	export default {
		name: "region-chioce",
		components: {
			componentPopup
		},
		props: {
			propShow: {
				type: Boolean,
				default: false,
			},
			propProvinceId: {
				type: String,
				default: "",
			},
			propCityId: {
				type: String,
				default: "",
			},
			propCountyId: {
				type: String,
				default: "",
			},
		},
		data() {
			return {
				columns: [
					[0],
					[0],
					[0]
				],
				// 下标
				columns_index: [
					[0],
					[0],
					[0]
				],
				indicatorStyle: `height: ${uni.upx2px(88)}px;`,
			};
		},
		methods: {
			// 地区初始化匹配索引
			get_region_value(index, id) {
				var data = this.columns[index];
				var data_id = id;
				var list_index = [];
				data.forEach((d, i) => {
					if (d.id == data_id) {
						list_index = [i];
						return false;
					}
				});
				this.$set(this.columns_index, index, list_index);
			},
			// picker 滚动change事件
			changeHandler(e) {
				const {
					dataset,
					value
				} = e.target;
				if (dataset.column == 0) {
					if (this.columns[0][value[0]].adcode) {
						this.$set(this.columns_index, dataset.column, value);
						this.$set(this.columns_index, 1, [0]);
						this.$set(this.columns_index, 2, [0]);
						this.getCity(this.columns[0][value[0]].adcode, true);
					}
				} else if (dataset.column == 1) {
					if (this.columns[1][value[0]].adcode) {
						this.$set(this.columns_index, dataset.column, value);
						this.$set(this.columns_index, 2, [0]);
						this.getArea(this.columns[1][value[0]].adcode, true);
					}
				} else if (dataset.column == 2) {
					this.$set(this.columns_index, dataset.column, value);
				}
			},

			// 获取省
			getProvince() {
				uni.request({
					url: "http://datavmap-public.oss-cn-hangzhou.aliyuncs.com/areas/csv/100000_province.json",
					method: 'GET',
					dataType: "json",
					success:(res)=> {
						if (res.statusCode == 200) {
							var data = res.data.rows
							this.$set(this.columns, 0, data);
							this.getCity(this.propProvinceId ? this.propProvinceId : data[0].adcode)
							if (this.propProvinceId) {
								this.get_region_value(0, this.propProvinceId);
							}
						} else {
							uni.showToast({
								title: res.errMsg,
								icon: "none"
							})
						}
					}
				})
			},
			// 获取市
			getCity(province_id, init = false) {
				let that = this
				if (province_id) {
					uni.request({
						url: `http://datavmap-public.oss-cn-hangzhou.aliyuncs.com/areas/csv/${province_id}_city.json`,
						method: "GET",
						dataType: "json",
						success: (res) => {
							var data = res.data.rows;
							this.$set(this.columns, 1, data);
							if (init) {
								this.getArea(data[0].adcode);
							} else {
								this.getArea(this.propCityId ? this.propCityId : data[0].adcode);
								if (this.propCityId) {
									this.get_region_value(1, this.propCityId);
								}
							}
						},
						fail: () => {
							app.globalData.showToast("城市获取失败");
						},
					});
				}
			},
			// 获取区
			getArea(city_id, init = false) {
				if (city_id) {
					// 加载loding
					uni.request({
						url: `https://datavmap-public.oss-cn-hangzhou.aliyuncs.com/areas/csv/${city_id}_district.json`,
						url: `https://geo.datav.aliyun.com/areas/bound/${city_id}_full.json`,
						method: "GET",
						dataType: "json",
						success: (res) => {
							var data = res.data.features;
							var result = []
							data.map(item => {
								result.push(item.properties)
							})
							this.$set(this.columns, 2, result);
							console.log(this.columns)
							if (!init) {
								if (this.propCountyId) {
									this.get_region_value(2, this.propCountyId);
								}
							}
						},
						fail: () => {
							app.globalData.showToast("区/县获取失败");
						},
					});
				}
			},
			// 关闭按钮
			popup_close_event(e) {
				this.$emit("onclose", false);
			},
			//提交按钮
			sub_ragion_event(e) {
				let province = this.columns[0][this.columns_index[0]];
				let city = this.columns[1][this.columns_index[1]];
				let areal = this.columns[2][this.columns_index[2]];
				this.popup_close_event();
				this.$emit("call-back", province, city, areal);
			},
		},

		watch: {
			propShow(new_val, old_val) {
				if (new_val) {
					this.getProvince();
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	::v-deep .popup-bottom {
		border-radius: 0;
	}

	.picker-view-column {
		height: 480rpx !important;
	}

	.g-dp-ctt-wrapper {
		height: 480upx;
		width: 100%;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.g-dp-ctt-wp-item {
		width: 100%;
		height: 88upx;
		line-height: 88upx;
		text-align: center;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: 30upx;
	}
</style>