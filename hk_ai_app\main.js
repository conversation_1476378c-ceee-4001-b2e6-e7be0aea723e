import Vue from 'vue';
import App from './App';
import util from './common/utils'
// 全局mixins
import base from './common/js/common/base';
import share from './common/js/common/share';
import http from './common/http/http.js'
import store from './store'

import iconfont from './components/iconfont/iconfont.vue';
// import showLoading from "./components/loading/loading.vue"
import refreshLoading from "./components/refresh-loading/refresh-loading.vue"
import uView from '@/uni_modules/uview-ui'
import ZPMixin from '@/uni_modules/z-paging/components/z-paging/js/z-paging-mixin'

Vue.mixin(ZPMixin)
Vue.mixin(base);
Vue.mixin(share);
// Vue.mixin(http);
Vue.prototype.$HTTP = http;
Vue.prototype.$store = store
Vue.use(uView)
// uni.$u.config.unit = "rpx"

Vue.config.productionTip = false;
// 注册到全局
Vue.component("iconfont", iconfont);
// Vue.component("showLoading", showLoading)
Vue.component("refreLoading", refreshLoading)
App.mpType = 'app';
util.install(Vue)
const app = new Vue({
	store,
	...App
});
app.$mount();