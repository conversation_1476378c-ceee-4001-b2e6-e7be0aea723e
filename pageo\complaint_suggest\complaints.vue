<template>
	<view class="page">
		<newNavBar :title="title" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
				<u-form-item :label="$t('accountUser.wtms')" prop="title" borderBottom labelWidth="300rpx">
					<u--textarea v-model="formData.title" :placeholder="$t('accountUser.xxsm')"
					  count  border="none" height="200" maxlength="200"></u--textarea>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.tpbc')" borderBottom labelWidth="300rpx" prop="transactionUrl">
					<view class="imgupload">
						<view class="imgList">
							<view class="imgitem" v-for="item in formData.urlList" :key="item">
								<view class="imgdetail">
									<image :src="item" mode="" style="width: 120rpx;height: 120rpx;border-radius: 10rpx;" @tap="onTapimage(item)"></image>
									<image @tap="delimg(item)" class="imgicon" src="https://file.36sm.cn/xtjyjt/images/common/delicon.png" mode="" style="width: 25rpx; height: 25rpx;"></image>
								</view>
							</view>
						</view>
						<view @tap="upload_img" style="min-width: 120rpx;height: 120rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;background-color: #f9f9f9;border-radius: 10rpx;" >
							<image style="width: 120rpx;height: 120rpx;"
							 src="https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png"></image>
							 <!-- <text style="font-size: 18rpx;margin-top: 5rpx;">{{$t('accountUser.tpbc')}}</text> -->
						</view>
					</view>
				</u-form-item>
				
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right, #FF6900 , #FF6900)" @click="submit" :loading="loading"></u-button>
			
		</view>
	</view>
</template>

<script>
	const app = getApp();
	
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	
	export default {
		data() {
			return {
				type:0,
				loading:false,
				// 金额单位
				coinName: "",
				// 充值地址二维码
				czqrImg: "",
				formData: {
					title: '',
					urlList: [
						// "https://img.keaitupian.cn/uploads/2020/12/21/****************.jpg",
						// "https://c-ssl.duitang.com/uploads/blog/202204/17/20220417194312_65b3f.jpg"
						
					]
				},
				title: this.$t('accountUser.jy'),
				rules: {
					title: {
						required: true,
						// message: '请输入问题描述(反馈建议)',
						message: this.$t('message.wtms'),
						trigger: ['blur']
					}
				}
			}
		},
		onLoad(data) {
			
		},
		onReady() {
		},
		created() {
		},
		methods: {
			delimg(item){
				const index = this.formData.urlList.indexOf(item);
				  if (index !== -1) {
					this.formData.urlList.splice(index, 1);
				  }
			},
			upload_img() {
				// 限制最多只能上传4张图片
				if(this.formData.urlList.length > 3) {
						uni.showToast({
							// title: "最多上传4张图片!",
							title: this.$t('message.zdsc'),
							icon: 'none'
						})
					return
				}
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							// url: 'http://************/khrWeb/oss/hteditor/upload',
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								this.formData.urlList.push(Data.data.data.fileUrl)
								console.log(this.formData.urlList, '上传的图片数组')
								if (Data.code == 200) {
									uni.showToast({
										// title: "上传成功",
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			onTapimage(src) {
				console.log(src, 'src')
				 uni.previewImage({
				        urls: [src],
						current: src
				      });
			
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						console.log(this.formData, '提交参数')
						if(this.formData.urlList.length == 0) {
							uni.showToast({
								// title: "请至少上传一张图片!",
								title: this.$t('message.zsxzyz'),
								icon: 'none'
							})
							return
						}
						this.loading = true
						setTimeout(() => {
							this.loading = false
						},2000)
						// let that = this
						api({
							url: `mb/feedback/save`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: this.formData
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									// title: '建议提交成功',
									title: this.$t('message.czcg'),
									icon:'none'
								})
								setTimeout(() => {
									this.$back()
								}, 500)
							}
						})
						
					}
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.amount = temp
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.imgupload{
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		.imgList{
			display: flex;
			align-items: center;
			.imgitem{
				margin-right: 30rpx;
				.imgdetail{
					position: relative;
					.imgicon{
						position: absolute;
						right: -10rpx;
						top: -10rpx;
					}
				}
			}
		}
	}
</style>