<template>
	<view class="page">
		<view class="header" :style="{ 'padding-top': navBarHeight + 'px' }">
			<view class="navbar">
				<view class="toptitle">
					<view class="imgView" @click="goBack">
						<image src="https://file.36sm.cn/xtjyjt/images/common/bback.png" mode=""></image>
					</view>
					<text>{{title}}</text>
				</view>
				<view class="search">
					<u-search placeholder="请输入搜索内容" :showAction="false" height="34" v-model="Params.propName" bgColor="#fff"
					@search="searchPlace" @clear="clearPlace"></u-search>
				</view>
				<view class="title"> </view>
			</view>
			
			<!-- <view class="toptab">
				<view class="paixu">
					<text>
						排序
					</text>
					<view class="updown">
						<u-icon name="arrow-up-fill" size="16rpx" color="#ccc"></u-icon>
						<u-icon name="arrow-down-fill" size="16rpx" color="#ccc"></u-icon>
					</view>
					
				</view>
				<view class="quyu">
					<text>
						区域
					</text>
					<view class="updown">
						<u-icon name="arrow-down-fill" size="18rpx" color="#ccc"></u-icon>
					</view>
				</view>
				<view class="dengji">
					<text>
						等级
					</text>
					<view class="updown">
						<u-icon name="arrow-down-fill" size="18rpx" color="#ccc"></u-icon>
					</view>
				</view>
				<view class="qktj">
					<view class="trash">
						<u-icon name="trash" size="30rpx" color="#ccc"></u-icon>
					</view>
					<text>
						清空条件
					</text>
				</view>
			</view> -->
		</view>
		<view class="classifylist">
			<view class="cate-list" v-if="visible">
				<view class="cate-left">
					<view class="cate-item" v-for="(item,index) in oneList" :key="index"
						:class="activeIndex == index ? 'active' : ''" @click="changeActive(item, index)">{{item.name}}
					</view>
				</view>

				<scroll-view class="scroll-container" scroll-y="true"  @scrolltolower="onscrollBottom">
					<view class="cate-right" v-if="twoList.length > 0">
						<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
						:use-page-scroll="true" :show-refresher-when-reload="true" :auto-show-back-top-top="true" :auto="false">
								<refresh-loading slot="refresher"></refresh-loading>
								<view class="twolist">
									<view class="listlist">
										<view class="twodetail" v-for="(item, index) in twoList" :key="index"
											@click="navToDetail(item)">
											<view class="img">
												<image :src="item.coverImageUrl" mode="aspectFill" style="width: 150rpx; height: 150rpx;margin-right: 20rpx;border-radius: 15rpx;"></image>
											</view>
											<view class="listItem">
												<view class="twotitle">
												    <u--text :lines="1" :text="item.propName" size="28rpx" color="#000" bold></u--text>
												</view>
												<view class="schemeRemarks">
													{{item.introduce}}
												</view>
											</view>
										</view>
									</view>
									<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
									<component-bottom-line :propStatus="bottom_line_status" propMsg="没有更多了" v-if="!loadingStatus && twoList.length > 0"></component-bottom-line>
								</view>
						</z-paging>
					</view>
					<view class="no-list" v-if="twoList.length == 0">
						<view class="noData">
							<image src="https://file.36sm.cn/xtjyjt/images/common/goods_null.png" mode=""
								style="width: 200rpx;height: 200rpx;"></image>
							<view class="name">
								暂无道具～
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			<cc-gifLoading v-else
				gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
		</view>
	</view>
</template>
<script>
	import TabbarCom from '../../components/tabbar/tabbar.vue'
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default {
		components: {
			TabbarCom,
			componentBottomLine,
			componentBottomLoading
		},
		data() {
			return {
				 // 状态栏高度
				navBarHeight: 0,
				visible: false,
				activeIndex: 0,
				// 一级类目
				parentCodeIdList: ['1864239605310558208'], //请求父类id
				oneList: [],
				// 二级类目
				schemeNames: '',
				twoList: [],
				isPromise: true,
				Params: {
					page: 1,
					size: 10,
					propName: ""
				},
				loadingStatus: true,
				bottom_line_status: false,
				keyword: "",
				title: ""
			}
		},
		onShow() {
			// this.visible = false
			// this.Params.page = 1
			// this.twoList = []
			// this.schemeList()
		},
		onLoad(params) {
			console.log(params, '路由参数')
			if(params.title) {
				this.title = params.title
			}
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)

			this.Params.page = 1
			this.twoList = []
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			// 获取二级类目
			this.schemeList()
			//获取二级类目下的数据

		},
		methods: {
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				})
			},
			onReachBottom() {
				console.log('上拉触底')
				
				this.loadingStatus = true
				this.Params.page ++
				this.schemeListData()
			
				// this.$refs.bottom.get_video_data()
				
			},
			onscrollBottom() {
				console.log('上拉触底')
				
				this.loadingStatus = true
				this.Params.page ++
				this.schemeListData()
			},
			queryList() {
				console.log('下拉刷新')
				this.Params.page = 1
				this.Params.size = 10
				this.schemeListData()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			// 点击类目
			changeActive(item, index) {
				console.log(item, index);

				this.Params.page = 1
				this.twoList = []
				this.schemeName = item.name
				this.activeIndex = index
				console.log(this.schemeName);
				this.schemeListData()
			},
			searchPlace() {
				this.Params.page = 1
				this.twoList = []
				this.schemeListData()
			},
			clearPlace() {
				this.Params.page = 1
				this.twoList = []
				this.schemeListData()
			},
			schemeList() {
				//获取数据
				const dataList = {
					parentCodeIdList: this.parentCodeIdList
				}
				console.log(dataList, '数据格式');
				api({
					url: `tx/code/get/sub/list/map`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: dataList
				}).then(res => {
					console.log(res, '分类列表')
					if (res.code == 200) {
						console.log(res);
						const data = res.data.data
						this.oneList = data.A1864239605310558208
						console.log(this.oneList);
						this.schemeName = this.oneList[this.activeIndex].name
						console.log(this.schemeName);
						this.schemeListData()
					} else {
						uni.showToast({
							title: res.msg,  
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},

			// 获取二级类目下的数据
			schemeListData() {
				api({
					url: `tx/prop/get/list?page=${this.Params.page}&size=${this.Params.size}&typeInfo=${this.schemeName}&propName=${this.Params.propName}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data
						if(this.twoList.length == data.totalCount) {
							setTimeout(() => {
								this.loadingStatus = false;
								this.bottom_line_status = true
							}, 500)
						} else {
							if(this.Params.page == 1) {
								this.twoList = [...data.list]
								if(this.twoList.length == data.totalCount) {
									this.bottom_line_status = true
								}
							} else {
								this.twoList = [...this.twoList, ...data.list]
							}
							setTimeout(() => {
								this.loadingStatus = false;
							}, 500)
						}
						setTimeout(() => {
							this.visible = true
						}, 1200)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			navToDetail(item) {
				console.log(item, '道具资源详情', item.propId);
				uni.navigateTo({
					url: `/pageg/item_resources/itemdetails?propId=${item.propId}`
				})
			},

			//下拉刷新
			onPullDownRefresh() {
				console.log('上拉触底')
				this.Params.page = 1
				uni.stopPullDownRefresh();
				this.twoList = [];
				this.schemeListData()
			},

			loadMore() {
				console.log('下拉刷新');
				// if (this.visible) return; // 防止重复加载
				console.log(this.Params.page);
				if (this.isPromise) {
					this.Params.page; // 重置页码
					this.schemeListData(); // 触发刷新
				}

			}

		}
	}
</script>
<style lang="scss" scope>
	.page {
		background-color: #f5f5f5;
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}

.header {
		width: 100%;
		height: 80rpx;
		background-color: #f5f5f5;
		position: fixed;
		z-index: 999;

		.navbar {
			height: 80rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 10rpx;
			box-sizing: border-box;
			.toptitle {
				display: flex;
				min-width: 250rpx;
				font-size: 35rpx;
				font-weight: bold;
				color: #37458c;
				letter-spacing: 2rpx;
				.imgView{
					width: 20rpx;
					height: 30rpx;
					margin-right: 20rpx;
					image{
						width: 20rpx;
						height: 30rpx;
					}
				}
			}
			.title {
				text-align: center;
				width: 180rpx;
				font-size: 35rpx;
				font-weight: bold;
				color: #37458c;
				letter-spacing: 2rpx;
			}
			.search {
				width: 50%;
			}
		}
		
		.toptab{
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.paixu{
				width: 25%;
				display: flex;
				align-items: center;
				justify-content: center;
				.updown{
					margin-left: 10rpx;
				}
			}
			.quyu, .dengji{
				width: 25%;
				display: flex;
				align-items: center;
				justify-content: center;
				.updown{
					margin-left: 10rpx;
				}
			}
			.qktj{
				display: flex;
				width: 25%;
				align-items: center;
				justify-content: center;
				.trash{
					margin-right: 10rpx;
				}
			}
			.paixu:hover, .quyu:hover, .dengji:hover, .qktj:hover {
			    background-color: #ddd;
			}
		}
	}

.classifylist{
	.cate-list{
		margin-top: 180rpx;
		height: calc(100vh - 200rpx);
		overflow-y: auto;
		background-color: #fff;
		padding-top: 10rpx;
		display: flex;
		.cate-left{
			flex: 3;
			background-color: #fff;
			// height: 100%;
			.cate-item{
				width: 100%;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
				padding-left: 40rpx;
				box-sizing: border-box;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #777777;
				font-style: normal;
				text-transform: none;
			}
			.active {
				color: #2A3A88;
				border-left: 12rpx solid #2A3A88;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				font-style: normal;
				text-transform: none;
				background-color: #f5f5f5;
			}
		}
		.scroll-container{
			flex: 7;
			// background-color: skyblue;
			background-color: #f5f5f5;
			// overflow-y: auto;
			// height: 100%;
			height: calc(100vh - 215rpx);
			overflow-y: auto;
			color: #0d0d0d;
			.cate-right{
					.twolist{
						width: 100%;
						min-height: 100%;
						margin: 0 auto;
						margin-bottom: 10rpx;
						padding: 30rpx 20rpx 10rpx  20rpx;
						box-sizing: border-box;
					.listlist{
						.twodetail{
							display: flex;
							justify-content: flex-start;
							align-items: center;
							margin-bottom: 30rpx;
						
						}
						.listItem{
							height: 130rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							align-items: flex-start;
							.schemeRemarks {
								// background-color: skyblue;
								min-height: 60rpx;
								max-width: 320rpx;
								font-size: 12px;
								color: #959595;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								overflow: hidden;
								text-overflow: ellipsis;
								line-height: inherit;
								letter-spacing: 2rpx;
							}
						}
						.twotitle {
							height: 40rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 28rpx;
							color: #333333;
							line-height: 40rpx;
							font-style: normal;
							text-transform: none;
							margin-bottom: 6rpx;
						}
					}
						
					}
					
				}
				
				.no-list{
					height: calc(100vh - 285rpx);
					.noData {
						width: 100%;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						height: 100%;
						image {
							width: 150rpx;
							height: 150rpx;
						}
					
						.name {
							margin: 10rpx 0;
							color: #ccc;
							letter-spacing: 4rpx;
						}
					}
				}
		}
	}
}

</style>
