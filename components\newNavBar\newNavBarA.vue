<template>
	<view>
		<view class="top-status-bar" id="topStatusBar" style="height: 340rpx;">
			<view class="slot-view">
				<view class="topView status-content">
					<view class="bgc_img">
						<view class="imgView">
							<image src="https://file.36sm.cn/xtjyjt/images/common/topbgc.png" mode=""></image>
						</view>
						
						<view class="titleLeft">
							<text>见安足疗</text>
						</view>
					</view>
					<view class="TopSearch">
						<component-searcha propBrColor="#F5F5F5" @onsearch="search_input" :propIsOnEvent="true" @oninput="inputSearch" :propIsOnInputEvent="true"
								@onfocus="goSearch" :propIsOnFocusEvent="true" @onblur="" :propIsOnBlurEvent="true" :propIsRequired="false"
						        propPlaceholder="请输入搜索内容" propIconColor="#777777" propBgColor="#fff"></component-searcha>
					</view>
						<view class="newSlist">
							<view class="listitem" v-for="(item,index) in listNews" :key="key">
								<view class="detail">
									{{item}}
								</view>
							</view>
							<!-- <view class="left">
								<image src="https://file.36sm.cn/xtjyjt/images/common/tips.png" mode=""></image>
								<text class="text">消息: </text>
							</view>
							<view class="right">
								<swiper vertical autoplay interval="3000" duration="1000" circular>
										<swiper-item v-for="(item,index) in listNews" :key="index">
											<text>{{item.name}}</text>
										</swiper-item>
									</swiper>
							</view> -->
						</view>
				</view>
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import {
		getJyInfo,
		getOneList
	} from "@/common/request/api/home.js"
	import componentSearcha from '../../components/search/searchA';
	import api from '@/common/request/index.js'
	export default {
		name: "top-status-bar",
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			},
			isrefresh:{
				type:Boolean,
				default: false
			},
			width:{
				type: String,
				default: ''
			},
			iconLeft: {
				type: String,
				default: 'arrow-left'
			},
			iconRight: {
				type: String,
				default: ''
			},
			imageSrc:{
				type: String,
				default: ''
			},
			imageWidth:{
				type: String | Number,
				default: '55rpx'
			},
			imageHeight:{
				type: String | Number,
				default: '55rpx'
			},
			title:{
				type: String | Number,
				default: '首页'
			},
			title1:{
				type: String | Number,
				default: '定制'
			},
			iconSize:{
				type: String | Number,
				default: '50rpx'
			},
			textSize:{
				type: String | Number,
				default: '36rpx'
			},
			textColor:{
				type: String,
				default: '#3D3D3D'
			},
			iconColor:{
				type: String,
				default: '#3D3D3D'
			},
			showSlider:{
				type:Boolean,
				default: false
			},
		},
		data() {
			return {
				slotObserver: null,
				slotHeight: 0,
				systemInfo:uni.getStorageSync('cache_shop_system_info_key'),
				JyData: {},
				tabIndex: 1,
				// 消息列表
				listNews: [
					// { name: 'xxxxx下单成功！'},
					// { name: 'xxxx下单成功！'},
					// { name: 'xxx下单成功'},
					// { name: 'xx下单成功'},
					// { name: 'x下单成功'}
				]
			}
		},
		components: {
			componentSearcha,
		},
		computed: {
			...mapState({
				topStatusBarData: state => state.init.topStatusBarData
			}),
			statusBarHeight() {
				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight',h)
				return h
			},
			slotMarginTop() {
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				// #endif
				return sw
			},
			titleHeight(){
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			}
		},
		async created() {
			await this.getNews()
		},
		mounted() {
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
		},
		destroyed() {
			this.slotObserver?.disconnect()
			let data = {
					statusBarHeight: this.statusBarHeight,
					slotWidth: this.slotWidth,
					slotMarginTop: this.slotMarginTop,
					titleHeight:this.titleHeight,
					isLoad: true
				}
			this.setGlobalData(data)
		},
		methods: {
			...mapMutations(['setTopStatusBarData']),
			setGlobalData(data) {
				this.setTopStatusBarData(data)
			},
			search_input() {
				uni.navigateTo({
					url: "/pageo/search/search"
				})
			},
			inputSearch() {},
			getNews() {
				api({
					url: `bs/cj/jl/info?location=zh`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						const data = res.data.data
						console.log(data, 'data信息信息信息')
						this.listNews = data
					} else {
						uni.showToast({
							title: res.msg,
							icon:'none'
						})
					}
				})
			},
			goSearch() {
				console.log('去搜索')
				uni.navigateTo({
					url: "/pageo/search/search"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	view{
		box-sizing: border-box;
	}
	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		z-index: 999;
		background-color: #F4F4F4;
		.slot-view{
			width: 100%;
			.topView{
				width: 100%;
				.bgc_img{
					position: relative;
					width: 100%;
					.imgView{
						image{
							width: 100%;
							height: 340rpx;
							// mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(244, 244, 244, 1) 100%);
							// -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(244, 244, 244, 1) 100%); /* 兼容 Safari */
						}
					}
					.titleLeft{
						position: absolute;
						left: 20rpx;
						top: 110rpx;
						text{
							font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
							font-weight: bold;
							font-size: 40rpx;
							color: #FFFFFF;
							line-height: 47rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
							letter-spacing: 4rpx;
						}
					}
				}
				
				.TopSearch{
					width: 100%;
					left: 20rpx;
					top: 180rpx;
					position: absolute;
				}
				.newSlist {
				        position: absolute;
						top: 280rpx;
						left: 20rpx;
						display: flex;
						align-items: center;
						z-index: 2;
						width: 96%;
						height: 55rpx;
						line-height: 55rpx;
						margin: 0 auto;
						// background-color: #fff;
						border-radius: 20rpx;
						overflow-x: auto;
						white-space: nowrap;
						.listitem{
							background: #FFFFFF;
							  border-radius: 106rpx;
							  margin-right: 10rpx; /* 添加一些间距 */
							  flex-shrink: 0; /* 防止缩小，保持宽度 */
							  display: inline-block;
							  padding: 0 20rpx;
							  color: #777777FF;
						}



						.left {
							display: flex;
							align-items: center;
							image{
								width: 45rpx;
								height: 45rpx;
								margin:0 10rpx;
							}
							.text {
								font-family: Source Han Sans SC, Source Han Sans SC;
								font-weight: 400;
								font-size: 24rpx;
								font-style: normal;
								text-transform: none;
						        margin-right: 20rpx;
								color: #a7a7a7;
								}
						}
						.right{
							flex: 1;
							height: 100%;
							swiper{
									height:100%;
									&-item{
										height: 100%;
										font-size: 30rpx;
										color: #a7a7a7;
										overflow: hidden;
										white-space: nowrap;
										text-overflow: ellipsis;
									}
								}
						}
				}
				
			}
		}
	}
</style>
