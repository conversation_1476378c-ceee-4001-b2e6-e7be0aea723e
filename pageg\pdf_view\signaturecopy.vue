<template>
	<view class="container">
		<view class="sign-box">
			<canvas class="mycanvas" :style="{width:width +'px',height:height +'px'}" canvas-id="mycanvas"
				@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>
			<canvas canvas-id="camCacnvs" :style="{width:height +'px',height:width +'px'}" class="canvsborder"></canvas>
		</view>
		<view class="sigh-btns">
			<view class="btn" @tap="handleCancel">取消</view>
			<view class="btn" @tap="handleReset">重写</view>
			<view class="btn" @tap="handleConfirm">确认</view>
		</view>
	</view>

</template>
<script>
	var x = 20;
	var y = 20;
	var tempPoint = []; //用来存放当前画纸上的轨迹点
	var id = 0;
	var type = '';
	let that;
	let canvasw;
	let canvash;
	export default {
		data() {
			return {
				ctx: '', //绘图图像
				points: [], //路径点集合,
				width: 0,
				height: 0
			};
		},
		mounted() {},

		onLoad(option) {
			that = this;
			console.log(option);
			id = option.id;
			type = option.type;
			this.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象
			//设置画笔样式
			this.ctx.lineWidth = 4;
			this.ctx.lineCap = 'round';
			this.ctx.lineJoin = 'round';

			uni.getSystemInfo({
				success: function(res) {
					console.log(res);
					that.width = res.windowWidth;
					that.height = res.windowHeight * 0.85;
				}
			});
		},

		methods: {
			//触摸开始，获取到起点
			touchstart: function(e) {
				let startX = e.changedTouches[0].x;
				let startY = e.changedTouches[0].y;
				let startPoint = {
					X: startX,
					Y: startY
				};

				/* **************************************************
				    #由于uni对canvas的实现有所不同，这里需要把起点存起来
				 * **************************************************/
				this.points.push(startPoint);

				//每次触摸开始，开启新的路径
				this.ctx.beginPath();
			},

			//触摸移动，获取到路径点
			touchmove: function(e) {
				let moveX = e.changedTouches[0].x;
				let moveY = e.changedTouches[0].y;
				let movePoint = {
					X: moveX,
					Y: moveY
				};
				this.points.push(movePoint); //存点
				let len = this.points.length;
				if (len >= 2) {
					this.draw(); //绘制路径
				}
				tempPoint.push(movePoint);
			},

			// 触摸结束，将未绘制的点清空防止对后续路径产生干扰
			touchend: function() {
				this.points = [];
			},

			/* ***********************************************	
			#   绘制笔迹
			#   1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
			#   2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
			#   3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
			************************************************ */
			draw: function() {
				let point1 = this.points[0];
				let point2 = this.points[1];
				this.points.shift();
				this.ctx.moveTo(point1.X, point1.Y);
				this.ctx.lineTo(point2.X, point2.Y);
				this.ctx.stroke();
				this.ctx.draw(true);
			},

			handleCancel() {
				uni.navigateBack({
					delta: 1
				});
			},

			//清空画布
			handleReset: function() {
				console.log('handleReset');
				that.ctx.clearRect(0, 0, that.width, that.height);
				that.ctx.draw(true);
				tempPoint = [];
			},

			//将签名笔迹上传到服务器，并将返回来的地址存到本地
			handleConfirm: function() {
				if (tempPoint.length == 0) {
					uni.showToast({
						title: '请先签名',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				uni.showLoading({
					title: '签名图片生成中，请耐心等候'
				})
				uni.canvasToTempFilePath({
					canvasId: 'mycanvas',
					success: function(res) {
						let tempPath = res.tempFilePath;

						const ctx = uni.createCanvasContext('camCacnvs', that);
						ctx.translate(0, that.width);
						ctx.rotate((-90 * Math.PI) / 180);
						ctx.drawImage(tempPath, 0, 0, that.width, that.height);
						ctx.draw();
						setTimeout(() => {
							//保存签名图片到本地
							uni.canvasToTempFilePath({
								canvasId: 'camCacnvs',
								success: function(res) {
									//这是签名图片文件的本地临时地址
									let path = res.tempFilePath;
									console.log(path, '生成的图片资源')
									uni.uploadFile({
										url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
										filePath: path,
										name: "multipartFile",
										header: {
											"jarepair-platform": uni
												.getStorageSync("token")
										},
										success: (res) => {
											const data = JSON.parse(res
												.data)
											console.log(data, '上传接口返回的数据')
											uni.showToast({
												title: '签名生成成功',
												icon: 'none'
											});
										}
									})
									uni.hideLoading()
								},
								fail: err => {
									console.log('fail', err);
									uni.hideLoading()
								}
							}, );
						}, 200);
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;
		height: 100%;
	}

	.sign-box {
		background-color: skyblue;
		width: 80%;
		height: 80%;
		// margin: auto;
		// display: flex;
		// flex-direction: column;
		// text-align: center;
	}

	.sign-view {
		height: 100%;
	}

	.sigh-btns {
		// background-color: skyblue;
		display: flex;
		justify-content: space-around;
		margin-top: 150rpx;

		.btn {
			// margin: auto;
			text-align: center;
			padding: 10rpx 25rpx;
			border-radius: 10rpx;
			// transform: rotate(90deg);
			border: 1rpx solid #ccc;
		}
	}

	.mycanvas {
		width: 100%;
		margin: auto 0rpx;
		background-color: #ececec;
	}

	.canvsborder {
		border: 1rpx solid #333;
		position: fixed;
		top: 0;
		left: 10000rpx;
	}
</style>