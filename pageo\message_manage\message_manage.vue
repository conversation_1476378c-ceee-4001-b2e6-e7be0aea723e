<template>
	<view>
		<newNavBar :title="$t('search.xxgl')" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<view v-if="visible">
			<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="false"
				:use-page-scroll="true" :auto="false">
				<refresh-loading slot="refresher"></refresh-loading>
				
				<view class="message" v-if="messageList.length > 0">
					<view class="messageList" v-for="(item,index) in messageList" :key="item.mxId">
						<u-swipe-action >
						    <u-swipe-action-item ref="SwipeAction" :options="messageOptions" @click="delMessage(item)">
						     <view class="messagedetail" @tap="MessageDetail(item)">
						     	<view class="left" v-if="item.systemMessageDTO.thumbnailImageUrl">
									<image :src="item.systemMessageDTO.thumbnailImageUrl" mode="" style="width: 100rpx;height: 100rpx;border-radius: 50%;"></image>
						     	</view>
						     	<view class="right">
						     		<view class="topright">
						     			<view class="title">
						     				{{item.systemMessageDTO.introduction}}
						     			</view>
										<view class="time">
											{{changeTime(item.systemMessageDTO.createTime)}}
										</view>
						     		</view>
						     		<view class="bottomright">
						     			<view class="content">
						     				<!-- <text>{{item.systemMessageDTO.labelTitle1}}</text> -->
										<!-- 	<u--text :lines="1" text="系统回复系统回复系统回复系统回复系统回复系统回复系统回复系统回复"
											size="28rpx" color="#999999"></u--text> -->
											<u--text :lines="1" :text="item.systemMessageDTO.labelTitle1"
											size="28rpx" color="#999999"></u--text>
						     			</view>
										<!-- 未读状态 -->
										<view class="ischeck" v-if="item.checkStatus == 0">
											<text>1</text>
										</view>
						     		</view>
						     	</view>
						     </view>
						    </u-swipe-action-item>
						</u-swipe-action>
					</view>
				</view>
				<view v-else>
					<no-data :propStatus="propStatus"></no-data>
				</view>

				<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line :propMsg="$t('accountUser.mysjl')" v-else></component-bottom-line>
				
			</z-paging>
		</view>
		
		
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
  import store from '@/store'
  import api from '@/common/request/index.js'
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
  
	export default{
		data(){
			return {
				pageNum:1,
				messageList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				type:0,
				params: {
					page: 1,
					size: 15
				},
				// 下拉触底加载效果
				loadingStatus: 	true,
				messageOptions: [{
					text: this.$t('search.sc')
				}],
				visible: false
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad() {
			console.log('onLoad')
			this.getMessage()
			
			setTimeout(() => {
				this.$data.visible = true
			}, 2000)
		},
		onReachBottom() {
			console.log('下拉触底')
			this.loadingStatus = true
			this.params.page ++
			this.getMessage()
		},
		onShow() {
			console.log('onShow')
			this.messageList = []
			this.getMessage()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods:{
			delMessage(item) {
				console.log(item, '删除删除')
				api({
					url: `mb/message/rm/details/${item.mxId}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
				    if (res.code === 200) {
						uni.showToast({
						    // title: '删除成功',
							title: this.$t('message.SCcg'),
						    icon: "none"
						});
						this.params.page = 1
						this.params.size = 15
						this.messageList = []
						this.getMessage()
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
				
			},
			changeTime(createTime) {
				const now = new Date();
				const time = new Date(createTime);
				
				// Check if it's today
				if (time.toDateString() === now.toDateString()) {
					// If it's today, show hours and minutes
					return `${time.getHours()}:${time.getMinutes().toString().padStart(2, '0')}`;
				}
				
				// Check if it's yesterday
				const yesterday = new Date(now);
				yesterday.setDate(now.getDate() - 1);
				
				if (time.toDateString() === yesterday.toDateString()) {
					// return "昨天";
					return this.$t('message.zt');
				}
				
				// If it's older than yesterday, show full date in "YYYY-MM-DD" format
				const year = time.getFullYear();
				const month = (time.getMonth() + 1).toString().padStart(2, '0');
				const day = time.getDate().toString().padStart(2, '0');
				
				return `${year}-${month}-${day}`;
			},
			queryList() {
				console.log('上拉刷新')
				this.params.page = 1
				this.params.size = 15
				this.getMessage()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			MessageDetail(item) {
				console.log('消息详情', item)
				uni.navigateTo({
					url: `/pageo/message_manage/message_detail?mxId=${item.mxId}&checkStatus=${item.checkStatus}&messageId=${item.messageId}`
				})
			},
			getMessage(){
				api({
					url: `mb/message/get/page/list?location=${this.location}&page=${this.params.page}&size=${this.params.size}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '消息列表')
						if(res.data.data.list.length > 0){
							if(this.messageList.length == res.data.data.totalCount) {
								// 已获取到全部数据
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							} else {
								if(this.params.page == 1) {
									this.messageList = [...res.data.data.list]
								} else {
								   this.messageList = [...this.messageList, ...res.data.data.list]
								}
								console.log(this.messageList, '消息列表')
								this.isShow = false
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							}
						}else{
							this.propStatus = 0
							this.loadingStatus = false
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.message{
		.messageList{
			.messagedetail{
				height: 150rpx;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.left{
					padding-left: 40rpx;
				}
				.right{
					padding-right: 40rpx;
					margin-left: 30rpx;
					width: 80%;
					.topright{
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 15rpx;
						.title{
							color: #000;
							font-weight: 600;
							font-size: 30rpx;
						}
						.time{
							color: #999999;
						}
						
					}
					.bottomright{
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.content{
							width: 80%;
						}
						.ischeck{
							width: 40rpx;
							height: 40rpx;
						    font-size: 26rpx;
							line-height: 40rpx;
							text-align: center;
							color: #fff;
							border-radius: 50%;
							background-color: #ff493c;
						}
					}
				}
			}
		}
	}
	
</style>