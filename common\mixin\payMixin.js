import {
	getShoppingOrderTransaction,
	postDerivativeOrderPayOrder,
} from '@/common/request/api/goods.js'

export default {
	data() {
		return {

		}
	},
	methods: {
		requestPay({
			orderId,
			payType,
			sumTo,
			goodsType,
			orderToken
		}) {
			if (payType == 5) return this.$toastApp('功能开发中~')
			// #ifndef MP-WEIXIN
			if (payType == 3) return this.$toastApp('当前仅限微信小程序')
			// #endif
			let data = {
				orderId,
				payType,
				orderToken:orderToken ? orderToken : 1
			}
			uni.showLoading({
				title: '正在支付~',
				mask: true
			})
			let fn = goodsType == 1 ? getShoppingOrderTransaction : postDerivativeOrderPayOrder
			fn(data).then(res1 => {
				uni.hideLoading()
				let data = {
					orderId: orderId,
					payType: payType,
					totalPrice: sumTo,
					type: 1,
					goodsType: goodsType
				}
				if (res1.code == 200) {
					switch (payType) {
						case 1:
							this.$goRedirectTo('/pageo/paytips/paytips', data)
							break;
						case 2:
							this.$goRedirectTo('/pageo/paytips/paytips', data)
							break;
						case 3:
						  // #ifdef MP-WEIXIN
						  uni.requestPayment({
						  	provider: 'wxpay',
						  	timeStamp: res1.data.data.timeStamp,
						  	nonceStr: res1.data.data.nonceStr,
						  	package: res1.data.data.packageValue,
						  	signType: res1.data.data.signType,
						  	paySign: res1.data.data.paySign,
						  	success: function(res) {
						  		console.log('success:' + JSON.stringify(res));
						  		this.$goRedirectTo('/pageo/paytips/paytips', data)
						  	},
						  	fail: function(err) {
						  		console.log('fail:' + JSON.stringify(err));
						  	}
						  });
						  // #endif
							break;
						case 4:
							// 支付宝沙箱环境
							// // #ifdef APP
							// let EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
							// EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
							// // #endif
							uni.getProvider({
								service: 'payment',
								success: (res) => {
									if (~res.provider.indexOf('alipay')) {
										uni.requestPayment({
											"provider": "alipay", //固定值为"alipay"
											"orderInfo": res1.data.data, //此处为服务器返回的订单信息字符串
											success: (res) => {
												console.log("支付成功", res);
												this.$goRedirectTo('/pageo/paytips/paytips', data)
											},
											fail: function(err) {
												console.log('支付失败', err);
											}
										});
									}
								}
							});
							break;
						case 3:
							break;
					}
				} else {
					data.type = 2
					data.msg = res.msg
					this.$goRedirectTo('/pageo/paytips/paytips', data)
				}
			})
		}
	}
}