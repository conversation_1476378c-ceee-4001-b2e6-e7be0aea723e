<template>
	<view class="page">
		<view class="header" :style="{ 'padding-top': navBarHeight + 'px' }">
			<view class="navbar">
				<view class="imgView" @click="goBack">
					<image src="https://file.36sm.cn/mttttxxs/2025/02/12/db353050fb7c4f0e94721b6fe9a28b72.png" mode="">
					</image>
				</view>
				<view class="title">
					<text>所有技师</text>
				</view>
				<view class="title" style=" width: 120rpx;"> </view>
			</view>
		</view>
		<view class="classifylist">
			<view class="cate-list" v-if="visible">
				<view class="cate-left">
					<view class="cate-item" v-for="(item,i) in oneList" :key="i"
						:class="activeIndex == i ? 'active' : ''" @click="changeActive(item, i)">
						<view class="cate-detail" :class="activeIndex == i ? 'isactive' : ''">
							<image v-if="activeIndex == i"
								src="https://file.36sm.cn/jaxinfo/2025/02/13/82b3f98ee38146feb61af9269df0aa8e.png"
								mode="" style="width: 18rpx;height: 18rpx;margin-right: 10rpx;"></image>
							<text>{{item.name}}</text>
						</view>
					</view>
				</view>

				<view class="twoRightlist">
					<scroll-view class="scroll-container" scroll-y="true" @scrolltolower="onscrollBottom">
						<view class="cate-right" v-if="twoList.length > 0">
							<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
								:use-page-scroll="true" :show-refresher-when-reload="true"
								:auto-show-back-top-top="true" :auto="false">
								<refresh-loading slot="refresher"></refresh-loading>
								<view class="twolist">
									<view class="listlist">
										<view class="twoitem" v-for="(item, index) in twoList" :key="index">
											<view class="twodetail">
												<view class="topC" @click="ToCoash(item)">
													<view class="img">
														<image :src="item.coverImageUrl" mode="aspectFill"
															style="width: 84rpx; height: 84rpx;border-radius: 8rpx;">
														</image>
													</view>
													<view class="right">
														<view class="topN">
															<view class="name">
																<text>{{item.coachName}}</text><text>({{item.starLevel}})</text>
																<image src="../../static/images/notice.png" mode=""
																	style="width: 22rpx;height: 24rpx;">
																</image>
															</view>
															<view class="righticon">
																<image src="../../static/images/righticon.png" mode=""
																	style="width: 24rpx;height: 24rpx;">
																</image>
															</view>
														</view>
														<view class="bottom">
															<text>一级技师，擅长XXXX</text>
														</view>
													</view>
												</view>
												<view class="project">
													<view class="projectitem" v-for="(items, i) in item.pjInfoList"
														:key="i">
														<view class="projectdetail">
															<view class="topPro">
																<view class="lefttitle" @click="showProject(items)">
																	<text>{{items.projectName}}</text>
																</view>
																<view class="rightStatus">
																	<text class="yystatus">{{items.yyStatusName}}</text>
																	<text v-if="items.yyStatus == 2"
																		class="waittime">({{changeTime(items.yyDate)}}开始)</text>
																</view>
															</view>
															<view class="bottomPro">
																<view class="leftdetail">
																	<text class="minite">{{items.xmSj}}分钟</text>
																	<text class="xjprice">￥{{items.xj}}</text>
																	<view class="yjprice">￥{{items.yj}}
																		<text class="line"></text>
																	</view>
																</view>
																<view class="rightIcon" @click="addCarts(item, items)">
																	<image src="../../static/images/addPro.png" mode=""
																		style="width: 32rpx;height: 32rpx;margin-top: 5rpx;">
																	</image>
																</view>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
									</view>
									<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
									<component-bottom-line :propStatus="bottom_line_status" propMsg="没有更多了"
										v-else></component-bottom-line>
								</view>
							</z-paging>
						</view>
						<view class="no-list" v-if="twoList.length == 0">
							<view class="noData">
								<image src="https://file.36sm.cn/xtjyjt/images/common/goods_null.png" mode=""
									style="width: 200rpx;height: 200rpx;"></image>
								<view class="name">
									暂无技师～
								</view>
							</view>
						</view>
					</scroll-view>
				</view>

				<view class="bottomCon">
					<view class="bottomC">
						<view class="topCon">
							<view class="leftCart">
								<view class="Cartimg" @click="showCartPopup">
									<image src="../../static/images/addcarts.png" mode="" v-if="addEngineers.length > 0"
										style="width: 76rpx;height: 76rpx;"></image>
									<image src="../../static/images/nocart.png" mode="" v-if="addEngineers.length == 0"
										style="width: 76rpx;height: 76rpx;"></image>
									<view class="NumValue" :class="addEngineers.length > 0 ? 'havecart' : 'nohave'">
										<text>{{addEngineers.length}}</text>
									</view>
								</view>
								<!-- 购物车有商品 -->
								<view class="rightPrice" v-show="addEngineers.length > 0">
									<view class="topP">
										<view class="xjprice">
											￥<text>{{totalxjprice}}</text>
										</view>
										<view class="yjprice">
											￥<text>{{totalyjprice}}</text>
											<text class="line"></text>
										</view>
									</view>
									<view class="bottomD">
										<text>已享受最大优惠减{{ProperPrice}}元</text>
										<image src="../../static/images/toup.png" mode=""
											style="width: 20rpx;height: 20rpx;"></image>
									</view>
								</view>
								<view class="rightPrice" v-show="addEngineers.length == 0">
									<view class="noPro">
										<text>￥0</text>
									</view>
								</view>
							</view>
							<view class="rightbtn">
								<u-button v-if="addEngineers.length >= 0"
									:color="addEngineers.length > 0 ? 'linear-gradient(to right, #FF6900 , #FF6900)' : '#ccc'"
									text="去结算" @tap="goOrder" :custom-style="{ borderRadius: '94rpx' }"
									:loading="addCartloading" loadingText="结算中" class="custom-button">
								</u-button>
							</view>
						</view>
						<view class="bottomCC" v-if="addEngineers.length > 0">
							<view class="Countdown">
								<text>支付倒计时:</text>
								<view class="Timedown">
									<u-count-down :time="timeValue*1000" format="mm:ss" @change="TimeonChange" autoStart>
										<view class="timeview">
											<view class="minutes">
												<text>{{ timeData.minutes>10?timeData.minutes:'0'+timeData.minutes}}</text>
											</view>
											<text class="time__doc">:</text>
											<view class="second">
												<text>{{ timeData.seconds>10?timeData.seconds:'0'+timeData.seconds}}</text>
											</view>
										</view>
									</u-count-down>
								</view>
							</view>
						</view>
					</view>
					<view style="height: 68rpx;">
					</view>
				</view>
				<uni-popup ref="cartPopup" type="bottom" :mask-click="false">
					<view class="PopupCart">
						<view class="topView">
							<view class="title">
								<text>已选推拿师({{addEngineers.length}})位</text>
							</view>
							<view class="Close" @click="closecartPopup">
								<text>继续预约</text>
							</view>
						</view>
						<view class="Unsubscribe">
							<view class="mxList" v-for="(item,index) in addEngineers" :key="item.cartMxId">
								<view class="mxDetail">
									<view class="left">
										<image :src="item.coverImageUrl" mode="aspectFill"
											style="width: 140rpx;height: 140rpx;">
										</image>
									</view>
									<view class="rightDetail">
										<view class="topname">
											<view class="title">
												<text class="coachName">{{item.coachName}}</text>
												<text class="starLevel">{{item.starLevel}}分</text>
											</view>
											<image src="/static/images/deldel.png" mode="aspectFill"
												@click="delEngineer(index)" style="width: 30rpx;height: 30rpx;">
											</image>
										</view>
										<view class="centerC">
											<view class="minite">
												<text>{{item.xmSj}}分钟</text>
											</view>
											<view class="divider">

											</view>
											<view class="proname">
												<text>{{item.projectName}}</text>
											</view>
										</view>
										<view class="bottom">
											<view class="detailTime">
												<text>{{ Dateformat(item.dayInfo) }}</text>
												<text>{{ formatTime(item.startInfo) }}</text>至<text>{{ formatTime(item.endInfo) }}</text>
											</view>
											<view class="rightPrice">
												￥<text>{{item.xj}}</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</uni-popup>

			</view>
			<cc-gifLoading v-else
				gifSrc="https://testksjjkl.oss-cn-hangzhou.aliyuncs.com/txjy/2025/02/12/142c4f60aee441ab884da7052337a6a7.gif"></cc-gifLoading>
		</view>
	</view>
</template>
<script>
	import TabbarCom from '../../components/tabbar/tabbar.vue'
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default {
		components: {
			TabbarCom,
			componentBottomLine,
			componentBottomLoading
		},
		data() {
			return {
				navBarHeight: 0, // 状态栏高度
				visible: false,
				activeIndex: 0,
				// 一级类目
				parentCodeIdList: ['1864195697188818944'], //请求父类id
				oneList: [],
				twoList: [],
				isPromise: true,
				Params: {
					page: 1,
					size: 10,
					placeId: '',
					typeInfo: ''
				},
				timeValue: 180,
				timeData: {},
				loadingStatus: true,
				bottom_line_status: false,
				keyword: "",
				addEngineers: [],
				cartList: [],
				addCartloading: false,
				cartable: false,
				Prodetail: {},
				// 标记倒计时是否开始
				isCountdownStarted: false,
				selectedDD: {},
				placeName: '',
				IsShow: false,
				Projectable: false,
				projectDetail: {}
			}
		},
		onShow() {
			console.log('onshow')
			// this.visible = false
			// this.Params.page = 1
			// console.log(this.activeIndex, 'activeIndex')
			// this.twoList = []
			// this.schemeList()
		},
		// totalxjprice
		computed: {
			totalxjprice() {
				return this.cartList.length == 0 ?
					0 : this.cartList.reduce((sum, engineer) => sum + (engineer.xj || 0), 0).toFixed(2);
			},
			totalyjprice() {
				return this.cartList.length == 0 ?
					0 : this.cartList.reduce((sum, engineer) => sum + (engineer.yj || 0), 0).toFixed(2);
			},
			ProperPrice() {
				return this.totalyjprice - this.totalxjprice;
			}

		},
		onLoad(params) {
			this.Params.page = 1
			this.twoList = []
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			console.log(params, 'params')
			if (params.selectedDD) {
				this.selectedDD = JSON.parse(params.selectedDD)
				console.log(this.selectedDD, '预约的到店时间')
			}
			if (params.placeName) {
				this.placeName = params.placeName
			}
			// 获取二级类目
			this.schemeList()
			// this.schemeName = this.oneList[this.activeIndex].name
			//获取二级类目下的数据
			setTimeout(() => {
				this.visible = true
			}, 1200)
		},
		methods: {
			showProject(item) {
				console.log(item, '展示项目详情', item.projectId)
				if (this.Projectable) {
					return
				}
				this.Projectable = true
				// 获取项目详情
				api({
					url: `tx/project/id/info/${item.projectId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '项目详情')
						this.projectDetail = res.data.data
						console.log(this.projectDetail, '项目详情')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.Projectable = false
					}, 500)
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
					this.Projectable = false
				});

			},
			delEngineer(index) {
				uni.showLoading({
					title: '加载中',
				});
				const cartMxId = this.addEngineers[index].cartMxId
				const params = {
					cartMxIdList: [cartMxId]
				};
				console.log(params, '提交参数')
				api({
					url: `car/del/goods/nub?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, '接口参数')
						this.addEngineers.splice(index, 1);
						if (this.addEngineers.length == 0) {
							// 关闭弹窗
							this.closecartPopup()
						}
						uni.hideLoading()
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			Dateformat(dateString) {
				// 将日期字符串中的空格替换为 'T'，保证兼容性
				const formattedDateString = dateString.replace(" ", "T");
				const date = new Date(formattedDateString);
				const month = date.getMonth() + 1; // 月份从0开始
				const day = date.getDate();
				return `${month}月${day}号`;
			},
			// 格式化结束时间
			formatTime(dateString) {
				// 同样处理时间字符串
				const formattedDateString = dateString.replace(" ", "T");
				const date = new Date(formattedDateString);
				const hour = date.getHours();
				const minute = date.getMinutes();
				return `${hour}:${minute < 10 ? '0' + minute : minute}`;
			},
			closecartPopup() {
				this.$refs.cartPopup.close()
				uni.showLoading({
					title: '加载中',
				});
				this.Params.page = 1;
				this.schemeListData();
			},
			showCartPopup() {
				if (this.addEngineers.length == 0) return
				this.$refs.cartPopup.open()
				// if (!this.IsShow) {
				// 	this.$refs.cartPopup.open()
				// } else {
				// 	this.$refs.cartPopup.close()
				// }
				// this.IsShow = !this.IsShow
			},
			changeTime(date) {
				return date.slice(11, 16)
			},
			ToCoash(item) {
				// 推拿师预约详情
				uni.navigateTo({
					url: `/pageg/coash_resources/coashDetails?coachId=${item.coachId}`
				})
			},
			addCarts(item, items) {
				if (this.cartList.length >= 3) {
					uni.showModal({
						title: '温馨提示',
						content: '单笔订单最多可选择3位推拿师。如需继续预约，请先支付此笔订单，在预约所需项目及推拿师',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								console.log('用户点击确定');
								return
							}
						}
					});
					return
				}
				console.log(item, items, '添加购物车');
				// 检查是否需要等待
				if (items.yyStatus == 2) {
					const formattedDate = items.yyDate?.slice(5, 16);
					// 需等待
					uni.showModal({
						title: '',
						content: `预计${formattedDate}开始`,
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定');
								// 用户点击确定后，执行下面的操作
								this.executeAddToCart(item, items, items.yyDate);
							} else if (res.cancel) {
								console.log('用户点击取消');
								// 如果点击取消，不执行后续操作
							}
						}.bind(this) // 使用 bind 强制绑定 this
					});
				} else {
					// 如果不需要等待，直接执行
					this.executeAddToCart(item, items);
				}
			},
			// 格式化日期为 "yyyy/MM/dd HH:mm"
			formatDate(date) {
				console.log(date, '转换的时间')
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}:00`;
			},

			executeAddToCart(item, items, yyDate = null) {
				if (this.cartable) {
					return;
				}
				this.cartable = true;
				uni.showLoading({
					title: '预约中'
				});
				console.log(this.selectedDD, '预约的到店时间');

				// 获取当前年份
				const currentYear = new Date().getFullYear();

				// 如果 yyDate 存在，则使用 yyDate 来计算开始时间，否则使用 selectedDD 来计算
				let startDate;
				if (yyDate) {
					// 使用 yyDate 的时间
					startDate = new Date(yyDate.replace(/-/g, '/')); // 替换 - 为 /，确保跨平台兼容
				} else {
					// 使用 selectedDD 的时间
					const monthDay = this.selectedDD.selectedDate.split('月');
					const month = monthDay[0]; // 2
					const day = monthDay[1].replace('日', ''); // 17
					startDate = new Date(
						`${currentYear}-${month}-${day} ${this.selectedDD.selectedHour}:${this.selectedDD.selectedMinute}:00`
					);
				}

				const startInfo = this.formatDate(startDate); // 格式化成 "yyyy/MM/dd HH:mm"

				// 结束时间（endInfo）
				const endDate = new Date(startDate.getTime() + items.xmSj * 60000); // xmSj 为分钟，转换成毫秒
				const endInfo = this.formatDate(endDate); // 格式化成 "yyyy/MM/dd HH:mm"
				this.Prodetail = {
					coachId: item.coachId,
					coachName: item.coachName,
					cooperateLevel: item.cooperateLevel,
					coverImageUrl: item.coverImageUrl,
					projectId: items.projectId,
					projectName: items.projectName,
					starLevel: item.starLevel,
					startInfo: startInfo,
					endInfo: endInfo,
					xj: items.xj,
					yj: items.yj,
					xmSj: items.xmSj,
					dayInfo: startInfo
				};
				console.log(this.Prodetail, '提交参数');
				api({
					url: `car/add/goods/info?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: this.Prodetail
				}).then(res => {
					if (res.code === 200) {
						this.cartable = false;
						uni.hideLoading();
						console.log(res.data.data, '接口参数');
						this.cartList.push(this.Prodetail)
						this.addEngineers.push(res.data.data);
						setTimeout(() => {
							this.Prodetail = {};
						}, 1500);
						// 新增预约 重新计时
						this.timeValue = 180;
						this.$refs.cartPopup.open()
						console.log(this.addEngineers, 'addEngineers');
						console.log(this.cartList, 'cartList')
						this.Params.page = 1;
						this.schemeListData();
					} else {
						this.cartable = false;
						this.Prodetail = {};
						uni.hideLoading();
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					this.cartable = false;
					this.Prodetail = {};
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			TimeonChange(time) {
				this.timeData = time
				// 倒计时为0
				// 如果倒计时已经开始过
				if (this.isCountdownStarted) {
					// 倒计时为0
					if (this.timeData.minutes == 0 && this.timeData.seconds == 0) {
						// 支付超时了
						uni.showModal({
							title: '温馨提示',
							content: '支付超时了哟,快快重新下单吧',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									console.log('用户点击确定');
									this.addEngineers = [];
									this.cartList = [];
									uni.switchTab({
										url: '/pages/home/<USER>'
									});
								}
							}
						});
					}
				} else {
					// 如果倒计时未开始，则标记倒计时已开始
					this.isCountdownStarted = true;
				}
			},
			goOrder() {
				//结算操作
				console.log('去结算')
				if (this.addEngineers.length == 0) {
					uni.showToast({
						title: '您还未添加项目哦!',
						icon: "none"
					});
					return
				}
				if (this.addCartloading) {
					return
				}
				this.addCartloading = true
				const cartMxIdList = this.addEngineers.map(engineer => engineer.cartMxId);
				// 构造提交参数
				const params = {
					cartMxIdList: cartMxIdList
				};
				console.log(params, '提交参数')
				// 购物车生成订单
				api({
					url: `car/sell/goods/info?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code === 200) {
						console.log(res, '接口参数')
						// 订单生成成功  跳转进入订单确认页面
						const orderinfo = res.data.data
						uni.navigateTo({
							url: `/pageg/confirm_order/index?orderinfo=${encodeURIComponent(JSON.stringify(orderinfo))}&placeName=${this.placeName}`
						});
						// this.addEngineers = []
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.cartList = []
						this.addEngineers = []
						this.addCartloading = false
						this.$refs.cartPopup.close()
					}, 800)
				}).catch(err => {

					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
					this.addCartloading = true
				});
			},
			changeCrease(item, iswhat) {
				console.log(item, iswhat)
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			onReachBottom() {
				console.log('上拉触底')

				this.loadingStatus = true
				this.Params.page++
				this.schemeListData()

				// this.$refs.bottom.get_video_data()

			},
			onscrollBottom() {
				console.log('上拉触底')

				this.loadingStatus = true
				this.Params.page++
				this.schemeListData()
			},
			queryList() {
				console.log('下拉刷新')
				this.Params.page = 1
				this.Params.size = 10
				this.schemeListData()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			// 点击类目
			changeActive(item, index) {
				console.log(item, index);
				this.Params.page = 1
				// this.twoList = []
				uni.showLoading({
					title: '加载中',
				});
				this.Params.typeInfo = item.codeId
				this.activeIndex = index
				console.log(this.Params.typeInfo);
				this.schemeListData()
			},
			searchPlace() {
				this.Params.page = 1
				this.twoList = []
				this.schemeListData()
			},
			clearPlace() {
				this.Params.page = 1
				this.twoList = []
				this.schemeListData()
			},
			schemeList() {
				//获取数据
				const dataList = {
					parentCodeIdList: this.parentCodeIdList
				}
				console.log(dataList, '数据格式');
				api({
					url: `tx/code/get/sub/list/map`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: dataList
				}).then(res => {
					console.log(res, '分类列表')
					if (res.code == 200) {
						console.log(res);
						const data = res.data.data
						this.oneList = data.A1864195697188818944
						console.log(this.oneList);
						this.Params.typeInfo = this.oneList[this.activeIndex].codeId
						console.log(this.Params.typeInfo);
						this.schemeListData()
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},

			// 获取二级类目下的数据
			schemeListData() {
				api({
					url: `tx/project/get/list?page=${this.Params.page}&size=${this.Params.size}&typeInfo=${this.Params.typeInfo}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					// console.log(res, '分类列表')
					if (res.code == 200) {
						uni.hideLoading();
						const data = res.data.data
						// console.log(data, '列表数据')
						if (this.twoList.length == data.totalCount) {
							setTimeout(() => {
								this.loadingStatus = false;
								this.bottom_line_status = true
							}, 500)
							if (this.Params.page == 1) {
								this.twoList = [...data.list]
							}
						} else {
							if (this.Params.page == 1) {
								this.twoList = [...data.list]
								if (this.twoList.length == data.totalCount) {
									this.bottom_line_status = true
								}
							} else {
								this.twoList = [...this.twoList, ...data.list]
							}
							setTimeout(() => {
								this.loadingStatus = false;
							}, 500)
						}
						console.log(this.twoList, 'twoList')
						setTimeout(() => {
							this.visible = true
						}, 1200)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			navToDetail(item) {
				console.log(item);
				uni.navigateTo({
					url: `/pageu/caseDetails/caseDetails?schemeId=${item.schemeId}`
				})
			},

			//下拉刷新
			onPullDownRefresh() {
				console.log('上拉触底')
				this.Params.page = 1
				uni.stopPullDownRefresh();
				this.twoList = [];
				this.schemeListData()
			},

			loadMore() {
				console.log('下拉刷新');
				// if (this.visible) return; // 防止重复加载
				console.log(this.Params.page);
				if (this.isPromise) {
					this.Params.page; // 重置页码
					this.schemeListData(); // 触发刷新
				}

			}

		}
	}
</script>
<style lang="scss" scope>
	.page {
		background-color: #FBF5F1;
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}

	.header {
		width: 100%;
		height: 80rpx;
		background-color: #FBF5F1;
		position: fixed;
		z-index: 999;

		.navbar {
			height: 80rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			// padding: 0 10rpx;
			box-sizing: border-box;

			.imgView {
				width: 60rpx;
				height: 60rpx;

				image {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.title {
				letter-spacing: 1rpx;
				// width: 144rpx;
				height: 44rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 36rpx;
				color: #333333;
				line-height: 44rpx;
				// text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}

		.toptab {
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;

			.paixu {
				width: 25%;
				display: flex;
				align-items: center;
				justify-content: center;

				.updown {
					margin-left: 10rpx;
				}
			}

			.quyu,
			.dengji {
				width: 25%;
				display: flex;
				align-items: center;
				justify-content: center;

				.updown {
					margin-left: 10rpx;
				}
			}

			.qktj {
				display: flex;
				width: 25%;
				align-items: center;
				justify-content: center;

				.trash {
					margin-right: 10rpx;
				}
			}

			.paixu:hover,
			.quyu:hover,
			.dengji:hover,
			.qktj:hover {
				background-color: #ddd;
			}
		}
	}

	.classifylist {
		.cate-list {
			margin-top: 180rpx;
			height: calc(100vh - 190rpx);
			overflow-y: auto;
			background-color: #FBF5F1;
			// padding-top: 10rpx;
			display: flex;

			.cate-left {
				flex: 1;
				background-color: #FBF5F1;

				.cate-item {
					width: 100%;
					height: 80rpx;
					line-height: 80rpx;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					// padding-left: 40rpx;
					box-sizing: border-box;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #777777;
					font-style: normal;
					text-transform: none;

					.cate-detail {
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.isactive {
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

				.active {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 32rpx;
					color: #FF6900;
					font-style: normal;
					text-transform: none;

					background-color: #fff;
					border-radius: 20rpx 0rpx 0rpx 20rpx;
				}
			}

			.twoRightlist {
				flex: 3;

				.scroll-container {
					height: calc(100vh - 380rpx);
					overflow-y: auto;
					color: #0d0d0d;

					.cate-right {
						.twolist {
							.listlist {
								.twoitem {
									width: 530rpx;
									margin-bottom: 12rpx;
									background: #FFFFFF;
									border-radius: 0rpx 16rpx 16rpx 16rpx;

									.twodetail {
										.topC {
											margin: 0 24rpx;
											display: flex;
											align-items: center;
											border-bottom: 1rpx solid #EEEEEE;
											padding: 22rpx 0;

											.right {
												flex: 1;
												height: 84rpx;
												margin-left: 24rpx;
												// background-color: skyblue;
												display: flex;
												flex-direction: column;
												justify-content: space-between;

												.topN {
													display: flex;
													align-items: center;
													justify-content: space-between;

													.name {

														// background-color: skyblue;
														// padding-right: 5rpx;
														text {
															font-family: PingFang SC, PingFang SC;
															font-weight: 500;
															font-size: 28rpx;
															color: #333333;
															font-style: normal;
															text-transform: none;
															margin-right: 8rpx;
														}

														image {
															margin-right: 5rpx;
														}
													}
												}

												.bottom {
													font-family: PingFang SC, PingFang SC;
													font-weight: 400;
													font-size: 24rpx;
													color: #666666;
													font-style: normal;
													text-transform: none;
												}
											}
										}

										.project {
											padding: 10rpx 22rpx;

											.projectitem {
												margin: 12rpx 0;

												.projectdetail {
													.topPro {
														display: flex;
														align-items: center;
														justify-content: space-between;
														height: 44rpx;
														line-height: 44rpx;
														margin-bottom: 5rpx;

														.lefttitle {
															font-family: PingFang SC, PingFang SC;
															font-weight: 600;
															font-size: 32rpx;
															color: #000000;
															font-style: normal;
															text-transform: none;
														}

														.rightStatus {
															.yystatus {
																font-family: PingFang SC, PingFang SC;
																font-weight: 400;
																font-size: 28rpx;
																color: #FF6900;
																font-style: normal;
																text-transform: none;
															}

															.waittime {
																font-family: PingFang SC, PingFang SC;
																font-weight: 400;
																font-size: 20rpx;
																color: #999999;
																font-style: normal;
																text-transform: none;
															}
														}
													}

													.bottomPro {
														height: 40rpx;
														display: flex;
														align-items: center;
														justify-content: space-between;

														.leftdetail {
															display: flex;
															align-items: center;

															.xjprice {
																margin: 0 8rpx;
																font-weight: 500;
																font-size: 24rpx;
																color: #FF6900;
																line-height: 40rpx;
																font-style: normal;
																text-transform: none;
															}

															.minite {
																font-family: PingFang SC, PingFang SC;
																font-weight: 400;
																line-height: 40rpx;
																font-size: 22rpx;
																color: #999999;
																font-style: normal;
																text-transform: none;
															}

															.yjprice {
																font-family: PingFang SC, PingFang SC;
																font-weight: 400;
																line-height: 40rpx;
																font-size: 22rpx;
																color: #999999;
																font-style: normal;
																text-transform: none;
																position: relative;
																width: 62rpx;
																height: 40rpx;

																// background-color: skyblue;
																.line {
																	position: absolute;
																	top: 50%;
																	/* 垂直居中 */
																	left: 0;
																	/* 水平居中 */
																	min-width: 90rpx;
																	height: 0rpx;
																	border: 1rpx solid #d6d6d6;
																	transform: translate(0, -50%);
																	/* 精确居中，补偿自身的宽高 */
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}

						}

					}

					.no-list {
						height: calc(100vh - 285rpx);

						.noData {
							width: 100%;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							height: 100%;

							image {
								width: 150rpx;
								height: 150rpx;
							}

							.name {
								margin: 10rpx 0;
								color: #ccc;
								letter-spacing: 4rpx;
							}
						}
					}
				}
			}

			.bottomCon {
				position: fixed;
				width: 100%;
				background: #FFFFFF;
				bottom: 0;
				z-index: 999;

				.bottomC {
					// height: 212rpx;
					// border-bottom: 2rpx solid #EEEEEE;
					padding: 24rpx 20rpx 0 20rpx;

					.topCon {
						// background-color: skyblue;
						padding: 5rpx 0;
						display: flex;
						margin-bottom: 10rpx;
						justify-content: space-between;

						.leftCart {
							display: flex;

							.Cartimg {
								width: 76rpx;
								height: 76rpx;
								position: relative;
								margin-right: 22rpx;

								.NumValue {
									position: absolute;
									right: 0;
									top: 0;
									min-width: 24rpx;
									height: 24rpx;
									line-height: 24rpx;
									text-align: center;
									border-radius: 60rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									font-size: 20rpx;
									color: #FFFFFF;
									font-style: normal;
									text-transform: none;
								}

								.havecart {
									color: #FFFFFF;
									background: #FF6900;
								}

								.nohave {
									color: #FFFFFF;
									background: #999999;
								}

							}

							.rightPrice {
								.topP {
									display: flex;
									align-items: flex-end;

									.xjprice {
										font-family: PingFang SC, PingFang SC;
										color: #FF6900;
										text-align: 8left;
										font-style: normal;
										text-transform: none;
										font-size: 24rpx;

										text {
											font-weight: 600;
											font-size: 32rpx;
										}

										margin-right: 14rpx;
									}

									.yjprice {
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										line-height: 40rpx;
										font-size: 22rpx;
										color: #999999;
										font-style: normal;
										text-transform: none;
										position: relative;
										width: 62rpx;
										height: 40rpx;

										.line {
											position: absolute;
											top: 50%;
											left: 0;
											min-width: 80rpx;
											height: 0rpx;
											border: 1rpx solid #d6d6d6;
											transform: translate(0, -50%);
											/* 精确居中，补偿自身的宽高 */
										}
									}
								}

								.bottomD {
									height: 40rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 18rpx;
									color: #999999;
									line-height: 40rpx;
									text-align: left;
									font-style: normal;
									text-transform: none;
									display: flex;
									align-items: center;
									margin-right: 8rpx;
								}
							}

							.noPro {
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: center;

								text {
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #999999;
									font-style: normal;
									text-transform: none;
									letter-spacing: 2rpx;
								}
							}
						}

						.rightbtn {
							width: 240rpx;
							height: 80rpx;
						}

					}

					.bottomCC {
						display: flex;
						align-items: center;
						justify-content: center;

						.Countdown {
							display: flex;
							align-items: center;
							color: #FF6900;

							.Timedown {
								margin-left: 12rpx;

								.timeview {
									display: flex;

									.minutes,
									.second {
										width: 32rpx;
										height: 32rpx;
										line-height: 32rpx;
										// background: #EC5C1B;
										border-radius: 4rpx 4rpx 4rpx 4rpx;
										font-family: DIN Black, DIN Black;
										// font-weight: 600;
										font-size: 24rpx;
										text-align: center;
										font-style: normal;
										text-transform: none;
									}

									.time__doc {
										width: 10rpx;
										height: 32rpx;
										line-height: 32rpx;
										margin: 0 4rpx;
										font-family: DIN Black, DIN Black;
										// font-weight: 600;
										font-size: 32rpx;
										color: #FF6900;
										text-align: center;
										font-style: normal;
										text-transform: none;
									}
								}
							}
						}
					}
				}
			}

			.PopupCart {
				width: 100%;
				min-height: 300rpx;
				background-color: #EEEEEE;
				border-radius: 30rpx 30rpx 0 0;
				padding-bottom: 80rpx;
				margin-bottom: -80rpx;
				padding: 40rpx 0 250rpx 0;

				.topView {
					// background-color: skyblue;
					width: 94%;
					margin: 0 auto;
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 50rpx;
					font-family: PingFang SC, PingFang SC;
					font-size: 30rpx;
					color: #333333;
					font-weight: 600;
					font-style: normal;
					text-transform: none;
					letter-spacing: 1rpx;
					margin-bottom: 30rpx;
				}

				.Unsubscribe {
					width: 92%;
					margin: 0 auto;

					.mxList {
						border-radius: 20rpx;
						background-color: #fff;
						margin-bottom: 20rpx;

						.mxDetail {
							padding: 30rpx 20rpx;
							display: flex;
							align-items: center;

							.left {
								margin-right: 30rpx;

								image {
									border-radius: 50%;
								}
							}

							.rightDetail {
								// height: 120rpx;
								flex: 1;

								.topname {
									display: flex;
									align-items: center;
									justify-content: space-between;

									.title {
										.coachName {
											font-family: PingFang SC, PingFang SC;
											font-weight: 600;
											font-size: 28rpx;
											color: #000000;
											font-style: normal;
											text-transform: none;
											margin-right: 30rpx;
										}

										.starLevel {
											font-family: PingFang SC, PingFang SC;
											font-weight: 400;
											font-size: 20rpx;
											color: #999999;
											font-style: normal;
											text-transform: none;
										}
									}
								}

								.centerC {
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									font-size: 28rpx;
									color: #333333;
									font-style: normal;
									text-transform: none;
									display: flex;
									align-items: center;
									// justify-content: space-between;
									width: 250rpx;
									margin: 15rpx 0;

									.minite {}

									.divider {
										width: 4rpx;
										height: 20rpx;
										background-color: #ccc;
										margin: 0 20rpx;
									}

									.proname {}
								}

								.bottom {
									display: flex;
									align-items: center;
									justify-content: space-between;

									.detailTime {
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 22rpx;
										color: #999999;
										font-style: normal;
										text-transform: none;
										margin: 10rpx 0;
									}

									.rightPrice {
										font-family: PingFang SC, PingFang SC;
										font-weight: 600;
										font-size: 28rpx;
										color: #FF6900;
										font-style: normal;
										text-transform: none;
									}
								}
							}
						}
					}
				}

			}
		}
	}
</style>