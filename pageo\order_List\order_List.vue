<template>
	<view >
		<view class="top-status-bar" id="topStatusBar">
			<view class="slot-view">
				<view class="topView status-content">
					<view class="bgc_img">
						<view class="imgView">
							<image src="https://file.36sm.cn/mttttxxs/2025/01/15/f7de237b29df4dba81b123de37176725.png" mode=""></image>
						</view>
						<view class="tabLeft">
							<view class="imgView" @click="goBack">
								<image src="https://file.36sm.cn/xtjyjt/images/common/Vector.png" mode=""></image>
							</view>
						</view>
					</view>
					<!-- <view class="TopTabs">
						<view class="tab_item" @click="IndexTab(item)" v-for="(item,index) in FiledList" :key="item.marketId" :class="marketId == item.marketId ? 'tabActive' : ''" >
							<text >{{item.name}}</text>
							<view :class="marketId == item.marketId ? 'line' : ''"></view>
						</view>
					</view> -->
					
					<view class="TopTabs">
						<u-tabs :list="FiledList" @click="sectionChange" :current="current" keyName="name" gutter="25"
						  :itemStyle="{height: '44px',boxSizing: 'border-box', fontSize: '16rpx',}" lineColor="#2A3A88FF" lineWidth="60"></u-tabs>
					</view>
				</view>
				<slot></slot>
			</view>
		</view>
		<!-- <scroll-view class="scroll-container" scroll-y="true"  @scrolltolower="onscrollBottom"> -->
		<view class="scroll-container">
			<view class="detail_view" style="margin-top: 12rpx;" v-if="visible">
				<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
					:use-page-scroll="true" :auto-show-back-top-top="true" :auto="false">
					<refresh-loading slot="refresher"></refresh-loading>
					
				<view v-if="ProductList.length > 0">
					<view class="detail_item" v-for="(item, index) in ProductList" :key="item.productCode">
						<view class="itemleft" @tap="goToProdetail(item)">
							<image :src="item.recommendImageUrlSquare" mode=""></image>
						</view>
						<view class="itemright">
							<view class="righttop">
								<!-- <view class="title">
									{{item.productTitle}}
								</view> -->
								
								<u--text :lines="2" :text="item.productTitle" size="28rpx" color="#000" bold></u--text>
							</view>
							<view class="rightbottom">
								<!-- <view class="Amprice">
										USD 
										<view class="value">
											{{item.productSellPriceTrade}}
										</view>
								</view> -->
								<view class="CNprice">
								 {{item.tradeCoinName}} {{item.price}}
								</view>
								<view class="saleView">
									<view class="monthSold">
										<text>{{item.recommendReason}}</text>
										<view class="value">
											 {{item.salesVolume}}
										</view>
									</view>
									<view class="btn"  @tap="goToProdetail(item)">
										抢购
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="noOrder" v-if="ProductList.length == 0">
					<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
					<text class="notitle">暂无商品~</text>
				</view>
			<!-- 结尾 -->
			    <component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line :propStatus="bottom_line_status" propMsg="数据加载完全" v-if="!loadingStatus && ProductList.length > 0"></component-bottom-line>
				
				<!-- 版权信息 -->
				<!-- <component-copyright></component-copyright> -->
				
				</z-paging>
			</view>
		    <cc-gifLoading v-if="!visible"
		    	gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
		<!-- </scroll-view> -->
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import api from '@/common/request/index.js'
	import {
		getFiled,
		getRecommend
	} from "@/common/request/api/home.js"
	
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';

	export default {
		name: "top-status-bar",
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			},
			isrefresh:{
				type:Boolean,
				default: false
			}
		},
		data() {
			return {
				slotObserver: null,
				slotHeight: 0,
				tabName: "",
				marketId: "",
				// tabblist
				FiledList: [],
				params: {
					page: 1,
					size: 10
				},
				ProductList: [],
				// 视图加载
				visible: false,
				// 下拉触底加载效果
				loadingStatus: true,
				current: 0,
				bottom_line_status: false
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		computed: {
			...mapState({
				topStatusBarData: state => state.init.topStatusBarData
			}),
			statusBarHeight() {
				
				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight',h)
				return h
			},
			slotMarginTop() {
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				// #endif
				return sw
			},
			titleHeight(){
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			},
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		mounted() {
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
		},
		onLoad(params) {
			console.log(params, '路由参数')
			this.tabName = params.name
			this.marketId = params.marketId
			this.getFiledList()
		},
		created() {
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
		},
		destroyed() {
			// 取消节点监听
			this.slotObserver?.disconnect()
			let data = {
					statusBarHeight: this.statusBarHeight,
					slotWidth: this.slotWidth,
					slotMarginTop: this.slotMarginTop,
					titleHeight:this.titleHeight,
					isLoad: true
				}
			this.setGlobalData(data)
		},
		onReachBottom() {
			console.log('上拉触底')
			this.loadingStatus = true
			this.params.page ++
			this.getProductList()
		},
		methods: {
			...mapMutations(['setTopStatusBarData']),
			setGlobalData(data) {
				this.setTopStatusBarData(data)
			},
			queryList() {
				// 重置瀑布流数据
				// this.ProductList = []
				this.params.page = 1
				this.params.size = 10
				console.log('下拉刷新')
				this.getProductList()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			onscrollBottom() {
				console.log('上拉触底')
				this.loadingStatus = true
				this.params.page ++
				this.getProductList()
			},
			
			sectionChange(item) {
				this.visible = false
				console.log(item, 'item')
				this.current = item.index;
				this.tabName = item.name
				this.marketId = item.marketId
				this.ProductList = []
				this.params.size = 10
				this.params.page = 1
				// setTimeout(() => {
					this.getProductList()
				// }, 100)
			},
			IndexTab(item) {
				this.visible = false
				this.tabName = item.name
				this.marketId = item.marketId
				this.ProductList = []
				this.params.size = 10
				this.params.page = 1
				this.getProductList()
				// setTimeout(() => {
				// 	this.getProductList()
				// }, 100)
			},
			goToProdetail(item) {
				console.log(item, 'productitem')
				
				
				if(item.resourceType == 0) {
					// 限时报名
					uni.navigateTo({
						url: `/pageo/product_details/product_details?acid=${item.productCode}&resourceType=${item.resourceType}`
					})
				}
				
				if(item.resourceType == 1) {
					// 活动商品
					uni.navigateTo({
						url: `/pageo/product_details/product_details?acid=${item.productCode}&resourceType=${item.resourceType}`
					})
				}
				
				if(item.resourceType == 2) {
					// 活动产品
					uni.navigateTo({
						url: `/pageu/productDetails/productDetails?productId=${item.productCode}&resourceType=${item.resourceType}`
					})
					
				}
				
				return
				const token = uni.getStorageSync("token")
				if(!token) {
					uni.showToast({
						title: this.$t('message.qxdl'),
						icon: 'none'
					})
					// setTimeout(() => {
						uni.navigateTo({
							url: '/pageo/login/login',
						})
					// }, 1000);
					return
				}
				uni.navigateTo({
					url: `/pageo/product_details/product_details?acid=${item.productCode}`
				})
			},
			goBack() {
				// this.$nextTick(() => {
				// });
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getFiledList() {
				api({
				    url: `ac/mk/get/field?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get',
				}).then(res => {
				    if (res.code == 200) {
				    	this.FiledList = res.data.data.list
				    	console.log(this.FiledList, '获取活动的名称和商品列表')
				    	console.log(this.tabName, '当前tabbarname', this.marketId)
				    	console.log(this.FiledList, 'FiledList')
				    	this.current = this.FiledList.findIndex(item => item.marketId == this.marketId);
				    	console.log(this.current, 'currentcurrent')
				    	setTimeout(() => {
				    		this.getProductList()
				    	}, 200)
				    }else {
				    	uni.showToast({
				    		title: res.msg,
				    		icon: "none"
				    	})
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || this.$t('message.qqsb'),
				        icon: "none"
				    });
				});
			},
			// 获取商品列表
			getProductList() {
				api({
				    url: `ac/mk/get/field/sp?location=${this.location}&marketId=${this.marketId}&page=${this.params.page}&size=${this.params.size}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
				    if (res.code === 200) {
						console.log(res.data.data.list, '商品列表')
						if(this.ProductList.length == res.data.data.totalCount) {
							// 数据全部加载完全
							// setTimeout(() => {
								this.loadingStatus = false
								this.bottom_line_status = true
							// }, 500)
						} else {
							if(this.params.page == 1) {
								this.ProductList = [...res.data.data.list]
								if(this.ProductList.length == res.data.data.totalCount) {
									this.bottom_line_status = true
								}
							} else {
							   this.ProductList = [...this.ProductList, ...res.data.data.list]
							}
							this.$set(this, 'ProductList', this.ProductList);
							this.visible = true
							setTimeout(() => {
								this.loadingStatus = false
							}, 500)
							console.log(this.ProductList, '商品列表数据')
						}
						
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	view{
		box-sizing: border-box;
	}
	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		z-index: 999;
		background-color: #fff;
		.slot-view{
			width: 100%;
			.topView{
				width: 100%;
				.bgc_img{
					width: 100%;
					.imgView{
						position: relative;
						image{
							width: 100%;
							height: 180rpx;
						}
					}
					.tabLeft{
						position: absolute;
						left: 20rpx;
						top: 110rpx;
						.imgView{
							width: 20rpx;
							height: 30rpx;
							image{
								width: 20rpx;
								height: 30rpx;
							}
						}
					}
				}
				.TopTabs{
					height: 86rpx;
					margin-top: -8rpx;
					background-color: #fff;
					
				}
			}
		}
	}
	.scroll-container {
		margin-top: 270rpx;
	    // height: calc(100vh - 360rpx); /* 设置高度，减去其他固定元素的高度 */
	    overflow-y: auto; /* 启用垂直滚动 */
		.detail_view{
			margin-top: 12rpx;
			.detail_item{
				background-color: #fff;
				border-radius: 18rpx;
				width: 97%;
				margin: 0 auto;
				margin-bottom: 12rpx;
				display: flex;
				align-items: center;
				padding: 16rpx;
				.itemleft{
					image{
						border-radius: 18rpx;
						width: 180rpx;
						height: 180rpx;
					}
				}
				.itemright{
					margin-left: 20rpx;
					width: 100%;
					.righttop{
						height: 80rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 28rpx;
						color: #000000;
						text-align: left;
						font-style: normal;
						text-transform: none;
						letter-spacing: 1rpx;
					}
					.rightbottom{
						padding: 0 10rpx;
						.Amprice{
							display: flex;
							align-items: center;
							width: 108rpx;
							font-family: DIN, DIN;
							font-weight: 400;
							font-size: 24rpx;
							color: #2A3A88FF;
							text-align: left;
							font-style: normal;
							text-transform: none;
							.value{
								width: 108rpx;
								height: 32rpx;
								margin-left: 5rpx;
								font-family: DIN, DIN;
								font-weight: 600;
								font-size: 30rpx;
								color: #2A3A88FF;
								line-height: 32rpx;
								text-align: left;
								font-style: normal;
								text-transform: none;
							}
						}
						.CNprice{
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 22rpx;
							color: #777777;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
						.saleView{
							display: flex;
							align-items: center;
							justify-content: space-between;
							.monthSold{
								display: flex;
								align-items: center;
								width: 226rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #AAAAAA;
								text-align: left;
								font-style: normal;
								text-transform: none;
								.value{
									margin-left: 10rpx;
									color: #2A3A88FF;
								}
							}
							.btn{
								width: 144rpx;
								height: 46rpx;
								background: linear-gradient( 92deg, #2A3A88FF 0%, #bdc0cdff 100%);
								border-radius: 110rpx 110rpx 110rpx 110rpx;
								font-family: PingFang SC, PingFang SC;
								font-size: 24rpx;
								font-weight: 300;
								color: #FFFFFF;
								display: flex;
								align-items: center;
								justify-content: center;
								font-style: normal;
								text-transform: none;
							}
						}
					}
				}
			}
			
			.noOrder{
				height: 800rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.notitle{
					margin-top: 50rpx;
					color: #ccc;
					letter-spacing: 2rpx;
				}
			}
		}
	}
</style>