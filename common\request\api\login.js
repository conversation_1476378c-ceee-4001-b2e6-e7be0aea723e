import api from '../index.js'

const location = uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh';

// 注册会员获取验证码
export const postRegisterSendCode = data =>
	api({
		url: `mb/login/register/sendCode?location=${location}`,
		method: 'post',
		header: {
			// 'content-type': 'application/x-www-form-urlencoded',
			// 'content-type': 'application/json'
		},
		data
	})
// 注册会员
export const postLoginRegister = data =>
	api({
		url: `mb/login/register?location=${location}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在注册'
	})
// 修改、忘记密码获取验证码
export const postModifySendCode = data =>
	api({
		url: `mb/login/modify/sendCode?location=${location}`,
		method: 'post',
		header: {
			// 'content-type': 'application/json;charset=UTF-8'
		},
		data
	})
// 修改密码
export const postLoginrModify = data =>
	api({
		url: `mb/login/modify?location=${location}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在验证'
	})
// 会员登录获取验证码
export const postCodeSendCode = data =>
	api({
		url: `mb/login/code/sendCode?location=${location}`,
		method: 'post',
		header: {
			// 'content-type': 'application/json;charset=UTF-8'
		},
		data
	})
// 会员验证码登录
export const postCodeInfo = data =>
	api({
		url: `mb/login/code/info?location=${location}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在验证'
	})
// 密码登录
export const postLoginInfo = data =>
	api({
		url: `mb/login/info?location=${location}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在登录'
	})
// 微信授权登录
export const postWxAuthLogin = data =>
	api({
		url: `mb/login/wx/auth/login?location=${location}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '授权登录'
	})
	
	
//微信手机号登录
export const wxPhoneLogin = (data) => {
  return new Promise((resolve, reject) => {
    api({
      // url: "/mb/login/app/phone/code",
	  url: "/js/login/app/phone/code",
      method: "post",
      data
    }, {
      auth: false,
      isLoading: true,
      loadingMsg: '正在登录'
    }).then(res => {
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
}
