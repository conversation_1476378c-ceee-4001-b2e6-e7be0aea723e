<template>
    <m-tabbar fixed fill :current="tableIndex" :tabbar="tabbar" @change="handleChange" @showPopup="handleShowPopup">
		<template v-slot:tabbar_index_1>
		        <view class="custom_style">
		            <view class="custom_style_icon">
		            	<image class="custom_style_img" src="https://file.36sm.cn/jaxinfo/2025/06/10/dcd7417ee833404a916343257cb4f7d9.png" mode="aspectFit" ></image>
		            </view>
		        </view>
		</template>
	</m-tabbar>
</template>

<script>
	
  export default {
    data() {
      return {
        
      };
    },
	computed:{
		tableIndex(){
			return this.$store.state.tabbar.tabindex
		},
		tabbar(){
			return this.$store.state.tabbar.tabbar
		}
	},
	methods:{
		handleChange(e){
			this.$store.dispatch('setIndex',e)
		},
		handleShowPopup(value) {
			// console.log(value, 'value')
			this.$emit('showPopup', value);
		},
	}
  };
</script>
<style lang="scss" scoped>
	.custom_style{
	        color: #fff;
	        display: flex;
	        flex-direction: column;
	        align-items: center;
	        justify-content: center;
			font-family: 'alimm-font' !important;
	        &_icon{
				// box-shadow: 0 -10rpx 25rpx rgba(0, 0, 0, 0.1);
				background-color: #fff;
	            width: 140rpx;
	            height: 140rpx;
	            border-radius: 100%;
	            display: flex;
	            justify-content: center;
	            align-items: center;
	            margin-top: -80rpx;
				padding-top: 7rpx;
				z-index: -999;
	        }
			&_img{
				width: 120rpx;
				height: 120rpx;
				border-radius: 100%;
			}
	    }
</style>