const objToUri = obj => {
	return Object.keys(obj)
		.filter(k=>obj[k])
		.map(k=>k + '=' + obj[k])
		.join('&')
}

const getWxScene = (key, scene) => {
	const queryNameRegex = new RegExp(`${key}=([^&]*)(&|$)`)
	let queryNameMatch = scene.match(queryNameRegex)
	let params = null
	if (queryNameMatch) {
		params = queryNameMatch[1]
		let length = params.length - 1
		if (params[length] === '/') {
			params = params.slice(0, length)
		}
	}
	return params
}
// 微信的scene参数转换为对象形式
const sceneParamsToObj = url => {
	if (url) {
		let params = decodeURIComponent(url).split('&')
		let obj = {}
		params.forEach(item => (obj[item.split('=')[0]] = item.split('=')[1]))
		return obj
	}
	return {}
}
export { objToUri, getWxScene,sceneParamsToObj}
