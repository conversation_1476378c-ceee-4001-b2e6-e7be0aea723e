<template>
	<view class="page">
		<view class="u-p-10 formData" style="background-color: #fff;margin: 10rpx 10rpx;border-radius: 20rpx;padding: 0 20rpx;">
			<u--form labelPosition="left" :model="formData" :rules="rules" ref="uForm">
				<u-form-item borderBottom label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;"></text> <text> {{clientName}}</text>
					  </view>
					 </template>
					<!-- <u--input placeholder="需求提交人" v-model="formData.submitterName" disabled></u--input> -->
					<view class="">
						{{companyName}}
					</view>
				</u-form-item>
				
				<u-form-item borderBottom label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text>活动评分</text>
					  </view>
					 </template>
					<view class="rate">
						<uni-rate :size="20" v-model="formData.pf" @change="onChange" active-color="#2A3A88FF"/>
					</view>
				</u-form-item>
				
				<u-form-item borderBottom prop="evaluate" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text>评价内容</text>
					  </view>
					 </template>
					 
					  <u--textarea placeholder="评价内容(200字符以内)" v-model="formData.evaluate" maxlength="200" placeholderStyle="font-size: 28rpx;color: #c0c4cc;"></u--textarea>
				</u-form-item>
				
			</u--form>
			
			<view class="u-p-10">
				<view class="u-p-20 bottombtn">
					<view class="stepbtn saveSub">
						<button style="color:#fff;background-color:#2A3A88FF;font-size: 30rpx;" @tap="submitNext" :loading="subloading">提交评价</button>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>


<script>
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default {
		data() {
			return {
				formData: {
					evaluate: '',
				    pf: 0
				},
				rules: {
					evaluate: {
						required: true,
						message: '请输入评价内容',
						trigger: ['blur', 'change']
					}
				},
				userInfo: {},
				clientName: '',
				companyName: '',
				estimateId: '',
				subloading: false
			}
		},
		onLoad(params) {
			console.log(params, '路由信息')
			if(params.estimateId) {
				this.estimateId = params.estimateId
			}
			this.userInfo = this.$store.state.user.userInfo
			console.log(this.userInfo, '当前用户信息')
			this.clientName = this.userInfo.nickName
			this.companyName = this.userInfo.companyName
		},
		methods: {
			onChange(e) {
				console.log(e, 'eee')
			},
			submitNext() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						if(this.subloading) {
							return
						}
						this.subloading = true
						setTimeout(() => {
							this.subloading = false
						}, 5000)
						if(this.formData.pf == 0) {
							uni.showToast({
								title: '请输入活动评分',
								icon:'none'
							})
							return
						}
						const params = {
							estimateId: this.estimateId,
							evaluate: this.formData.evaluate,
							pf: this.formData.pf
						}
						console.log(params, '提交参数')
						api({
							url: `rw/mb/cz/kh/pj/image`,
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: params
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									title: '评价成功',
									icon:'none'
								})
								setTimeout(() => {
									uni.navigateBack({
										success: () => {},
										fail: (err) => {
											uni.reLaunch({
												url:"/pages/home/<USER>"
											})
										}
									})
								}, 1000)
							} else {
								uni.showToast({
									title: res.msg,
									icon: "none"
								})
							}
						})
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	
	.page{
		.formData{
			.bottombtn{
				width: 92%;
				margin: 0 auto;
				margin-top: 20rpx;
				margin-bottom: 20rpx;
			}
		}
	}
</style>