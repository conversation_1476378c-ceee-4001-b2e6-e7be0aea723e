<template>
	<view class="page">
		<newNavBar :title="$t('search.spfl')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="cate-list" v-if="visible">
			<view class="cate-left">
				 <view class="cate-item" v-for="(item,index) in oneList" :key="index" :class="activeIndex == index ? 'active' : ''"
				       @click="changeActive(item, index)">{{item.name}}</view>
			</view>
			<view class="cate-right" v-if="twoList.length > 0">
			  <view class="twolist" v-for="(item, index) in twoList" :key="index">
				   <view class="twodetail">
					   <view class="twotitle">
						   {{item.name}}
					   </view>
					  <view class="threelist" v-if="item.subList.length > 0">
					   	<view class="threeitem" v-for="(threeitem, i) in item.subList" :key="i" @tap="itemThree(threeitem)">
					   		<view class="threedetail">
					   			<image :src="threeitem.imageUrl" mode="" style="width: 140rpx;height: 140rpx;"></image>
								<view class="threetitle">
									<text>{{threeitem.name}}</text>
								</view>
					   		</view>
					   	</view>
					   </view>
					   <view class="threelist" v-if="item.subList.length == 0">
							<view class="noDatalist">
								<image src="https://file.36sm.cn/xtjyjt/images/common/goods_null.png" mode=""></image>
								<view class="name">
									<!-- 暂无商品～ -->
									{{$t('search.zwsp')}}～
									</view>
							</view>
					   </view>
				   </view>
			   </view>
			</view>
			<view class="cate-right" v-else>
				<view class="noData">
					<image src="https://file.36sm.cn/xtjyjt/images/common/goods_null.png" mode=""></image>
					<view class="name">
						<!-- 暂无商品～ -->
						{{$t('search.zwsp')}}～
					</view>
				</view>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				oneList: [],
				activeIndex: 0,
				parentCodeId: "",
				oneName: '',
				twoList: [],
				visible: false,
			}
		},
		onShow() {
			// this.getOnelist()
		},
		onLoad() {
			this.getOnelist()
			// setTimeout(() => {
				// this.$data.visible = true
			// }, 3000)
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			// 获取分类列表
			getOnelist() {
				api({
					url: `bs/code/get/tree/list?location=${this.location}&page=1&size=20`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					console.log(res, '分类列表')
					if (res.code == 200) {
						this.$data.visible = true
						const data = res.data.data
						this.oneList = data
						console.log(this.oneList, 'oneList')
						if(this.oneList.length > 0) {
						   this.parentCodeId = this.oneList[0].codeId
						   this.oneName = this.oneList[0].name
						   this.twoList = this.oneList[0].subList
						}
					}else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			changeActive(item,index) {
				this.activeIndex = index
				this.oneName= item.name
				
				this.twoList = [...item.subList]
				console.log(item, '一级类目')
			},
			itemThree(item) {
				console.log(item, '三级', item.name)
				uni.navigateTo({
					url: `/pageo/search/searchA?keyvalue=${item.name}`
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
	  .cate-list {
	    display: flex;
	    width: 100%;
	    height: 100vh;
	    
	    .cate-left {
	      width: 200rpx;
	      overflow-y: auto; /* 使左侧可滚动 */
	      height: 100%; /* 确保左侧占满父容器的高度 */
	      
	      .cate-item {
	        width: 100%;
	        height: 90rpx;
			line-height: 90rpx;
	        border-radius: 0rpx 0rpx 0rpx 0rpx;
	        padding-left: 40rpx;
	        box-sizing: border-box;
	        font-family: PingFang SC, PingFang SC;
	        font-weight: 500;
	        font-size: 26rpx;
	        color: #777777;
	        font-style: normal;
	        text-transform: none;
	      }
	
	      .active {
	        position: relative;
	        padding-left: 40rpx;
	        box-sizing: border-box;
	        color: #FF4000;
	        font-family: PingFang SC, PingFang SC;
	        font-weight: bold;
	        font-size: 32rpx;
	        font-style: normal;
	        text-transform: none;
	
	        &::before {
	          content: '';
	          display: block;
	          width: 5rpx;
	          border-radius: 6rpx;
	          height: 40rpx;
	          background-color: #FF4000;
	          position: absolute;
	          left: 0rpx;
	          top: 30rpx;
	        }
	      }
	    }
	
	    .cate-right {
	      flex: 1; /* 右侧占据剩余空间 */
	      height: 100%;
	      overflow-y: auto; /* 使右侧内容区域可以滚动 */
	      padding: 10px;
		  width: 100%;
		  .noData{
			  height: 1000rpx;
			  display: flex;
			  flex-direction: column;
			  align-items: center;
			  justify-content: center;
			  image{
				  width: 300rpx;
				  height: 400rpx;
			  }
			  .name{
				  margin-top: 10rpx;
				  color: #ccc;
			  }
		  }
		  .twolist{
			background-color: #FFFFFF;
			width: 99%;
			margin: 0 auto;
			margin-bottom: 20rpx;
			border-radius: 15rpx;
			padding-top: 20rpx;
			.twodetail{
				.twotitle{
					height: 40rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #333333;
					line-height: 40rpx;
					font-style: normal;
					text-transform: none;
					margin-left: 20rpx;
				}
				.threelist{
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					.threeitem{
						width: 33.3%;
						margin: 20rpx 0 25rpx 0;
						.threedetail{
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							.threetitle{
								height: 40rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 26rpx;
								color: #333333;
								line-height: 40rpx;
								margin-top: 5rpx;
								font-style: normal;
								text-transform: none;
							}
						}
					}
					.noDatalist{
						width: 100%;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						image{
							width: 150rpx;
							height: 150rpx;
						}
						.name{
							margin: 10rpx 0;
							color: #ccc;
						}
					}
				}
			}
		  }
	    }
	  }
	}
</style>
