/* #ifdef H5 */
body {
    background: #333 !important;
}

page {
    min-height: 100%;
}

a {
    text-decoration: none;
}

/**
 * 页面宽度最大800px、并居中
 */
page,
iframe,
.uni-page-head,
.uni-tabbar,
.open-location,
.home-top-nav-content,
.search-content-fixed,
.trn-nav-top,
.goods-buy-nav,
.popup-bottom,
.popup-top,
.quick-movable-container,
.online-service-movable-container,
.bottom-fixed,
.buy-nav,
.plugins-popupscreen .content,
.cart-buy-nav,
.bg-img,
.pa-w {
    max-width: 800px !important;
    margin: 0 auto !important;
}

.trn-nav-top,
.goods-buy-nav,
.nav-more-view,
.quick-movable-container,
.online-service-movable-container,
.buy-nav,
.cart-buy-nav {
    left: auto !important;
}

iframe,
.bottom-fixed {
    right: 0 !important;
}

.web-html-content iframe {
    width: 100% !important;
    max-height: 500rpx !important;
}

/**
 * 隐藏导航栏内容、仅展示返回按钮
 */
uni-page-head {
    position: absolute;
    margin-top: -999999px;
    z-index: 99;
}

.uni-page-head-bd {
    display: none !important;
}

.uni-page-head-btn {
    position: fixed;
    left: auto;
    bottom: 70px;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: 0 0 10px rgb(0 0 0 / 30%);
    margin: 0 0 0 10px;
    border-radius: 50px;
    width: 31px;
    height: 31px;
    text-align: center;
    padding: 2px 3px 0 0;
}

.uni-page-head-btn .uni-btn-icon {
    color: #fff !important;
}

.uni-app--showtabbar uni-page-wrapper,
uni-page-head[uni-page-head-type=default]~uni-page-wrapper {
    height: calc(100% - var(--window-bottom)) !important;
}

/* #endif */

/**
 * 公共样式、框架样式覆盖
 */
page {
    background: #f5f5f5;
    color: #4a4a4a;
}

input[type="text"],
input[type="number"],
input[type="idcard"],
input[type="digit"],
textarea {
    -webkit-appearance: none;
    border-radius: 5px;
    box-sizing: border-box;
}

page,
textarea,
.a-textarea-control textarea {
    font-size: 28rpx;
}

button:after,
button:before {
    border: 0;
    border-radius: 0;
}

/**
 * 模块分割间距
 */
.spacing {
    padding-top: 20rpx;
}

.spacing-mt-10 {
    margin-top: 20rpx;
}

.spacing-mb-10 {
    margin-bottom: 20rpx;
}

.spacing-mb {
	/* border-bottom: 1rpx solid #E8E8E8; */
	/* border-radius: 0 !important; */
}

.spacing-mt {
    margin-top: 24rpx;
}

/**
 * 常用样式
 */
.max-w {
    max-width: 100%;
}

.max-h {
    max-height: 100%;
}

.min-w {
    min-width: 100%;
}

.min-h {
    min-height: 100%;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.wh-auto {
    width: 100% !important;
}

.ht-auto {
    height: 100% !important;
}

.tc {
    text-align: center;
}

.tl {
    text-align: left;
}

.tr {
    text-align: right;
}

.oh {
    overflow: hidden;
}

.dis-none {
    display: none !important;
}

.dis-block {
    display: block !important;
}

.dis-inline {
    display: inline !important;
}

.dis-inline-block {
    display: inline-block !important;
}

.va-t {
    vertical-align: top;
}

.va-m {
    vertical-align: middle;
}

.va-b {
    vertical-align: text-bottom;
}

.pf {
    position: fixed;
}

.pa {
    position: absolute;
}

.pr {
    position: relative;
}

.z-i {
    z-index: 1 !important;
}

.z-i-deep {
    z-index: 2 !important;
}

.bs-bb {
    box-sizing: border-box;
}

.radius-0 {
    border-radius: 0 !important;
}

.radius {
    border-radius: 10rpx !important;
}

.round {
    border-radius: 10rpx !important;
}

.circle {
    border-radius: 50% !important;
}

.fw-b {
    font-weight: 500;
}

.auto {
    margin: 0 auto;
}

.lh-0 {
    line-height: 0 !important;
}

.cp {
    /* #ifdef H5 */
    cursor: pointer;
    /* #endif */
}

/**
 * 透明度
 */
.opacity-min {
    opacity: 0.3;
}

.opacity {
    opacity: 0.6;
}

.opacity-max {
    opacity: 0.8;
}

/**
 * 底部浮动
 */
.bottom-fixed {
    position: fixed;
    left: 0;
    border: none;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    z-index: 2;
    padding: 40rpx 90rpx;
    background-color: #fff;
    box-shadow: inset 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
}

/**
 * 兼容底部横线底部内边距
 */
.bottom-line-exclude {
    padding-bottom: calc(env(safe-area-inset-bottom) - 40rpx);
}

/**
 * 页面底部浮动、增加底部边距
 */
.page-bottom-fixed {
    padding-bottom: calc(172rpx + env(safe-area-inset-bottom) - 40rpx) !important;
    /* #ifdef H5 */
    padding-bottom: 172rpx !important;
    /* #endif */
}

/**
 * 主要类样式
 */
.margin-main {
    margin: 24rpx;
}

.margin-top-main {
    margin-top: 24rpx;
}

.margin-right-main {
    margin-right: 24rpx;
}

.margin-bottom-main {
    margin-bottom: 24rpx;
}

.margin-left-main {
    margin-left: 24rpx;
}

.margin-horizontal-main {
    margin-left: 24rpx;
    margin-right: 24rpx;
}

.margin-vertical-main {
    margin-top: 24rpx;
    margin-bottom: 24rpx;
}

.padding-main {
    padding: 24rpx;
}

.padding-top-main {
    padding-top: 24rpx;
}

.padding-right-main {
    padding-right: 24rpx;
}

.padding-bottom-main {
    padding-bottom: 4rpx;
}

.padding-left-main {
    padding-left: 24rpx;
}

.padding-horizontal-main {
	margin-top: 24rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
}

.padding-vertical-main {
    padding-top: 24rpx;
    padding-bottom: 24rpx;
}

.border-radius-main {
    border-radius: 16rpx;
}

.border-radius-left-main {
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
}

.border-radius-right-main {
    border-top-right-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
}

.border-radius-top-main {
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
}

.border-radius-bottom-main {
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
}

.border-radius-xs {
    border-radius: 4rpx;
}

.border-radius-left-xs {
    border-top-left-radius: 4rpx;
    border-bottom-left-radius: 4rpx;
}

.border-radius-right-xs {
    border-top-right-radius: 4rpx;
    border-bottom-right-radius: 4rpx;
}

.border-radius-top-xs {
    border-top-left-radius: 4rpx;
    border-top-right-radius: 4rpx;
}

.border-radius-bottom-xs {
    border-bottom-left-radius: 4rpx;
    border-bottom-right-radius: 4rpx;
}

.border-radius-sm {
    border-radius: 8rpx;
}

.border-radius-left-sm {
    border-top-left-radius: 8rpx;
    border-bottom-left-radius: 8rpx;
}

.border-radius-right-sm {
    border-top-right-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
}

.border-radius-top-sm {
    border-top-left-radius: 8rpx;
    border-top-right-radius: 8rpx;
}

.border-radius-bottom-sm {
    border-bottom-left-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
}

/**
 * 价格
 */
.sales-price {
    color: #0C71FD;
    font-weight: bold;
    font-size: 28rpx;
}
.sales-pay {
	color: #8B8B8B;
    font-size: 22rpx;
}

.original-price {
    color: #999;
    font-size: 24rpx;
    text-decoration: line-through;
}

/**
 * 边框 灰色（四边）、红色、黄色、绿色、灰色、无边线
 */
.br {
    border: 1px solid #EEEEEE !important;
}

.br-b {
    border-bottom: 1px solid #EEEEEE !important;
}

.br-t {
    border-top: 1px solid #EEEEEE !important;
}

.br-l {
    border-left: 1px solid #EEEEEE !important;
}

.br-r {
    border-right: 1px solid #EEEEEE !important;
}

.br-f5 {
    border: 1px solid #f5f5f5 !important;
}

.br-b-f5 {
    border-bottom: 1px solid #f5f5f5 !important;
}

.br-t-f5 {
    border-top: 1px solid #f5f5f5 !important;
}

.br-l-f5 {
    border-left: 1px solid #f5f5f5 !important;
}

.br-r-f5 {
    border-right: 1px solid #f5f5f5 !important;
}


.br-f9 {
    border: 1px solid #f9f9f9 !important;
}

.br-b-f9 {
    border-bottom: 1px solid #f9f9f9 !important;
}

.br-t-f9 {
    border-top: 1px solid #f9f9f9 !important;
}

.br-l-f9 {
    border-left: 1px solid #f9f9f9 !important;
}

.br-r-f9 {
    border-right: 1px solid #f9f9f9 !important;
}

.br-e {
    border: 1px solid #eeeeee !important;
}

.br-b-e {
    border-bottom: 1px solid #eeeeee !important;
}

.br-t-e {
    border-top: 1px solid #eeeeee !important;
}

.br-l-e {
    border-left: 1px solid #eeeeee !important;
}

.br-r-e {
    border-right: 1px solid #eeeeee !important;
}

.br-red {
    border: 1px solid #E22C08 !important;
}

.br-yellow {
    border: 1px solid #f6c133 !important;
}

.br-green {
    border: 1px solid #52C41A !important;
}

.br-grey {
    border: solid 1px #dddddd !important;
}

.br-grey-d {
    border: solid 1px #dddddd !important;
}

.br-grey-f5 {
    border: solid 1px #f5f5f5 !important;
}

.br-grey-9 {
    border: solid 1px #999999 !important;
}
.br-grey-c {
    border: solid 1px #cccccc !important;
}

.br-blue {
    border: solid 1px #2196F3 !important;
}

.br-black {
    border: solid 1px #333333 !important;
}

.br-base {
    border: solid 1px #666 !important;
}

.br-0 {
    border: 0 !important;
    box-shadow: none;
}

/**
 * 虚线边框、灰色（四边）、红色、黄色、绿色、灰色
 */
.br-dashed {
    border: dashed 1px #EEEEEE !important;
}

.br-b-dashed {
    border-bottom: dashed 1px #EEEEEE !important;
}

.br-t-dashed {
    border-top: dashed 1px #EEEEEE !important;
}

.br-l-dashed {
    border-left: dashed 1px #EEEEEE !important;
}

.br-r-dashed {
    border-right: dashed 1px #EEEEEE !important;
}

.br-dashed-red {
    border: dashed 1px #E22C08 !important;
}

.br-dashed-yellow {
    border: dashed 1px #f6c133 !important;
}

.br-dashed-green {
    border: dashed 1px #1AAD19 !important;
}

.br-dashed-grey {
    border: dashed 1px #d6d6d6 !important;
}

/**
 * 统一颜色
 * 文本基础色、白色、黑色、灰色、浅灰色、红色、黄色、绿色、蓝色
 */
.cr-base {
    color: #666 !important;
}

.cr-white {
    color: #fff !important;
}

.cr-black {
    color: #333 !important;
}

.cr-grey,
.cr-grey-9 {
    color: #999 !important;
}

.cr-grey-c {
    color: #777 !important;
    font-size: 32rpx;
    letter-spacing: 2rpx !important;
}

.cr-grey-d {
    color: #ddd !important;
}

.cr-grey-white {
    color: #e7e7e7 !important;
}

.cr-red {
    color: #E22C08 !important;
}

.cr-yellow {
    color: #f6c133 !important;
}

.cr-green {
    color: #1AAD19 !important;
}

.cr-blue {
    color: #2196F3 !important;
}

/**
 * 统一背景色
 * 白色、绿色、红色、黄色、灰色
 */
.bg-white {
    background-color: #fff !important;
}

.bg-green {
    background-color: #1AAD19 !important;
}

.bg-red {
    background-color: #E64340 !important;
}

.bg-yellow {
    background-color: #f6c133 !important;
}

.bg-grey {
    background-color: #d6d6d6 !important;
}

.bg-grey-d {
    background-color: #dddddd !important;
}

.bg-grey-e {
    background-color: #eeeeee !important;
}

.bg-grey-f5 {
    background-color: #f5f5f5 !important;
}

.bg-grey-f8 {
    background-color: #f8f8f8 !important;
}

.bg-grey-f9 {
    background-color: #f9f9f9 !important;
}

.bg-base {
    background-color: #f5f5f5 !important;
}

.bg-blue {
    background-color: #2196F3 !important;
}

.bg-black {
    background-color: #333333 !important;
}

button[disabled].bg-white {
    background-color: #fdfdfd !important;
    color: #ccc !important;
}

button[disabled].bg-green {
    background-color: #94de94 !important;
    color: #d5f5d5 !important;
}

button[disabled].bg-red {
    background-color: #ffbaba !important;
    color: #ffdfdf !important;
}

button[disabled].bg-yellow {
    background-color: #fff1cb !important;
    color: #fbd777 !important;
}

button[disabled].bg-grey {
    background-color: #ececec !important;
    color: #ababab !important;
}

/**
 * 表单
 */
.form-container .form-gorup {
    padding: 24rpx;
}

.form-container .form-gorup .form-gorup-item-left {
    float: left;
    padding-right: 20rpx;
}

.form-container .form-gorup .form-gorup-item-right {
    float: right;
    padding-left: 20rpx;
    border-left: 1px solid #f7f7f7;
}

.form-container .form-gorup .form-gorup-item-left,
.form-container .form-gorup .form-gorup-item-right {
    width: calc(50% - 20rpx - 1px);
}

.form-container .form-gorup-submit {
    padding: 20rpx 0;
}

.form-container .form-gorup:not(.form-gorup-submit) {
    background: #fff;
}

.form-container .form-group-tips,
.form-container .form-group-tips-must {
    margin-left: 6rpx;
    font-size: 24rpx;
    color: #ccc;
}

.form-container .form-group-tips-must {
    color: #f00;
}

.form-container .form-gorup input,
.form-container .form-gorup textarea,
.form-container .form-gorup .picker {
    border-radius: 0;
    width: 100%;
    box-sizing: border-box;
    font-size: 28rpx;
}

.form-container .form-gorup input,
.form-container .form-gorup .picker,
.form-container .form-gorup .radio-gorup {
    height: 70rpx;
    line-height: 70rpx;
}

.form-container .form-gorup textarea {
    padding: 0;
    height: 150rpx !important;
    margin-top: 20rpx;
}

.form-container .form-gorup-text {
    padding: 20rpx 10rpx;
}

.form-container .form-gorup .switch {
    margin: 30rpx 0 20rpx 0;
}

.form-container .form-gorup .radio-gorup .radio radio {
    transform: scale(0.7);
}

.form-container .form-gorup .radio-gorup .radio:not(:last-child) {
    margin-right: 50rpx;
}

/**
 * 表单图片上传
 */
.form-container-upload .form-gorup-title {
    margin-bottom: 20rpx;
}

.form-container-upload .form-upload-data .item {
    margin: 10rpx 10rpx 0 0;
    position: relative;
}

.form-container-upload .form-upload-data .delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    color: #e5e5e5;
    background-color: #d9534f;
    padding: 5rpx 18rpx;
    font-size: 36rpx;
    border: 1px solid #eee;
    border-radius: 50rpx;
    line-height: 44rpx;
    z-index: 1;
}

.form-container-upload .form-upload-data image {
    width: 140rpx;
    height: 140rpx;
    padding: 5rpx;
    border: 1px solid #eee;
    display: block;
    border-radius: 10rpx;
}

/**
 * 箭头符号
 */
.arrow-right {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGqADAAQAAAABAAAAGgAAAABMybYKAAACJElEQVRIDWNgGAWDPQQYCTkwNTXVhZmZ2RKIp02dOvUtIfW45JlwSSCJp3NwcDT9/ft3S0ZGhg6SOElMghYBfdL47du306ysrBZAk3empaUFkGQDVDEzIU1nzpx5ZWxsvP7////SQEutgXSIiYnJV19f35MHDhz4T0g/TJ6gRSCFZ8+e/aaoqLiRjY3tDxMTkx3QQs9Pnz7JmJmZHQY65AfMMHw0wcSArhmYOIJBCQMYlGK/fv06ArQ4Zfr06TfR1aHzifIRsqZz585dNzU13f/7928LdnZ2U2Ai8QMG7Q2gr+8gq0Nnk+wjmAFZWVkSf/78mQL0WTCQBgVfhaSk5OSGhoZ/MDXINMk+gmk+ffr0F15e3nWCgoL/gMFnD8TeX758YQXG2T6YGmSaYPJGVozOBqa6P8BUeB0o/h0Ybwz//v1TQFcD47PAGKTSoaGhzEDf1AJ9Us3IyMgCjLO5wPgqw2UOWRbl5uaKAlPcZBYWlnBg/PwCGl4yY8aMPqCFOPMVyYkBWDLoAg1eAExxRkBfPAUGXebMmTM34/IJTJwki9LT0/2BrgblISmgj44DDUkBWnINZhg+miiLgEmW6dmzZ2VASxqBwcUGDK5FQLqIlNKcoEXJyclCQB/0AVNVPNCCv0BXN7x796599erVIDbRgGBiALp8IjA+Yn78+PESaGoOMKjWEG06kkKC+QgY2Zd//vy5DZhHPMi1BMm+UeYgDgEAf6bHwBiHBjoAAAAASUVORK5CYII=');
    background-size: 26rpx 26rpx;
    background-repeat: no-repeat;
    background-position: center right;
}

.arrow-bottom {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABRklEQVRYR+2UsUrFMBSGT9LBN3AQR4vQNO0gPoA6Cy5XZ1ddfZbr6nwdXFwVH8AlJacg2cTFN7BLjkQQwtW2STvcJRlL8/8fX3LCYMOLbbgfEkAykAwkA4MGEHEhhLif81gZY3bzPP/oy+gFQMSCiJCIjqSUL1MgjDFbXdd9AcB1WZbL/zIGDWitVwCwmAKhlNrOsuyTMYYAcC6EaKMB3IZfCMbYiRDiOcQEIu4RkRkrd1lBU+CZOJVSPg5BNE1zwDl/DSkPBvBNENGFlNIdzZ+FiMdE9BRaHgXgQ1hrL6uquvMJ2rY9s9Y+xJRHA/gQnPOroihu3Tc3rkS0ii2fBLB2MW8A4H1q+WQAH+InZGTUhi5t0BT0BWitDQDsMMYO++Z8bGxnAbhwpdR+XddvY0XRT/HUwNh9sw3EFq7/nwCSgWQgGfgGWR6PIRYTYskAAAAASUVORK5CYII=');
    background-size: 38rpx 38rpx;
    background-repeat: no-repeat;
    background-position: center right;
}

/**
 * 信息提示、加载页面图片
 */
.data-loding image {
    width: 120rpx;
    height: 120rpx;
    background-size: 80% 80% !important;
}

/**
 * 元素漂出可视页面
 */
.drift {
    position: fixed;
    left: -1000px;
}

/**
 * 导航分割
 */
.spacing-nav-title {
    margin-bottom: 10rpx
}

.spacing-nav-title .text-wrapper {
    display: inline-block;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/**
 * 关键字标签展示
 */
.word-list .word-icon {
    border-radius: 4rpx;
    border: 2rpx solid #FFCAC0;
    color: #FFCAC0;
    padding: 0 12rpx;
}

.word-list .word-icon:not(:last-child) {
    margin-right: 20rpx;
}

.word-list image {
    max-width: 100%;
    margin: 0 auto;
}

/**
 * 文字超出部分使用省略号
 */
.single-text {
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
}

.multi-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 76rpx;
    line-height: 38rpx;
    font-size: 28rpx;
    white-space: initial;
}

/**
 * 基础导航
 */
.nav-base {
    height: 80rpx;
    line-height: 80rpx;
}

.nav-base .item {
    /* #ifdef H5 */
    cursor: pointer;
    /* #endif */
}

/**
 * 导航下划线
 */
.nav-active-line {
    position: relative;
}

.nav-active-line::before {
    content: '';
    width: 12px;
    height: 2px;
    background: transparent;
    border-radius: 1px;
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
}

/**
 * 滚动标签高度
 */
.scroll-box {
    height: 100vh;
}

/**
 * 存在基础导航
 */
.scroll-box-ece-nav {
    height: calc(100vh - 80rpx);
}

/**
 * 滚动水平左右滑动
 */
.scroll-view-horizontal {
    white-space: nowrap;
    box-sizing: border-box;
    width: 100%;
}

/**
 * 滚动水平左右滑动内容宽度处理
 */
.rolling-horizontal .scroll-view-horizontal {
    width: calc(100% + 180rpx);
}

/**
 * 公共操作栏按钮样式
 */
.item-operation button:not(:first-child) {
    margin-left: 20rpx;
}

.item-operation button {
    padding: 0 35rpx;
    height: 58rpx;
    line-height: 58rpx;
    display: inline-flex;
}

/**
 * 单页预览
 */
.single-page-top {
    padding-top: 110rpx;
}

/**
 * 上一篇、下一篇
 */
.last-next-data navigator {
    width: calc(100% - 120rpx);
}

/**
 * 商品列表
 */
.goods-list .cart-badge-icon {
    top: -18rpx;
    right: 8rpx;
}

.goods-list .error-msg {
    position: absolute;
    top: 0;
    left: 0;
    background: rgb(253 253 253 / 60%);
    z-index: 1;
    filter: blur(0.3px);
}

.goods-data-grid-list .item .error-msg,
.goods-data-rolling-list .item .error-msg {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

/**
 * 商品列表 - 列表
 */
.goods-data-list .error-msg,
.goods-data-list .goods-img {
    width: 190rpx !important;
    height: 190rpx !important;
}

.goods-data-list .base {
    width: calc(100% - 220rpx);
}

.goods-data-list .base .base-bottom {
    width: calc(100% - 260rpx);
}

.goods-data-list .error-msg {
    top: 24rpx;
    left: 24rpx;
    line-height: 190rpx;
}

/*
 * 商品列表 - 滚动
 */
.goods-data-rolling-list .swiper {
    height: 310rpx !important;
}

.goods-data-rolling-list .item .error-msg,
.goods-data-rolling-list .item .goods-img {
    height: 210rpx !important;
}

.goods-data-rolling-list .item .error-msg {
    line-height: 210rpx;
}

/**
 * 商品列表 - 九方格
 */

.goods-data-grid-list .item {
    width: calc(50% - 12rpx);
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
}

.goods-data-grid-list .item:nth-of-type(2n + 1) {
    margin-right: 12rpx;
}

.goods-data-grid-list .item:nth-of-type(2n) {
    margin-left: 12rpx;
}

.goods-data-grid-list .item .error-msg {
    line-height: 341rpx;
}

/**
 * 头部客服信息弹窗
 */
/**
* 客服
*/
.header-service {
    width: 400rpx;
    left: 80rpx;
    top: 210rpx;
    z-index: 2;
    font-size: 24rpx;
    -webkit-box-shadow: 0 10px 10px rgb(0 0 0 / 30%);
    box-shadow: 0 10px 10px rgb(0 0 0 / 30%);
}

.header-service .item:first-child {
    border-top: 0 !important;
}

.header-service .qrcode .item:not(:last-child) {
    margin-right: 20rpx;
}

.header-service image {
    width: 130rpx;
    height: 130rpx;
}

.header-service .chat-info image {
    width: 40rpx;
    height: 40rpx;
}

/**
 * flex 布局
 */
.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.jc-sb {
    justify-content: space-between;
}

.jc-c {
    justify-content: center;
}

.jc-sa {
    justify-content: space-around;
}

.jc-e {
    justify-content: end;
}

.align-c {
    align-items: center;
}

.align-e {
    align-items: flex-end;
}

.align-s {
    align-items: flex-start;
}

.align-b {
    align-items: baseline;
}

.flex-warp {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.flex-1 {
    flex: 1;
}
.flex-2 {
    flex: 2;
}
.flex-3 {
    flex: 3;
}

.self-c {
    align-self: center;
}

.self-b {
    align-self: baseline;
}

.self-s {
    align-self: flex-start;
}

.self-e {
    align-self: flex-end;
}

.flex-width {
    width: 0;
}

.flex-width-half {
    width: 50%;
}

/**
 * css伪类文本之间的分割线 ｜ ——
*/
.divider-l,
.divider-r,
.divider-t,
.divider-b,
.divider-l-f5,
.divider-r-f5,
.divider-t-f5,
.divider-b-f5,
.divider-l-d,
.divider-r-d,
.divider-t-d,
.divider-b-d {
    position: relative;
}

.divider-l-f5::before,
.divider-r-f5::after,
.divider-t-f5::before,
.divider-b-f5::after {
    background: #f5f5f5 !important;
}

.divider-l-d::before,
.divider-r-d::after,
.divider-t-d::before,
.divider-b-d::after {
    background: #dddddd !important;
}

.divider-l::before,
.divider-l-f5::before,
.divider-l-d::before {
    content: '';
    width: 2rpx;
    height: 65%;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #EEEEEE;
}

.divider-r::after,
.divider-r-f5::after,
.divider-r-d::after {
    content: '';
    width: 2rpx;
    height: 65%;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: #EEEEEE;
}

.divider-t::before,
.divider-t-f5::before,
.divider-t-d::before {
    content: '';
    width: 100%;
    height: 2rpx;
    position: absolute;
    top: 0;
    background: #EEEEEE;
}

.divider-b::after,
.divider-b-f5::after,
.divider-b-d::after {
    content: '';
    width: 100%;
    height: 2rpx;
    position: absolute;
    bottom: 0;
    background: #EEEEEE;
}

/**
 * 标题左侧边线
*/
.title-left-border {
    position: relative;
    padding-left: 20rpx;
}

.title-left-border::before {
    content: '';
    width: 8rpx;
    height: 32rpx;
    border-radius: 6rpx;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

/**
 * 导航菜单左侧边线
 */
.nav-left-border {
    position: relative;
    padding-left: 20rpx;
}

.nav-left-border::before {
    content: '';
    width: 6rpx;
    height: 40rpx;
    border-radius: 6rpx;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

/**
 * 细微偏移量
 */
.top-0 {
    top: 0;
}

.top-xs {
    top: 2rpx;
}

.top-sm {
    top: 4rpx;
}

.top-md {
    top: 6rpx;
}

.top-lg {
    top: 8rpx;
}

.top-xl {
    top: 10rpx;
}

.left-0 {
    left: 0;
}

.left-xs {
    left: 2rpx;
}

.left-sm {
    left: 4rpx;
}

.left-md {
    left: 6rpx;
}

.left-lg {
    left: 8rpx;
}

.left-xl {
    left: 10rpx;
}

.bottom-0 {
    bottom: 0;
}

.bottom-xs {
    bottom: 2rpx;
}

.bottom-sm {
    bottom: 4rpx;
}

.bottom-md {
    bottom: 6rpx;
}

.bottom-lg {
    bottom: 8rpx;
}

.bottom-xl {
    bottom: 10rpx;
}

.right-0 {
    right: 0;
}

.right-xs {
    right: 2rpx;
}

.right-sm {
    right: 4rpx;
}

.right-md {
    right: 6rpx;
}

.right-lg {
    right: 8rpx;
}

.right-xl {
    right: 10rpx;
}

/**
 * 呼吸
 */
/* <div class="breathe"></div> */
.breathe {
    background-color: #FF7C74;
    border-radius: 100%;
    width: 12rpx;
    height: 12rpx;
    position: relative;
}

.breathe::before,
.breathe::after {
    content: '';
    background-color: rgba(255, 124, 116, 0.7);
    border-radius: 50%;
    position: absolute;
    animation: loading 2s infinite ease-in-out;
}

.breathe::before {
    width: 12px;
    height: 12px;
    top: -3px;
    left: -3px;
}

.breathe::after {
    width: 18px;
    height: 18px;
    top: -6px;
    left: -6px;
}

@keyframes loading {
    0% {
        opacity: 0.5;
        -webkit-transform: scale(1);
    }

    50% {
        opacity: 0.7;
        -webkit-transform: scale(1.12);
    }

    100% {
        opacity: 0.5;
        -webkit-transform: scale(1);
    }
}

/**
 * 扩散效果
 */
/* <div class="spread margin-left-xxxl">
	<div class="ring"></div>
	<div class="ring"></div>
	<div class="ring"></div>
</div> */
.spread {
    background-color: #FF7C74;
    height: 16rpx;
    width: 16rpx;
    border-radius: 100%;
    position: relative;
    z-index: 1;
}

.spread .ring {
    position: absolute;
    background-color: inherit;
    height: 100%;
    width: 100%;
    border-radius: 100%;
    opacity: .8;
    /* 速度为1.5 * 层数 = 实际运行速度，速度修改则 animation-delay 属性也修改相同速度 */
    animation: pulsing 4.5s ease-out infinite;
}

/* 速度为1*层数 */
.spread .ring:nth-of-type(1) {
    -webkit-animation-delay: -1.5s;
    animation-delay: -1.5s;
}

/* 速度为1*层数 */
.spread .ring:nth-of-type(2) {
    -webkit-animation-delay: -3s;
    animation-delay: -3s;
}

@keyframes pulsing {
    100% {
        transform: scale(3);
        opacity: 0
    }
}

/**
 * 通知
 */
.uni-noticebar {
    padding: 0 !important;
    margin: 0 !important;
}


/**
 * 顶部搜索按钮宽度
 */
.top-search-width {
    width: calc(100% - 250rpx) !important;
}

/**
 * 小程序端距离顶部位置的控制
 */
.weixin-nav-padding-top {
    /* #ifndef H5  */
    padding-top: calc(var(--status-bar-height) + 5px);
    /* #endif */
}
.home-over, .branded-space-over, .derivative-over {
	overflow: hidden;
}