<template>
	<view class="page">
		<TabBar></TabBar>
	</view>
</template>

<script>
	import {
		postCancelPaymentOrder,
		getShoppingOrder,
		getTypeOrder
	} from "@/common/request/api/goods.js"
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import TabbarCom from '../../components/tabbar/tabbar.vue'
	export default {
		data() {
			return {
				
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright,
			TabbarCom,
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
			isOk(){
				return this.$store.state.tabbar.isOk
			}
		},
		created() {
		},
		onLoad(opt) {
			console.log(opt, '路径参数')
			this.getProType()
		},
		onShow() {
			uni.navigateTo({
				url: 'pageg/shop_detail/index?placeId=1891758887364456448'
			})
		},
		methods: {
			
			getProType() {
				//获取数据
				const dataList = {
					parentCodeIdList: ['1864125893367148544']
				}
				api({
					url: `tx/code/get/sub/list/map`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: dataList
				}).then(res => {
					console.log(res, '产品分类')
					if (res.code == 200) {
						console.log(res);
						const data = res.data.data
						console.log(data, '产品分类')
						this.subsectionList = data.A1864125893367148544
						console.log(this.subsectionList, '产品分类')
						this.schemeName = this.subsectionList[0].name
						console.log(this.schemeName);
						this.schemeListData()
					} else {
						uni.showToast({
							title: res.msg,  
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
				},
				schemeListData() {
					api({
						url: `tx/product/get/list?page=${this.params.page}&size=${this.params.size}&typeInfo=${this.schemeName}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'get',
					}).then(res => {
						setTimeout(() => {
							this.visible = true
						}, 1200)
						if (res.code == 200) {
							const data = res.data.data
							if(this.prolist.length == data.totalCount) {
								setTimeout(() => {
									this.loadingStatus = false;
									this.bottom_line_status = true
								}, 500)
							} else {
								if(this.params.page == 1) {
									this.prolist = [...data.list]
									if(this.prolist.length == data.totalCount) {
										this.bottom_line_status = true
									}
								} else {
									this.prolist = [...this.prolist, ...data.list]
								}
								setTimeout(() => {
									this.loadingStatus = false;
								}, 500)
								
								console.log(this.prolist, '产品列表')
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none"
							})
						}
					}).catch(err => {
						uni.showToast({
							title: err.msg,
							icon: "none"
						})
					})
				},
				queryList() {
					// this.prolist = []
					this.params.page = 1
					this.params.size = 10
					this.schemeListData()
					setTimeout(() => {
						this.$refs.paging.complete(false);
					}, 1000)
				},
		
			searchOrder() {
				console.log('搜索操作')
			},
		},
	}
</script>

<style>
	.page {
		/* background-color: #fff; */
		background-color: #eef6ff;
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	
	
</style>
