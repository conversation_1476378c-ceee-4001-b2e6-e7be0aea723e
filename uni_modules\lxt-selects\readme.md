## 介绍
基于uni-app开发的一个少见的下拉多选组件、可以支持输入选择和搜索
已用于生产环境  如有自定义 和bug 可联系我免费维护

## table 属性
| 参数 | 说明 | 类型 | 可选值 | 默认值 |是否必须|
| ------ | ------ | ------ | ------ | ------ |------ |
| scrollHeight | 下拉款高度 | String |-- | 400rpx |否 |
| partlist | 显示的列数据 | array |-- | -- |必须 |
| value | 选择的值 | String | - | -- | 必须 |
| disabled | 是否禁用 | Boolean | - | false | 否 |
| inputHeight | 输入框高度是否动态 | Boolean | - | false | 否 |

## table 事件
| 参数 | 说明 | 类型 | 可选值                      | 默认值 |是否必须|
| ------ | ------ | ------ |--------------------------| ------ |------ |
| 事件名自定义 | 取决于type类型为operation的 renders参数里面 func 的参数名 | Function | (row,index)=>{}          | -- |否 |
| change | 数据改变触发 | Function | (val)=>{} | -- |否 |

## data 属性
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| ------ | ------ | ------ | ------ | ------ |
| value | key值 | String |true | 无 |
| text | 显示名称 | String |true | 无 |
| active | 选中的状态 | Boolean |true | false |

## 示例
```
<lxt-selects 
:partlist="partlist" 
:disabled="false" 
scrollHeight:"400rpx"
v-model="value">
</lxt-selects>
```
## 数据格式
```
partlist:[
	{
		value:101 ,
		text:"深圳",
		active: false,
	},
	{
		value:102 ,
		text:"成都",
		active: false,
	},
	{
		value:103 ,
		text:"北京",
		active: false,
	},
	{
		value:104 ,
		text:"上海",
		active: false,
	}
]
     
```
