<template>
	<!-- 瀑布流组件 -->
	<view class="waterfall">
		<view class="loadingLayout" v-if="!RecommendList.length && !noData">
			 <uni-load-more status="'loading'"></uni-load-more>
		</view>
		
		<view class="waterfallCon">
			<view class="waterfall_item" v-for="(item,index) in RecommendList" :key="item.productCode">
				<view class="waterdetail"  @click="Prodetail(item)">
					<view class="imageView">
						<image :src="item.recommendImageUrlSquare" mode="" @load="onoff='1'" v-show="onoff=='1'"></image>
						<image src="https://file.36sm.cn/xtjyjt/images/common/default.png" mode="" v-show="onoff=='0'" style="height: 380rpx;width: 100%;"></image>
						<image src="https://file.36sm.cn/xtjyjt/images/common/videoB.png" mode="" class="video" style="width: 100rpx; height: 100rpx;"></image>
					</view>
					<view class="good_detail">
						<view class="detail_title">
							{{item.productTitle}}
						</view>
						<view class="saledetail">
							<view class="value">
								{{item.salesVolume}}
							</view>
							<view class="title">
								{{item.recommendReason}}
							</view>
							<image src="https://file.36sm.cn/xtjyjt/images/common/videoA.png" style="width: 22rpx;height: 22rpx;" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- #ifndef MP-WEIXIN -->
			<!-- <view v-if="videoPlay" style="margin-top: 200rpx;">
			  <video controls id="myvideo" autoplay :src="videoUrl" @fullscreenchange="screenChange"  ref="videoPlayer"></video>
			</view> -->
		<!-- #endif -->
	</view>
</template>

<script>
	const app = getApp();
	import {
		getRecommend
	} from "@/common/request/api/home.js"
	import {
			onLoad,
			onShow,
			onReachBottom
		} from "@dcloudio/uni-app"
	export default {
		data() {
			return {
				onoff: 0,
			    theme_view: app.globalData.get_theme_value_view(),
				// RecommendList: [],
				leftdata: [],
				rightdata: [],
				noData: false,
				classifyList: [],
				sliderList: [
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/13/19162898ab664f72aabdb97b172fb1bb.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/12/2aaaccaab3db405292c8de5f0536423d.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/11/f338e7b4f8b740ee8a0eb79532edd6df.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/11/53fb4099bce24386822b13284f6fe9f0.png"
				],
				videoPlay: false,
				videoUrl: '',
				videoContext: null
			}
		},
		components: {
		},
		props: {
			RecommendList: {
				type: Array
			},
			CanClick: {
				type:Boolean,
				default: true
			}
		},
		computed: {
		},
		onReachBottom() {
			console.log('触底了')
		},
		methods: {
			screenChange(e) {
			  let fullScreen = e.detail.fullScreen; // 值true为进入全屏，false为退出全屏
			  console.log(e, "全屏");
			  if (!fullScreen) {
			    //退出全屏
			    this.videoPlay = false;   // 隐藏播放盒子
			  }
			},
			// this这个是实例对象 必传
			Prodetail(item) {
				console.log(item)
				uni.navigateTo({
					// url: `/pages/branded-space/videodetail?videourl=${JSON.stringify(item.tradeCoinName)}`
					url: `/pages/branded-space/videodetail?videourl=${item.tradeCoinName}`
				})
				
				// uni.navigateTo({
				// 	url: '/pageo/product_details/product_details?acid=' + item.acId
				// })
				
				// this这个是实例对象 必传
				// // #ifndef MP-WEIXIN
				// this.videoPlay = true;
				// this.videoUrl = item.tradeCoinName;  // 假设 tradeCoinName 是视频的 URL
								
				// console.log('H5 和app视频播放');
				
				// // 创建视频上下文
				// this.videoContext = uni.createVideoContext('myvideo', this);
				
				// // 通过 nextTick 确保视频上下文创建完成后，再调用全屏请求和播放
				// this.$nextTick(() => {
				//   // 先请求全屏
				//   this.videoContext.requestFullScreen({ direction: 90 });
								
				//   // 延迟播放，确保全屏模式先生效
				//   setTimeout(() => {
				//     this.videoContext.play();  // 播放视频
				//   }, 500);  // 延迟500ms确保全屏请求先执行
				// });
				// // #endif
			}
		},
		watch: {
			RecommendList: {
				immediate: true,
				handler(newVal) {
					// 当 RecommendList 变化时的处理逻辑
					console.log(newVal, '推荐列表更新');
					if(newVal.length > 0) {
						newVal.forEach((item, index) => {
							if (index % 2 == 0) {
								this.leftdata.push(item); // 偶数索引
							} else {
								this.rightdata.push(item); // 奇数索引
							}
						});
					}
				}
			},
		},
		created() {
			if(this.RecommendList.length > 0) {
				this.RecommendList.forEach((item, index) => {
					if (index % 2 === 0) {
						this.leftdata.push(item); // 偶数索引
					} else {
						this.rightdata.push(item); // 奇数索引
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.waterfall{
		.waterfallCon {
			width: 98%;
			margin: 0 auto;
			margin-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-around;
			.waterfall_item{
				width: 49%;
				.waterdetail{
					width: 100%;
					min-height: 360rpx;
					background-color: #fff;
					border-radius: 10rpx;
					margin: 5rpx 0 10rpx 0;
					.imageView{
						border-radius: 10rpx;
						position: relative;
						.video{
							position: absolute;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
						}
						image{
							border-top-left-radius: 10rpx;
							border-top-right-radius: 10rpx;
							width: 100%;
							height: 460rpx;
						}
					}
					.good_detail{
						padding:0 10rpx 10rpx 10rpx;
						.detail_title{
							 display: -webkit-box;
							  -webkit-box-orient: vertical;
							  -webkit-line-clamp: 2; /* 设置行数为2 */
							  overflow: hidden; /* 隐藏超出部分 */
							  text-overflow: ellipsis; /* 用省略号表示多余文本 */
							  font-family: PingFang SC, PingFang SC;
							  font-weight: 500;
							  font-size: 24rpx;
							  color: #333333;
							  line-height: 40rpx;
							  text-align: left;
							  font-style: normal;
							  text-transform: none;
						}
						.price{
							margin-top: 10rpx;
							height: 30rpx;
							line-height: 30rpx;
							color:#FF4000;
							font-size: 22rpx;
							display: flex;
							align-items: center;
							// justify-content: center;
							font-family: PingFang SC-Regular;
							font-weight: 400;
							text-align: center;
							font-style: normal;
							text-transform: none;
							.value{
								color:#FF4000;
								margin-left: 4rpx;
								font-size: 28rpx;
								font-weight: 600;
								font-family: DIN, DIN;
								font-weight: 600;
								text-align: center;
								font-style: normal;
								text-transform: none;
							}
						}
						.saledetail{
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-style: normal;
							text-transform: none;
							color:#a9a9a9;
							display: flex;
							flex-direction: row-reverse;
							align-items: center;
							justify-content: end;
							font-size: 20rpx;
							letter-spacing: 3rpx;
							.title{
								margin-left: 10rpx;
							}
							.value{
								// margin:0 8rpx;
							}
							
						}
					}
				}
			}
		}

	}
</style>