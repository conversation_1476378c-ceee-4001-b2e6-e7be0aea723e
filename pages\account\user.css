/**
 * 顶部内容
 */
 .top-content {
	background-repeat: no-repeat;
	background-size: 100% auto;
	width: 100%;
	min-height: 380rpx;
}


/* 自定义组件样式 */
.bg-white {
}

.wrapper{
	/* background: #000 !important; */
}
.word {
	/* width: 300rpx; */
	height: 100rpx;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	align-items: flex-start;
}

.icon-nav-list {
	overflow: hidden;
	padding: 20rpx;
	height: 140rpx !important;
}

.nav_top {
	width: 100%;
	/* height: 60rpx; */
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.tabBtn{
}
.btnitem{
	position: relative;
}

.num{
	position: absolute;
	right: -10rpx;
	top: -10rpx;
	color: #fff;
	background: #FF4000;
	border-radius: 94rpx 94rpx 94rpx 94rpx;
	height: 28rpx;
	width: 28rpx;
	font-weight: 300;
	font-size: 16rpx;
	color: #FFFFFF;
	line-height: 28rpx;
	text-align: center;
	font-style: normal;
	text-transform: none;
}


.main_top_content {
	width: 300rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
}

.zuhe {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.content_text {
	width: 210rpx;
	height: 60rpx;
	border-radius: 228rpx;
	color: #0C71FD;
	text-align: center;
	line-height: 60rpx;
	margin-right: 30rpx;
}

.main_middle {
	width: 670rpx;
	height: 190rpx;
	background-size: cover;
	border-radius: 10rpx;
	padding: 20rpx;
	/* margin-left: 30rpx; */
	margin-left: 25rpx;

}

.topText{
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.middle_text {
		width: 50%;
		height: 30rpx;
		font-size: 28rpx !important;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		color: #3D3D3D;
		letter-spacing: 1px;
		-webkit-background-clip: text;
}
	

.content_middle {
	width: 670rpx;
	height: 160rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-around;
	/* padding: 10rpx 30rpx; */
	align-items: center;
}

.middle_word {
	display: flex;
	flex-direction: column;
	height: 100%;
	justify-content: center;
	align-items: center;
}

.main_middle_s {
	width: 660rpx;
	margin-top: 20rpx;
	/* height: 320rpx; */
	height: 240rpx;
	padding: 0 20rpx;
	border-radius: 14rpx;
	margin-left: 30rpx;
}

.unlogin_s {
	width: 660rpx;
	margin-top: 20rpx;
	/* height: 320rpx; */
	height: 80rpx;
	padding: 0 20rpx;
	border-radius: 14rpx;
	margin-left: 30rpx;
}

.items {
	display: flex;
	margin: auto;
	justify-content: space-between;
	align-items: center;
	width: 95%;
	/* height: 33.3%; */
	/* height: 25%; */
	height: 33.3%;
	border-bottom: 1rpx solid #E2E2E2;
}


.unloginitems {
	display: flex;
	margin: auto;
	justify-content: space-between;
	align-items: center;
	width: 95%;
	/* height: 33.3%; */
	/* height: 25%; */
	height: 100%;
	border-bottom: 1rpx solid #E2E2E2;
}

.item_S {
	display: flex;
	justify-content: center;
	align-items: center;
}

.item_b {
	display: flex;
	align-items: center;
}

.item_b text {
	font-size: 24rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	color: #B1B1B1;
	margin-right: 8rpx;
}

.item_S text {
	font-size: 25rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	color: #3D3D3D;
	letter-spacing: 1px;
}

.main_bottom {
	width: 660rpx;
	/* height: 400rpx; */
	/* height: 280rpx; */
	padding: 10rpx 20rpx;
	border-radius: 14rpx;
	margin-top: 20rpx;
	margin-left: 30rpx;
}

.itemb {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	height: 90rpx;
	/* height: 20%; */
	/* border-bottom: 1rpx solid #E2E2E2; */
}
.itemb text {
	font-size: 25rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	color: #3D3D3D;
	letter-spacing: 1px;
}

/**
 * 头部
 */
.topImage {
	width: 700rpx;
	/* text-align: right; */
	height: 45rpx;
	margin-bottom: 30rpx;
	margin-top: 24rpx;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.imgs {
	width: 45rpx;
	height: 45rpx;
}

.imgs:nth-child(1) {
	margin-right: 20rpx;
}

.head-base .head-id {
	height: 36rpx;
	line-height: 36rpx;
}

.text-size-xs,
.text-size-xss {
	font-size: 22rpx;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	color: #FFFFFF;
	line-height: 31rpx;
	-webkit-background-clip: text;
}
.tip{
	width: 338rpx;
height: 42rpx;
font-family: PingFang SC, PingFang SC;
font-weight: 400;
font-size: 28rpx;
color: #999999;
line-height: 42rpx;
text-align: left;
font-style: normal;
text-transform: none;
}
.nickname{
	height: 54rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 500;
	font-size: 36rpx;
	color: #333333;
	line-height: 54rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}
.head-avatar {
	width: 144rpx;
	height: 144rpx !important;
}
.user_purse {
	margin:  0 20rpx;
	width: 710rpx;
	height: 188rpx;
	border-radius: 24rpx;
	background: url(/static/images/userVIP.png) no-repeat;
	background-size: 100% 100%;
	position: relative;
}
.vip_card{
	/* width: 170rpx; */
	min-width: 240rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	color: #FFFFFF;
	line-height: 36rpx;
	font-style: normal;
	font-family: PingFang SC, PingFang SC;
	font-size: 24rpx;
	font-weight: 500;
	text-transform: none;
	position: absolute;
	left: 45rpx;
	bottom: 28rpx;
}
.vip_card_num{
	margin-left: 20rpx;
}
.my_points_card{
	width: 170rpx;
	height: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: absolute;
	right: 46rpx;
	bottom: 24rpx;
}
.my_badge{
	width: 162rpx;
	height: 162rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
.my_badge_image{
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.my_points{
	width: 170rpx;
	height: 46rpx;
	background: #FFFFFF;
	border-radius: 140rpx 140rpx 140rpx 140rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FF4000;
	font-family: PingFang SC, PingFang SC;
	font-weight: 500;
font-size: 24rpx;
color: #FF6900;
line-height: 36rpx;
font-style: normal;
text-transform: none;
}
.my_points_num{
	margin-left: 10rpx;
}
/**
 * 头部右上角
 */
.head-right {
	z-index: 10;
}

.head-right .item .badge-icon {
	top: -10px;
	right: -2px;
}

/**
 * 会员码
 */
.qrcode-content {
	height: 108rpx;
	line-height: 108rpx;
	background-size: 100% 100%;
	color: #FEF6CF;
}

.qrcode-content.divider-r::after {
	right: 50%;
	height: 30%;
	background: #FEF6CF;
}

.qrcode .icon {
	width: 48rpx;
	margin-right: 18rpx;
	position: relative;
	top: 6rpx;
}

.qrcode .iconfont {
	top: calc(50% - 10rpx);
	right: 48rpx;
}

/**
 * 底部内容
 */
.user-bottom {
	padding: 20rpx 24rpx;
	border-radius: 8px 8px 0px 0px;
}

/**
 * 行内导航列表
 */
.nav-content .nav-all-order-goods {
	width: 138rpx;
	padding: 16rpx 16rpx 16rpx 12rpx;
}

.nav-content .nav-all-order-goods::before {
	content: '';
	width: 26rpx;
	height: 130rpx;
	background-image: url(data:image/png;base64,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);
	background-size: 100% auto;
	background-repeat: no-repeat;
	background-position: center left;
	position: absolute;
	left: -10rpx;
	top: 50%;
	transform: translateY(-50%);
}

.nav-list .nav-item {
	box-sizing: border-box;
}

.nav-list .nav-item .item-icon {
	width: 40rpx;
	height: 40rpx;
}

.nav-list .nav-item .item-desc {
	margin-right: 50rpx;
	margin-top: 2rpx;
	width: calc(100% - 300rpx);
}

.nav-list .nav-item .item-arrow {
	width: 25rpx;
}

.nav-list-sub {
	padding: 16rpx 16rpx 16rpx 0;
}

.nav-content .nav-all-order-goods .item .item-icon,
.nav-list-sub .item .item-icon {
	width: 56rpx;
	height: 56rpx;
}

.nav-list-sub .item .badge-icon {
	top: -2px;
	left: calc(50% + 35rpx);
	z-index: 1;
}

/**
 * 聚合导航
 */
.nav-box .nav-item {
	box-sizing: border-box;
	width: 25%;
}

.nav-box .nav-item .item-icon {
	width: 52rpx;
	height: 52rpx !important;
}

.nav-box .nav-item .item-desc {
	margin-right: 50rpx;
	margin-top: 2rpx;
	width: calc(100% - 300rpx);
}

.service-nav .show-type-submit {
	width: 50rpx;
	height: 50rpx !important;
	top: 18rpx;
	right: 20rpx;
}

.head-base {
	padding: 40rpx 40rpx 26rpx 40rpx;
	top: 0;
	/* height: 240rpx !important; */
}

.backcolor {
	transform: translateY(-20rpx);
	border-radius: 18rpx !important;
}

.spacing-mt {
	margin-top: -74rpx;
	z-index: 100000;
}

.nav_base {
	margin-right: 30rpx;
	color: #B1B1B1;
	font-size: 22rpx;
	margin-top: 20rpx;
	display: flex;
	align-items: center;
}
.user_icon{
	width: 32rpx;
	height: 32rpx;
	margin-left: 4rpx;
	margin-right: 16rpx;
}