<template>
	<view>
		<newNavBar :title="$t('accountUser.cz')" width="100vw" @clickLeftIcon="goBack" showTab="true" :orderStatus="orderStatus"
		:FiledList="czType" @changeOrderStatus="changeOStauts"></newNavBar>
		<no-data :propStatus="propStatus" v-if="isShow"></no-data>
		<view v-if="!isShow" style="margin-top: 85rpx;">
			<!-- <z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="false"
				:use-page-scroll="true">
				<refresh-loading slot="refresher"></refresh-loading> -->
				<view class="flex flex-center u-p-20 u-m-b-10" v-for="(item,index) in addressList" :key="item.id" style="background-color: #ffffff;">
					<view class="flex-1 u-m-r-40" @tap="selectAddress(item)">
						<!-- <u--text :text="item.info1" :lines="2"></u--text> -->
						<view class="flex justify-between align-center u-m-t-10">
							<u--text :text="item.info1" :lines="2" type="success" v-if="item.statusName == '审核通过'"></u--text>
							<u--text :text="item.info1" :lines="2" type="primary" v-if="item.statusName == '待审核'"></u--text>
							<u--text :text="item.info1" :lines="2" type="error" v-if="item.statusName == '审核拒绝'"></u--text>
						</view>
						<view class="flex justify-between align-center u-m-t-10">
							<text>{{item.info2}}</text>
						</view>
						<view class="flex justify-between align-center u-m-t-10">
							<view class="flex flex-center" style="color: #ccc;">
								<text class="u-m-r-20">{{item.info3}}</text>
							</view>
						</view>
					</view>
					<view class="flex flex-col justify-between align-center">
						<!-- <view class="u-m-b-20" >
							<u-icon name="edit-pen" size="18" @click="editAddress(item)"></u-icon>
						</view> -->
						<view v-show="item.status == 0">
							<u-icon name="trash" size="18" @click="deleteAddress(item)"></u-icon>
						</view>
					</view>
				</view>
				<!-- <component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line propMsg="没有数据了" v-else></component-bottom-line>
			</z-paging> -->
			<view style="height: 160rpx;"></view>
		</view>
		<view class="bot-button">
			<u-button color="linear-gradient(to right,#2A3A88FF , #3c9cff)" :text="$t('accountUser.cz')" @tap="editAddress"></u-button>
		</view>
		<u-modal 
		:show="deleteShow" 
		:title="$t('accountUser.ts')" 
		:content="$t('message.sctx')"
		showCancelButton 
		closeOnClickOverlay
		@confirm="confirmDeleteAddress" 
		@cancel="()=>{deleteShow = false}"
		@close="()=>{deleteShow = false}"></u-modal>
	</view>
</template>

<script>
  import {getAddress,postUpdateIsDefault,deletedAddress, deletedtxdata, postSavetx, getTxtype} from '@/common/request/api/user.js'
  import store from '@/store'
  import api from '@/common/request/index.js'
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default{
		data(){
			return {
				pageNum:1,
				addressList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				deleteShow:false,
				type:0,
				params: {
					page: 1,
					size: 15
				},
				// 充值记录状态类型  100全部  0待审核   1审核通过  2 审核拒绝
				orderStatus: 100,
				czType: [],
				loadingStatus: 	true
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad(opt) {
			if(opt.type){
				this.type = opt.type
			}
		},
		created() {},
		onShow() {
			this.addressList = []
			this.getCZtype()
			this.getAddress()
		},
		onReachBottom() {
			console.log('下拉触底')
			this.loadingStatus = true
			this.params.page ++
			this.getAddress()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods:{
			changeOStauts(value) {
				console.log(value, '子组件传过来的数据')
				this.orderStatus = value
				this.addressList = []
				this.params.page = 1
				this.params.size = 15
				this.getAddress()
				// setTimeout(() => {
				// 	this.$refs.paging.complete(false);
				// }, 1200)
			},
			queryList() {
				this.addressList = []
				this.params.page = 1
				this.params.size = 15
				this.getAddress()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAddress(){
				api({
					url: `mb/cz/get/list?page=${this.params.page}&size=${this.params.size}&orderStatus=${this.orderStatus}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '会员充值记录')
						if(res.data.data.list.length){
							if(this.addressList.length == res.data.data.totalCount) {
								// 已获取到全部数据
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							} else {
								if(this.params.page == 1) {
									this.addressList = [...res.data.data.list]
								} else {
								    this.addressList = [...this.addressList, ...res.data.data.list]
								}
								console.log(this.addressList, '充值列表数据')
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
								this.isShow = false
							}
						}else{
							this.propStatus = 0
						}
					}
				})
			},
			// 充值记录状态类型
			getCZtype() {
				api({
					url: `mb/cz/get/type/list?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res=>{
					if(res.code == 200){
						// console.log(res, '充值记录状态类型')
						this.czType = [...res.data.data]
						console.log(this.czType, '充值记录状态类型')
					}
				})
			},
			setDefault(value){
				if(value.sortNumber == 0) return
				postUpdateIsDefault(value.memberId).then(res=>{
					if(res.code == 200){
						this.getAddress()
					}
				})
			},
			editAddress(value = null){
				uni.navigateTo({
					url: `/pageo/my_top_ups/savemy_top_ups`,
				});
			},
			deleteAddress(value){
				console.log(value, '删除的数据')
				if(value.statusName !== '待审核'){
					uni.showToast({
						// title:'不是待审核 禁止删除',
						title: $t('message.bsdshjzsc'),
						icon:'none'
					})
					return
				}
				this.deleteId = value.id
				this.deleteShow = true
			},
			confirmDeleteAddress(){
				console.log(this.deleteId)
				api({
				    url: `mb/cz/deleted/${this.deleteId}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							// title: '操作成功',
							title: $t('message.czcg'),
							icon: 'none'
						})
						this.deleteShow = false
						this.addressList = []
						this.params.page = 1
						this.params.size = 15
						this.getAddress()
					} else {
						uni.showToast({
							// title: '异常',
							title: $t('message.yc'),
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						// title: '异常',
						title: $t('message.yc'),
						icon: "none"
					})
				})
				
				// deletedAddress(this.deleteId).then(res=>{
				// 	if(res.code == 200){
				// 		this.getAddress()
				// 		this.deleteShow = false
				// 	}
				// })
			},
			selectAddress(item){
				if(!this.type) return
				store.commit('setEphemeral',item)
				this.$back()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.is-default{
		color: #ffffff;
		background-color: #000000;
		padding: 4rpx 10rpx;
		font-size: 20rpx;
	}
	.no-default{
		color: #000000;
		background-color: #ffffff;
		padding: 4rpx 10rpx;
		border: 1rpx solid #000000;
		font-size: 20rpx;
	}
</style>
