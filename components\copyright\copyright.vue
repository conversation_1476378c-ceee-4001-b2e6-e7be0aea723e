<template>
    <view class="page">
        <view class="copyright">
			<text class="text">@杭州理橙提供技术支持</text>
            <!-- <view class="text">辽ICP备2024028337号</view> -->
        </view>
    </view>
</template>
<script>
    const app = getApp();
    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                version: app.globalData.data.version
            };
        },
        components: {},
        props: {},
        methods: {}
    };
</script>
<style>
	.page{
		background-color: #eef6ff;
	}
    .copyright {
		width: 100%;
        color: #cfcfcf;
		background-color: #eef6ff;
        text-align: center;
        padding: 15rpx 0 20rpx  0;
    }
    .copyright .text {
        font-size: 26rpx;
        font-weight: 400;
    }
</style>