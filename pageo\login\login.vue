<template>
	<view style="background: linear-gradient( 180deg, #FFE8DA 0%, #FFFFFF 100%);height: 100%;">
		<newNavBar title="登录" width='100vw' @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-40 flex flex-col justify-between">
			<view>
				<view class="flex flex-center flex-col" style="height: 18vh;">
					<image src="https://file.36sm.cn/jaxinfo/2025/02/12/574d5959ba454fd9840e0a5e8ab549dc.png" mode=""
						style="width: 200rpx;height: 200rpx;border-radius: 50%;"></image>
				</view>
				<view class="navbar_content">
					<view v-for="(item,index) in headerTab" :key="item.name">
						<view :class="['header_tab', index === headerActive?'tab_header_active':'']"
							v-if="index < sfxshydl" @click='changeHdaderTab(index)'>
							{{item.name}}
						</view>
					</view>
				</view>
				<view class="u-p-50" v-if='headerActive!==0'>
					<u--form labelPosition="left" labelWidth="auto" :model="formData" :rules="rules" ref="uForm">
						<u-form-item leftIcon="account" :leftIconStyle="leftIconStyle" prop="phone">
							<u--input v-model="formData.phone" border="none" placeholder="输入手机号" clearable
								@input="checkPhone" maxlength="13"></u--input>
						</u-form-item>
						<u-form-item v-if="[2,3,4].includes(type)" leftIcon="chat" :leftIconStyle="leftIconStyle"
							prop="code">
							<u--input v-model="formData.code" type="number" border="none"
								:placeholder="$t('accountUser.yzm')" clearable></u--input>
							<template slot="right">

								<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
								<u-button @tap="getCode" :text="tips" type="warning" size="mini"
									style="background: linear-gradient(to right, #FF6900, #FF6900)) !important;"></u-button>
							</template>
						</u-form-item>

						<u-form-item v-if="type != 2" leftIcon="lock" :leftIconStyle="leftIconStyle" prop="password">
							<u--input v-model="formData.password" type="password" border="none"
								:placeholder="type == 4 ? $t('message.qsrxmm') : $t('message.qsrmm')"
								clearable></u--input>
						</u-form-item>

						<!-- #ifndef APP -->
						<u-form-item v-if="type == 3" leftIcon="qzone" :leftIconStyle="leftIconStyle">
							<u--input v-model="formData.supInvitationCode" border="none"
								:placeholder="$t('message.qryqm')" clearable></u--input>
						</u-form-item>
						<!-- #endif -->

						<!-- #ifdef APP -->
						<u-form-item v-show="type == 3" leftIcon="qzone" :leftIconStyle="leftIconStyle">
							<u--input v-model="formData.supInvitationCode" border="none"
								:placeholder="$t('message.qryqm')" clearable></u--input>
						</u-form-item>
						<!-- #endif -->

					</u--form>
				</view>
				<view class="flex justify-end u-m-b-20" v-if="[1,2].includes(type) && headerActive!==0">
					<text style="font-size: 24rpx;font-weight: 500;color: #FF6900;"
						@tap='switchoverLogin'>{{type == 1 ? $t('message.yzmdl') : $t('message.mmdl')}}</text>
				</view>
				<view :class="[headerActive == 0 ? 'login_num' : '']">
					<!-- #ifdef MP-WEIXIN -->
					<button v-if='headerActive==0' class='u-m-t-40' type="primary" :loading="phoneloading"
						open-type='getPhoneNumber' @getphonenumber='getphonenumber'
						style="background: linear-gradient(to right, #FF6900, #FF6900) !important;">手机号快捷登录</button>
					<!-- #endif -->
					<button v-if='headerActive ==1' type="primary" @click="submit" :loading="loading"
						style="background: linear-gradient(to right, #FF6900, #FF6900) !important;">{{loginText}}</button>
				</view>
				<view class="flex justify-between align-center u-m-t-40" v-if='headerActive== 1'>
					<view>
						<u--text @click="goLogin" :text="type==3 ? $t('message.yjyzhljdl') : $t('message.hmzhljzc')"
							size="24rpx" color="#FF6900"></u--text>
					</view>
					<view>
						<u--text @click="retypePassword" v-if="[1,4].includes(type)"
							:text="type==4 ? $t('message.fhdl') : $t('message.zhmm')" color="#FF6900"
							size="24rpx"></u--text>
					</view>
				</view>
				<view class="flex flex-center u-m-t-40" v-if="[1,2].includes(type)">
					<u-checkbox-group v-model="isAgree" shape="circle" activeColor="#FF6900" @change="checkboxChange">
						<u-checkbox :label="$t('message.ydbty')" :name="1" size="24rpx" labelSize="24rpx"
							labelColor="#999999"></u-checkbox>
					</u-checkbox-group>
					<text style="color: #FF6900;font-size: 24rpx;margin: 0 10rpx;"
						@tap="goUser">{{$t('message.fwxy')}}</text>
					<text style="color: #999999;font-size: 24rpx;">{{$t('message.yyy')}}</text>
					<text style="color: #FF6900;font-size: 24rpx;margin: 0 10rpx"
						@tap="goprivacy">{{$t('message.ysqzc')}}</text>
				</view>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<!-- <view class="u-p-b-60 body-bottom">
				<u-divider text="第三方登录"></u-divider>
				<view class="flex flex-center u-m-t-60">
					<u--image src="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/03/22/e3bab5fbf9b644528c8e8ec65b36e0a5.png"
						width="50rpx" height="50rpx" @click="submit('weixin')"></u--image>
				</view>
			</view> -->
			<!-- #endif -->
		</view>
	</view>
</template>
<script>
	import {
		postLoginInfo,
		postRegisterSendCode,
		postLoginRegister,
		postModifySendCode,
	} from "@/common/request/api/login.js"
	import loginBy from '@/common/loginByType.js'
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				authCode: null,
				type: 1,
				pickershow: false,
				tips: '',
				loading: false,
				phoneloading: false,
				// params: null,
				prev_page: null,
				isAgree: [],
				SupportedCou: [],
				countryArr: [{
						name: '中国',
						areaCode: 86
					},
					{
						name: '柬埔寨',
						areaCode: 855
					}
				],
				headerActive: 0,
				headerTab: [{
						name: '快捷登录',
						value: 0

					},
					{
						name: "会员登录",
						value: 1
					}
				],
				countryindex: 0,
				Arrcountry: ['中国', '柬埔寨'],
				formData: {
					phone: '',
					password: '',
					code: '',
					areaCode: 86,
					supInvitationCode: ''
				},
				rules: {
					phone: [{
							required: true,
							// message: '请输入手机号',
							message: this.$t('message.qsrsjh'),
							trigger: ['blur'],
						},
						// {
						// 	validator: (rule, value, callback) => {
						// 		return uni.$u.test.mobile(value);
						// 	},
						// 	message: '手机号码不正确',
						// 	trigger: ['blur'],
						// }
					],
					code: {
						required: true,
						// message: '请输入验证码',
						message: this.$t('message.qsryzm'),
						trigger: ['blur']
					},
					password: {
						required: true,
						// message: '请输入密码',
						message: this.$t('message.qsrmm'),
						trigger: ['blur']
					},
					// areaCode: {
					// 	required: false,
					// 	message: '请选择国家',
					// 	trigger: ['change']
					// }
				},
				rulesCopy: {},
				leftIconStyle: {
					fontSize: '50rpx'
				},
				// 邀请码
				supInvitationCode: '',
				sfxshydl: 0

			}
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
			loginText() {
				let text = this.type == 1 ? this.$t('message.dl') :
					this.type == 2 ? this.$t('message.dl') :
					this.type == 3 ? this.$t('message.qrzc') : this.$t('message.qrtj')
				return text
			},
			getCountryName() {
				const country = this.countryArr.find(item => item.areaCode == this.formData.areaCode);
				return country ? country.name : this.$t('message.wzgj');
			},
		},
		created() {
			this.rulesCopy = JSON.parse(JSON.stringify(this.rules))
		},
		onLoad(params) {
			// this.setData({
			// 	params: params,
			// });

			// 处理微信扫推荐推荐二维码 路径会自带 supInvitationCode
			if (params.supInvitationCode) {
				console.log('扫码跳转 获取邀请码')
				this.supInvitationCode = params.supInvitationCode
				this.formData.supInvitationCode = params.supInvitationCode

				uni.setStorageSync("supInvitationCode", params.supInvitationCode)

			} else {
				// 如果当前页面刷新丢失
				this.supInvitationCode = uni.getStorageSync("supInvitationCode");
				this.formData.supInvitationCode = uni.getStorageSync("supInvitationCode");
			}
		},
		onShow() {

			this.getSFxshydl()
		},
		methods: {
			// 是否显示会员登录
			getSFxshydl() {
				api({
					url: "/bs/jy/info?jsKey=sfxshydl",
					method: 'get',
				}).then((res) => {
					const data = res.data.data
					this.sfxshydl = res.data.data
					if (this.sfxshydl == 2) {
						this.headerActive = 1
					}
					// this.headerActive = res.data.data
					// 1不显示  2 显示
					console.log(this.sfxshydl, '是否显示会员登录', this.headerActive)
				})
			},
			changeHdaderTab(e) {
				this.headerActive = e
			},
			getphonenumber(e) {
				console.log(e, '222222222')
				if ([1, 2].includes(this.type)) {
					if (!this.isAgree.length) return uni.$u.toast(this.$t('message.qtyyxtk'))
				}
				if (e.detail.code) {
					this.code = e.detail.code
					console.log(this.code, 'codecode')
					uni.login({
						provider: 'weixin',
						success: (res) => {
							this.authCode = res.code
							loginBy['phoneNmuber'](this)
						}
					})
				}
			},
			getSupportC() {
				api({
					url: `bs/dl/zc/gj?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					console.log(res, 'resres')
					if (res.code == 200) {
						this.SupportedCou = [...res.data.data]
						console.log(this.SupportedCou, 'SupportedCou手机号支持的国家');
					} else {
						uni.showToast({
							title: this.$t('message.ztmyc'),
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: 'none'
					})
				})
			},
			checkPhone(value) {
				// console.log(value,'value')
				// const phoneRegex = /^[1][3-9][0-9]{9}[a-zA-Z0-9]{0,1}$/;
				// if (value && phoneRegex.test(value)) {

				// } else {
				//    uni.showToast({
				//    	title:'手机号格式不对',
				//    	icon:'none'
				//    })
				// return
				// }
			},
			goUser() {
				uni.navigateTo({
					url: '/pageo/agreement/user_service'
				})
			},
			goprivacy() {
				uni.navigateTo({
					url: '/pageo/agreement/user_privacy'
				})
			},
			bindPickerChange(e) {
				console.log(e.detail.value, '222')
				this.countryindex = e.detail.value
				this.formData.areaCode = this.countryArr[this.countryindex].areaCode
				console.log(this.formData.areaCode, '地区编码')
			},
			switchoverLogin() {
				if (this.type == 1) {
					this.type = 2
					this.formData.code = ''
					this.formData.password = ''
				} else if (this.type == 2) {
					this.type = 1
					this.formData.code = ''
					this.formData.password = ''
				}
			},
			goLogin() {
				console.log(this.type, 'this.type')
				if (this.type == 3) {
					this.type = 1
				} else {
					// 登录模块
					this.type = 3
					this.formData.code = ''
					this.formData.password = ''
				}
			},
			retypePassword() {
				if (this.type == 4) {
					this.type = 1
				} else {
					this.type = 4
					this.formData.code = ''
					this.formData.password = ''
				}
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				this.$refs.uForm.validateField('phone', (res) => {
					if (!res.length) {
						if (this.$refs.uCode.canGetCode) {
							uni.showLoading({
								// title: '正在获取验证码',
								title: this.$t('message.zzhqyzm')
							})
							let data = {
								phone: this.formData.phone,
								areaCode: this.formData.areaCode
							}
							switch (this.type) {
								case 2:
									loginBy['loginCode'](this, data)
									break;
								case 3:
									loginBy['registerCode'](this, data)
									break;
								case 4:
									loginBy['modifyCode'](this, data)
									break;
							}

						} else {
							// uni.$u.toast('倒计时结束后再发送');
							uni.$u.toast(this.$t('message.djsjszfs'));

						}
					}
				})

			},
			checkboxChange(e) {

			},
			submit(mode) {
				console.log(this.formData, '提交的参数', this.type)

				if ([1, 2].includes(this.type)) {
					if (!this.isAgree.length) return uni.$u.toast(this.$t('message.qtyyxtk'))
				}

				this.loading = true
				setTimeout(() => {
					this.loading = false
				}, 2500)

				this.rules = JSON.parse(JSON.stringify(this.rulesCopy))
				if (mode == 'weixin') {
					loginBy['weixin'](this)
					return
				}
				switch (this.type) {
					case 1:
						delete this.rules.code
						this.$refs.uForm.validate().then(res => {
							loginBy['pwd'](this)
						})
						break;
					case 2:
						delete this.rules.password
						this.$refs.uForm.validate().then(res => {
							loginBy['sms'](this)
						})
						break;
					case 3:
						this.$refs.uForm.validate().then(res => {
							uni.login({
								provider: 'weixin',
								success: (res) => {
									this.authCode = res.code
									loginBy['register'](this)
								}
							})
						})
						break;
					case 4:
						this.$refs.uForm.validate().then(res => {
							loginBy['modify'](this)
						})
						break;
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F2F6FA;
	}

	// .tab_header_active::after {
	// 	display: block;
	// 	content: '';
	// 	background-image: url('https://file.36sm.cn/jaxinfo/2025/03/03/f6efd7f13ca84568a4f79246f6762aa2.png');
	// 	width: 92rpx;
	// 	height: 18rpx;
	// 	background-size: cover;
	// 	margin-left: 10rpx;
	// 	margin-top: -10rpx;
	// }
	.tab_header_active {
		border-bottom: 2px solid #FF6347;
		/* 你可以根据需要调整颜色 */
		padding-bottom: 8rpx;
		/* 可以根据需要调整底部的间距 */
	}

	.header_tab {
		font-weight: 400;
		font-size: 30rpx;
		color: #000;
		margin-right: 40rpx;
	}

	.tab_header_active {
		font-weight: bold;
		font-size: 32rpx;
		color: #FF6900;
	}

	.navbar_content {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 80rpx;
	}

	page {
		background-color: #F2F6FA;
	}

	.navbar_title {
		color: white;
	}

	.body-bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
	}

	.login_num {
		margin-top: 60rpx;
	}
</style>