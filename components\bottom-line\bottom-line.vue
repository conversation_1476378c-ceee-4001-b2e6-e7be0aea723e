<template>
    <view :class="theme_view">
        <view v-if="(propStatus || false)" class="data-bottom-line">
            <view class="left"></view>
            <view class="msg">{{propMsg || '我是有底线的'}}</view>
            <view class="right"></view>
        </view>
    </view>
</template>
<script>
    const app = getApp();
    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
            };
        },
        components: {},
        props: {
            propStatus: <PERSON><PERSON><PERSON>,
            propMsg: String
        },
        methods: {}
    };
</script>
<style>
    .data-bottom-line {
		/* background-color: #F2F6FA; */
        padding: 20rpx 10rpx 10rpx 0;
        overflow: hidden;
		width: 100%;
    }
    .data-bottom-line view {
        width: 33.3%;
    }
    .data-bottom-line .left,
    .data-bottom-line .right {
        margin-top: 8px;
        border-bottom: 1px dashed #e1e1e1;
    }
    .data-bottom-line .msg {
        color: #999;
        text-align: center;
        font-size: 24rpx;
    }
    .data-bottom-line .left,
    .data-bottom-line .msg {
        float: left;
    }
    .data-bottom-line .right {
        float: right;
    }
</style>