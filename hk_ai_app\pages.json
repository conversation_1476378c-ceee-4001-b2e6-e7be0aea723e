{
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [{
		"path": "pages/home/<USER>",
		"style": {
			"navigationStyle": "custom",
			"navigationBarTitleText": "智灵传媒",
			"enablePullDownRefresh": false
		}
	}, {
		"path": "pages/digital-avatar/index",
		"style": {
			// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-KUAISHOU || H5 || APP
			"navigationStyle": "custom",
			// #endif
			// #ifndef MP-ALIPAY
			"navigationBarTitleText": "智灵传媒",
			// #endif
			// #ifdef MP-ALIPAY
			"transparentTitle": "always",
			"titlePenetrate": "YES",
			"navigationBarTitleText": "",
			// #endif
			"enablePullDownRefresh": false,
			"navigationBarTextStyle": "white"
		}
	}, {
		"path": "pages/branded-space/index",
		"style": {
			// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-KUAISHOU || H5 || APP
			"navigationStyle": "custom",
			// #endif
			// #ifndef MP-ALIPAY
			"navigationBarTitleText": "智灵传媒",
			// #endif
			// #ifdef MP-ALIPAY
			"transparentTitle": "always",
			"titlePenetrate": "YES",
			"navigationBarTitleText": "",
			// #endif
			"enablePullDownRefresh": false,
			"navigationBarTextStyle": "black"
		}
	}, {
		"path": "pages/account/user",
		"style": {
			// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-KUAISHOU || H5 || APP
			"navigationStyle": "custom",
			// #endif
			// #ifndef MP-ALIPAY
			"navigationBarTitleText": "智灵传媒",
			// #endif
			// #ifdef MP-ALIPAY
			"transparentTitle": "always",
			"titlePenetrate": "YES",
			"navigationBarTitleText": "",
			// #endif
			"enablePullDownRefresh": false,
			"navigationBarTextStyle": "black"
		}
	}],

	"subPackages": [{
		"root": "pageg",
		"pages": [{
				"path": "buy/buy",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "订单支付",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "goods-category/goods-category",
				"style": {
					// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-KUAISHOU || H5 || APP
					"navigationStyle": "custom",
					// #endif
					// #ifndef MP-ALIPAY
					"navigationBarTitleText": "商品分类",
					// #endif
					// #ifdef MP-ALIPAY
					"transparentTitle": "always",
					"titlePenetrate": "YES",
					"navigationBarTitleText": "",
					"scrollIndicator": "none", // 该页不显示滚动条
					// #endif
					"enablePullDownRefresh": false,
					// 禁止页面下拉
					"disableScroll": true
				}
			},
			{
				"path": "cart-page/cart-page",
				"style": {
					"enablePullDownRefresh": false,
					"disableScroll": true,
					"navigationBarTitleText": "购物车"
				}
			},
			{
				"path": "goods-search/goods-search",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "商品搜索"
				}
			},
			{
				"path": "goods-detail/goods-detail",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "商品详情",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "goods-comment/goods-comment",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "商品评价"
				}
			},
			{
				"path": "order-detail/order-detail",
				"style": {
					"navigationBarTitleText": "订单详情",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "goods-detail/detail-inovice/detail-inovice",
				"style": {
					"navigationBarTitleText": "开具发票",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			}
		]
	}, {
		"root": "pageu",
		"pages": [{
				"path": "partner/partner",
				"style": {
					"navigationBarTitleText": "申请城市合伙人",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "clone/ime-clone/ime-clone",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "clone/pre-clone",
				"style": {
					"navigationBarTitleText": "预约克隆",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "clone/ime-clone/clone-order",
				"style": {
					"navigationBarTitleText": "克隆订单",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "clone/order-list/order-list",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "user-address/user-address",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "收货地址",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "user-address-save/user-address-save",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "地址编辑",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "user-order/user-order",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "我的订单",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "user-order-detail/user-order-detail",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "订单详情",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "user-order-comments/user-order-comments",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "订单评价"
				}
			},
			{
				"path": "user-favor/user-favor",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "商品收藏"
				}
			}, {
				"path": "user-integral/user-integral",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "积分明细"
				}
			},
			{
				"path": "user-goods-browse/user-goods-browse",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "我的足迹"
				}
			},
			{
				"path": "user-orderaftersale/user-orderaftersale",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "退款/售后"
				}
			},
			{
				"path": "user-orderaftersale-detail/user-orderaftersale-detail",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "订单售后"

				}
			},
			{
				"path": "user-identify/user-identify",
				"style": {
					"navigationBarTitleText": "身份实名认证",
					"enablePullDownRefresh": false
				}
			}
		]
	}, {
		"root": "pageo",
		"pages": [{
				"path": "user/user",
				"style": {
					// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-KUAISHOU || H5 || APP
					"navigationStyle": "custom",
					// #endif
					// #ifndef MP-ALIPAY
					"navigationBarTitleText": "用户中心",
					// #endif
					// #ifdef MP-ALIPAY
					"transparentTitle": "always",
					"titlePenetrate": "YES",
					"navigationBarTitleText": "",
					// #endif
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "web-view/web-view",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "login/login",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "登录",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "paytips/paytips",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "安全支付",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "message/message",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "消息",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "extraction-address/extraction-address",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "自提地址"
				}
			},
			{
				"path": "common/open-setting-location/open-setting-location",
				"style": {
					// #ifndef MP-TOUTIAO
					"navigationStyle": "custom",
					// #endif
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "common/agreement/agreement",
				"style": {
					// #ifndef MP-TOUTIAO
					"navigationStyle": "custom",
					// #endif
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "design/design",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "页面设计"
				}
			},
			{
				"path": "error/error",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "温馨提示"
				}
			},
			{
				"path": "article-category/article-category",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "所有文章"
				}
			},
			{
				"path": "article-detail/article-detail",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "文章详情"
				}
			},
			{
				"path": "setup/setup",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "设置",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "personal/personal",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "个人资料"
				}
			},
			{
				"path": "logout/logout",
				"style": {
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "账号注销"
				}
			},
			{
				"path": "home-radio/home-radio/home-radio",
				"style": {
					 "navigationStyle": "custom"
					// "navigationBarTitleText": "",
					// "enablePullDownRefresh": false,
					// "navigationBarTextStyle": "white",
					// "navigationBarBackgroundColor": "#000000"
				}
			},
			{
				"path": "news-more/news-more/news-more",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "新闻",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "digital-live/digital-live/digital-live",
				"style": {
					"navigationBarTitleText": "AI数字人直播",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "news/news",
				"style": {
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "newa/newa",
				"style": {
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "service/service",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "消息",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "barcode/barcode",
				"style": {
					"navigationBarTitleText": "我的二维码",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "local-life/local-life",
				"style": {
					"navigationBarTitleText": "本地生活",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/navMore/navMore",
				"style": {
					"navigationBarTitleText": "更多内容",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "derivative/derivative-hot/derivative-hot",
				"style": {
					"navigationBarTitleText": "热门衍生品",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "derivative/derivative-special/derivative-special",
				"style": {
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "derivative/derivative-order/derivative-order",
				"style": {
					"navigationBarTitleText": "确认订单",
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "derivative/derivativeOrderList/derivativeOrderList",
				"style": {
					"navigationBarTitleText": "衍生品订单",
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "plugins/dhi/dhi",
				"style": {
					"navigationBarTitleText": "数字人资讯",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}

			},
			{
				"path": "plugins/wisdom/wisdom",
				"style": {
					"navigationBarTitleText": "智灵资讯",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/chain/chain",
				"style": {
					"navigationBarTitleText": "供应链",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "plugins/integral/integral",
				"style": {
					"navigationBarTitleText": "积分商城",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "plugins/northface/northface",
				"style": {
					"navigationBarTitleText": "北面空间",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/clothes/clothes",
				"style": {
					"navigationBarTitleText": "2024新上",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/promote/promote",
				"style": {
					"navigationBarTitleText": "数字人推广",
					// "navigationBarTextStyle": "white",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "search/search",
				"style": {
					"navigationStyle": "custom",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "digital-life/digital-life",
				"style": {
					"navigationBarTitleText": "智灵生活",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/cas/cas",
				"style": {
					"navigationBarTitleText": "投诉建议",
					"enablePullDownRefresh": false
				}
			},
			
			{
				"path": "deri-video/deri-video",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}
			},
			{
				"path": "plugins/wallet/recharge/recharge",
				"style": {
					"navigationStyle": "custom" ,
					"navigationBarTitleText": "充值余额",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "plugins/wallet/recharge-details/recharge-details",
				"style": {
					
					"navigationBarTitleText": "充值明细",
					"enablePullDownRefresh": false
				}
			}
		]
	}],
	"globalStyle": {
		"navigationBarTitleText": "智灵传媒",
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#f5f5f5",
		"backgroundColorBottom": "#f5f5f5",
		"app-plus": {
		   "bounce": "none" //关闭窗口回弹效果
		 }
	},
	"tabBar": {
		"color": "#333",
		"selectedColor": "#333",
		"backgroundColor": "#fff",
		"borderStyle":"white",
		"iconSize": 12,
		"list": [{
				"pagePath": "pages/home/<USER>",
				"iconPath": "static/images/common/tabbar/home.png",
				"selectedIconPath": "static/images/common/tabbar/home.png",
				"text": "数字人"
			}, {
				"pagePath": "pages/digital-avatar/index",
				"iconPath": "static/images/common/tabbar/category.png",
				"selectedIconPath": "static/images/common/tabbar/category.png",
				"text": "数字分身"
			},
			{
				"pagePath": "pages/branded-space/index",
				"iconPath": "static/images/common/tabbar/cart.png",
				"selectedIconPath": "static/images/common/tabbar/cart.png",
				"text": "品牌空间"
			},
			{
				"pagePath": "pages/account/user",
				"iconPath": "static/images/common/tabbar/user.png",
				"selectedIconPath": "static/images/common/tabbar/user.png",
				"text": "我的"
			}
		]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}