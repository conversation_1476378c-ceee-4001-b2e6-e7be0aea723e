<template>
	<view>
		<newNavBar :title="$t('accountUser.zjye')" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<no-data :propStatus="propStatus" v-if="isShow"></no-data>
		<view v-if="!isShow">
			<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="false"
				:use-page-scroll="true" :auto="false">
				<refresh-loading slot="refresher"></refresh-loading>
				<view class="flex flex-center u-p-20 u-m-b-20" v-for="(item,index) in fundvalueList" :key="item.id" style="background-color: #ffffff;">
					<view class="flex-1 u-m-r-40" @tap="selectAddress(item)">
						<!-- <u--text :text="item.coinId == 1 ? '余额' : '' " :lines="2"></u--text> -->
						<u--text :text="item.typeName" :lines="2" type="error" v-if="item.typeName == '购买'"></u--text>
						<u--text :text="item.typeName" :lines="2" type="success" v-if="item.typeName == '充值'"></u--text>
						<u--text :text="item.typeName" :lines="2" type="primary" v-if="item.typeName !== '购买' && item.typeName !== '充值'"></u--text>
						
						<view class="flex justify-between align-center u-m-t-10">
							<text>{{item.amount}}</text>
							<view class="flex flex-center">
								<text class="u-m-r-20" style="color: #a5a5a5;">{{item.createTime}}</text>
							</view>
						</view>
					</view>
					<view class="flex flex-col justify-between align-center">
						<!-- <view class="u-m-b-20" >
							<u-icon name="edit-pen" size="18" @click="editAddress(item)"></u-icon>
						</view>
						<view>
							<u-icon name="trash" size="18" @click="deleteAddress(item)"></u-icon>
						</view> -->
					</view>
				</view>
				<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line :propMsg="$t('accountUser.mysjl')" v-else></component-bottom-line>
			</z-paging>
		</view>
		<!-- <view class="bot-button">
			<u-button color="linear-gradient(to right,#2A3A88FF , #3c9cff)" text="新增收获地址+" @click="editAddress"></u-button>
		</view> -->
		<u-modal 
		:show="deleteShow" 
		:title="$t('accountUser.ts')" 
		:content="$t('message.sctx')"
		showCancelButton 
		closeOnClickOverlay
		@confirm="confirmDeleteAddress" 
		@cancel="()=>{deleteShow = false}"
		@close="()=>{deleteShow = false}"></u-modal>
	</view>
</template>

<script>
	import {getAddress,postUpdateIsDefault,deletedAddress} from '@/common/request/api/user.js'
  import store from '@/store'
  import api from '@/common/request/index.js'
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default{
		data(){
			return {
				pageNum:1,
				fundvalueList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				deleteShow:false,
				type:0,
				loadingStatus: true,
				params: {
					page: 1,
					size: 15
				}
			}
		},
		onLoad(opt) {
			if(opt.type){
				this.type = opt.type
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onReachBottom() {
			console.log('下拉触底')
			this.loadingStatus = true
			this.params.page ++
			this.getAddress()
		},
		onShow() {
			this.getAddress()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods:{
			queryList() {
				console.log('上拉刷新')
				// this.fundvalueList = []
				this.params.page = 1
				this.params.size = 15
				this.getAddress()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAddress(){
				api({
					url: `mb/zj/get/mx?location=${this.location}&page=${this.params.page}&size=${this.params.size}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res.data.data.list, '会员的钱包列表')
						if(this.fundvalueList.length == res.data.data.totalCount){
							// 数据加载完全了
							setTimeout(() => {
								this.loadingStatus = false
							}, 1500)
						}else{
							if(this.params.page == 1) {
								this.fundvalueList = [...res.data.data.list]
							} else {
								this.fundvalueList = [...this.fundvalueList, ...res.data.data.list]
							}
							this.isShow = false
							setTimeout(() => {
								this.loadingStatus = false
							}, 1500)
						}
					}
				})
			},
			editAddress(value = null){
				uni.navigateTo({
					url: `/pageu/user-address-save/user-address-save?address=${JSON.stringify(value)}&type=${this.type}`,
				});
			},
			deleteAddress(value){
				console.log(value)
				// if(value.isDefault == 1){
				// 	uni.showToast({
				// 		title:'默认地址禁止删除',
				// 		icon:'none'
				// 	})
				// 	return
				// }
				this.deleteId = value.addressId
				this.deleteShow = true
			},
			confirmDeleteAddress(){
				deletedAddress(this.deleteId).then(res=>{
					if(res.code == 200){
						this.getAddress()
						this.deleteShow = false
					}
				})
			},
			selectAddress(item){
				if(!this.type) return
				store.commit('setEphemeral',item)
				this.$back()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.is-default{
		color: #ffffff;
		background-color: #000000;
		padding: 4rpx 10rpx;
		font-size: 20rpx;
	}
	.no-default{
		color: #000000;
		background-color: #ffffff;
		padding: 4rpx 10rpx;
		border: 1rpx solid #000000;
		font-size: 20rpx;
	}
</style>