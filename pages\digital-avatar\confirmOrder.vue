<template>
	<view class="page">
		<newNavBar :title="$t('search.qrdd')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="OrderInfo" v-if="visible">
			<view class="addressInfo">
				<view class="address" v-if="OrderInfo.addressInfo" @tap="setaddress">
					<image src="https://file.36sm.cn/xtjyjt/images/common/dq.png" mode="" style="width: 50rpx;height: 50rpx;"></image>
					<view class="addressdetail">
						<view class="userinfo">
							<text style="margin-right: 20rpx;">{{OrderInfo.addressInfo.adUserName}}</text>
							<text>{{OrderInfo.addressInfo.adContactInfo}}</text>
						</view>
						<view class="adddetail">
							<u--text size="28rpx" bold color="#0f0f0f" :lines="1":text="OrderInfo.addressInfo.adAddress5 + OrderInfo.addressInfo.adAddressDetails" ></u--text>
						</view>
					</view>
					<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" mode="" style="width: 20rpx;height: 25rpx;margin-right: 20rpx;"></image>
				</view>
				<view class="noaddress" v-else @tap="setaddress">
					<view class="leftaddress">
						<image src="https://file.36sm.cn/xtjyjt/images/common/dq.png" mode="" style="width: 50rpx;height: 50rpx;"></image>
						<view class="title">{{$t('search.tjshdz')}}</view>
					</view>
					<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" mode="" style="width: 20rpx;height: 25rpx;margin-right: 20rpx;"></image>
				</view>
				
			</view>
			<view class="shopinfoList" v-if="OrderInfo.shopList.length > 0">
				<view class="shopitem" v-for="(item,index) in OrderInfo.shopList" :key="index">
					<view class="shopdetail">
						<view class="shopname">
							<image src="https://file.36sm.cn/xtjyjt/images/common/altouxiang.png" mode="" style="width: 45rpx;height: 45rpx;"></image>
							<text class="shoptitle">{{item.shopName}}</text>
						</view>
						<view class="mxlist" v-if="item.mxList.length > 0">
							<view class="mxitem" v-for="(items, i) in item.mxList" :key="i">
								<view class="mxdetail">
									<view class="promig">
										<image :src="items.productSkuImg" mode="" style="width: 200rpx;height: 200rpx;border-radius: 15rpx;"></image>
									</view>
									<view class="proright">
										<view class="protop">
											<view style="width: 300rpx;">
											   <u--text :text="items.productTitle" :lines="1" size="28rpx" color="#000"></u--text>
											</view>
											<view class="value">
												<text class="name">{{items.tradeCoinName}}</text>
												<text class="price">{{items.sellProductPriceTrade}}</text>
											</view>
										</view>
										<view class="procenter">
											<view style="width: 300rpx;">
												<u--text :text="items.productSkuName" :lines="1" size="22rpx" color="#aaa"></u--text>
											</view>
											<view class="value">
												<text class="name">{{items.baseCoinName}}</text>
												<text class="price">{{items.sellProductPriceBase}}</text>
											</view>
										</view>
										<view class="probottom">
											<text class="price">x{{items.sellQuantity}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
						
						<view class="masks">
							<view class="title">
								<text>{{$t('search.bz')}}</text>
							</view>
							<view class="remark" @tap="editRemark">
								<view class="MarksPro">{{proremarks}}</view>
								<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" mode="" style="width: 15rpx;height: 15rpx; margin-left: 15rpx;"></image>
							</view>
						</view>
						<view class="line"></view>
						<view class="firstfk">
							<view class="firsttitle">
								{{$t('search.dycfk')}}
							</view>
							<view class="content">
								<view class="huil">
									<view class="title">
										{{$t('search.hl')}}
									</view>
									<view class="value" style="color: #0F82E0;letter-spacing: 1rpx;">
										{{item.exchangeTate}}
									</view>
								</view>
								<view class="totalproprice">
									<view class="title">
										<!-- 商品总价 -->
										{{$t('search.spzj')}}
									</view>
									<view class="price">
										<view class="usd">
											{{item.tradeCoinName}}  {{item.spZjTrade}}
										</view>
										<view class="base">
											{{item.baseCoinName}}  {{item.spZjBase}}
										</view>
									</view>
								</view>
								<view class="zgyf">
									<view class="title">
										<!-- 中国运费 -->
										{{$t('search.zgyf')}}
									</view>
									<view class="price">
										<view class="usd">
											{{item.tradeCoinName}}  {{item.zgYfTrade}}
										</view>
										<view class="base">
											{{item.baseCoinName}}  {{item.zgYfBase}}
										</view>
									</view>
								</view>
								<view class="cghj">
									<view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.cghj'))">
										<!-- <text>采购合计</text> -->
										<text>{{$t('search.cghj')}}</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
									</view>
									<view class="price">
										<view class="usd">
											{{item.tradeCoinName}}  {{item.cgHjfTrade}}
										</view>
										<view class="base">
											{{item.baseCoinName}}  {{item.cgHjfBase}}
										</view>
									</view>
								</view>
								<view class="line"></view>
								<view class="dgfwf">
									<view class="title">
										<!-- 代购服务费 -->
										<text>{{$t('search.dgfwf')}}</text>
									</view>
									<view class="price">
										<view class="usd">
											{{item.tradeCoinName}}  {{item.dgfTrade}}
										</view>
									</view>
								</view>
								<view class="yhf">
									<view class="title" style="display: flex;"  @tap="showinfo($t('search.yhf'))">
										<!-- <text>验货费</text> -->
										<text>{{$t('search.yhf')}}</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
									</view>
									<view class="price">
										<view class="usd">
											{{item.tradeCoinName}}  {{item.yhfTrade}}
										</view>
									</view>
								</view>
								<view class="jgzj">
									<!-- <text>总计</text> -->
									<text>{{$t('search.zj')}}</text>
									<view class="value">
										{{item.tradeCoinName}}  {{item.syfyTrade}}
									</view>
								</view>
								<view class="yggjyf">
									<view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.yggjf'))">
										<!-- <text>预估国际运费</text> -->
										<text>{{$t('search.yggjf')}}</text>
										<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
									</view>
									<view class="price">
										
										<view class="usd">
										  <text>{{item.tradeCoinName}} {{item.gjwlfTrade}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="moreinfo" v-if="item.ismore">
							<!-- <view class="line"></view>
							<view class="hdfk">
								<view class="title" style="display: flex;align-items: center;">
									<text>货到付款</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
								</view>
								<u-switch v-model="ishdfk" @change="hdchange" size="24" activeColor="#FF4000"></u-switch>
							</view> -->
							<view class="line"></view>
							<view class="toubao">
								<view class="text">
									<!-- <text>投保</text> -->
									<text>{{$t('search.tb')}}</text>
								</view>
								<view class="gzwp">
									<!-- <text>贵重物品建议投保</text> -->
									<text>{{$t('search.gzwpjytb')}}</text>
									<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" mode="" style="width: 15rpx;height: 15rpx;"></image>
								</view>
							</view>
							<view class="line"></view>
							<view class="jgfw">
								<view class="jgfwtitle">
									<!-- <text>加固服务</text> -->
									<text>{{$t('search.jgfw')}}</text>
								</view>
								<view class="jgcontent">
									<!-- <text>该服务为收费服务, 详情查看</text> -->
									<text>{{$t('search.gfwsf')}}</text>
									<u-link href="https://www.baidu.com" :text="$t('search.jgfwsm')" :under-line="true" color="#000" lineColor="#000"></u-link>
								</view>
							</view>
							<view class="line"></view>
							<view class="thsb">
								<view class="thsbtitle">
									<!-- 特货申报 (可多选) -->
									<text>{{$t('search.thsb')}}</text>
								</view>
								<view class="thsbcontent">
									<!-- <text>请如实申报, 否则可能会照成清关扣货。特货需要额外加收费用，详情查看</text> -->
									<text>{{$t('search.qrssb')}}</text>
								</view>
								<view class="thsblink">
									<u-link href="https://www.baidu.com" :text="$t('search.thjssm')" :under-line="true" color="#000" lineColor="#000"></u-link>
								</view>
							</view>
							<view class="checkbox">
								<u-checkbox-group>
									<u-checkbox v-model="checkboxList[0].checked" shape="circle" :label="$t('message.mpfm')" activeColor="#FF4000"></u-checkbox>
								</u-checkbox-group>
							</view>
							<view class="checkbox">
								<u-checkbox-group>
									<u-checkbox v-model="checkboxList[1].checked" shape="circle" :label="$t('message.slyp')" activeColor="#FF4000"></u-checkbox>
								</u-checkbox-group>
							</view>
							<view class="checkbox">
								<u-checkbox-group>
									<u-checkbox v-model="checkboxList[2].checked" shape="circle" :label="$t('message.pbbjb')" activeColor="#FF4000"></u-checkbox>
								</u-checkbox-group>
							</view>
							<view class="checkbox">
								<u-checkbox-group>
									<u-checkbox v-model="checkboxList[3].checked" shape="circle" :label="$t('message.sjck')" activeColor="#FF4000"></u-checkbox>
								</u-checkbox-group>
							</view>
						</view>
						<!-- <u-divider
						        :text="item.moretitle"
						        textColor="#1092f5"
						        lineColor="#ccc"
								@tap="Moreinfo(item, index)"
						></u-divider> -->
					</view>
				</view>
			</view>
			<view style="height: 120rpx;">
				
			</view>
			<!-- <component-copyright></component-copyright> -->
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>、
		<view class=" totalbottom" v-if="visible">
			<view class="toptitle">
				<checkbox-group @change="changeSelect()">
					<label>
						<checkbox class="selected" color="#FF4000" :checked="ischecked" style="transform:scale(0.7)" shape="circle"/>
					</label>
				</checkbox-group>
				<text>{{$t('search.wyydbty')}}</text>
				<!-- <u-link href="https://www.baidu.com" :text="$t('search.dgfwsm')"  color="#0F82E0"></u-link> -->
				<text style="color: #0F82E0;margin-left: 15rpx;" @tap="Purchservice">{{$t('search.dgfwsm')}}</text>
			</view>
			<view class="bottomcontent">
				<view class="left">
					<view class="totalprice">
						<text>{{$t('search.zj')}}</text>
						<view class="value">
							<text style="margin-right: 10rpx;">{{OrderInfo.tradeCoinName}}</text>
							<text>{{OrderInfo.syfyTrade}}</text>
						</view>
						<view class="mx" @tap="showMX">
							<text>{{$t('search.mx')}}</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/down.png" mode="" style="width: 20rpx;height: 20rpx;"></image>
						</view>
					</view>
					<view class="noyf">
						<text>{{$t('search.bhgjf')}}</text>
					</view>
				</view>
				
				<view class="rightbtn">
					<u-button text="立即下单" class="addOrder" color="#FF4000" :loading="isloading" :disabled="isloading" :loadingText="$t('search.jiaz')" @tap="addOrderInfo"></u-button>
				</view>
			</view>
		</view>

		<uni-popup ref="marksPopup" type="center" :is-mask-click="false">
		   	<view class="remarksinfo" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<text>{{$t('search.bz')}}</text>
		   			</view>
					<view class="content">
						<!-- <input type="text" placeholder="请输入备注" v-model="proremarks"/> -->
						<u--input :placeholder="$t('search.bz')" border="none" clearable  v-model="proremarks" maxlength="100"></u--input>
					</view>
					<view class="bottombtn">
						<view class="btnbtn">
							<u-button iconColor="#FF4000" :text="$t('search.QX')"  @tap="closepopup" class="cancelbtn"></u-button>
						</view>
						<view class="btnbtn">
						  <u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.qd')"  @tap="closepopup"></u-button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<uni-popup ref="infoPopup" type="center" :is-mask-click="false">
		   	<view class="Popupinfo" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<text>{{infotitle}}</text>
		   			</view>
					<view class="content">
						<u-parse :content="infodetail"></u-parse>
					</view>
					<view class="bottombtn">
						<u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.wzdl')"  @tap="closeinfo"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
		<uni-popup ref="MXPopup" type="bottom" :is-mask-click="false">
		   	<view class="PopupinfoMx" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							<text>费用明细</text>
						</view>
						<view class="topClose" @click="closeMXPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 25rpx;height: 25rpx;"></image>
						</view>
					</view>
					
					<view class="feettitle">
						<text>特货申报、增值服务费用入仓后和国际运费一起支付</text>
					</view>
					<view class="content">
						<view class="totalproprice Ctitle">
							<view class="title">
								{{$t('search.spzj')}}
							</view>
							
							<view class="price">
								<view class="usd" style="display: flex;justify-content: flex-end;">
									<text>{{OrderInfo.tradeCoinName}}</text>
								</view>
								<view class="base">
									<text>{{OrderInfo.syfyTrade}}</text>
								</view>
							</view>
						</view>
						<view class="zgyf Ctitle">
							<view class="title">
								{{$t('search.zgyf')}}
							</view>
							<view class="price">
								<view class="usd">
									<!-- USD 中国运费-USD -->
									<text>{{OrderInfo.zgYfTrade}}</text>
								</view>
								<view class="base">
									<!-- CNY 中国运费-CNY -->
									<text>{{OrderInfo.zgYfBase}}</text>
								</view>
							</view>
						</view>
						<view class="cghj Ctitle">
							<view class="title" style="display: flex;align-items: center;">
							<!-- <view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.cghj'))"> -->
								<!-- <text>采购合计</text> -->
								<text>{{$t('search.cghj')}}</text>
								<!-- <image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image> -->
							</view>
							<view class="price">
								<view class="usd">
									<!-- USD 采购合计费用-USD -->
									<text>{{OrderInfo.cgHjfTrade}}</text>
								</view>
								<view class="base">
									<!-- CNY 采购合计费用-CNY -->
									<text>{{OrderInfo.cgHjfBase}}</text>
								</view>
							</view>
						</view>
						
						<view class="yggjyf Ctitle">
							<view class="title" style="display: flex;align-items: center;">
							<!-- <view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.yggjf'))"> -->
								<text>{{$t('search.yggjf')}}</text>
								<!-- <image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image> -->
							</view>
							<view class="price">
								<view class="usd">
								  <!-- <text>国际运费</text> -->
								  <text>{{OrderInfo.gjwlfTrade}}</text>
								</view>
							</view>
						</view>
						<view class="dgfwf Ctitle">
							<view class="title">
								<!-- 代购服务费 -->
								<text>{{$t('search.dgfwf')}}</text>
							</view>
							<view class="price">
								<view class="usd">
									<!-- USD 代购费用 -->
									<text>{{OrderInfo.dgfTrade}}</text>
								</view>
							</view>
						</view>
						
						<view class="yhf Ctitle">
							<!-- <view class="title" style="display: flex;align-items: flex-start;" @tap="showinfo($t('search.yhf'))"> -->
							<view class="title" style="display: flex;align-items: flex-start;">
								<text>{{$t('search.yhf')}}</text>
								<!-- <image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image> -->
							</view>
							<view class="price">
								<view class="usd">
								  <!-- <text>USD 验货费</text> -->
								  <text>{{OrderInfo.yhfTrade}}</text>
								</view>
							</view>
						</view>
						
						<view class="jsbz Ctitle">
							<view class="title">
								<text>结算币种</text>
							</view>
							<view class="price">
								<view class="usd">
									<!-- USD -->
									<text>{{OrderInfo.tradeCoinName}}</text>
								</view>
							</view>
						</view>
						<view class="huil Ctitle">
							<view class="title">
								{{$t('search.hl')}}
							</view>
							<view class="value" style="color: #0F82E0;letter-spacing: 1rpx;">
								<!-- 1USD = 7.50CNY -->
								<text>{{OrderInfo.exchangeTate}}</text>
							</view>
						</view>
					</view>
					<view class="Mxbottombtn">
						<view class="left">
							<view class="totalprice">
								<text>{{$t('search.zj')}}</text>
								<view class="value">
									<text style="margin-right: 10rpx;">{{OrderInfo.tradeCoinName}}</text>
									<!-- <text>所有费用总和</text> -->
									<text>{{OrderInfo.syfyTrade}}</text>
								</view>
							</view>
							<view class="noyf">
								<text>费用明细</text>
								<image src="https://file.36sm.cn/xtjyjt/images/common/up.png" mode="" style="width: 20rpx;height: 20rpx;margin-left: 10rpx;"></image>
							</view>
						</view>
						
						<view class="rightbtn">
							<u-button :text="$t('search.ljxd')" class="addOrder" color="#FF4000" :loading="isloading" :disabled="isloading" :loadingText="$t('search.jiaz')" @tap="addOrderInfo"></u-button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
	</view>
</template>

<script>
	const app = getApp();
	
	import componentCopyright from '../../components/copyright/copyright';
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				MxIdList: [],
				visible: false,
				OrderInfo: {},
				// 货到付款
				ishdfk: false,
				checkboxValue1: [],
				checkboxList: [
					{
						title: '品牌、粉末、纯电池',
						ischecked: false
					},
					{
						title: '少量药品、保健品、防疫用品、化工品',
						ischecked: false
					},
					{
						title: '平板、笔记本电脑',
						ischecked: false
					},
					{
						title: '手机、插卡手表',
						ischecked: false
					}
				],
				ischecked: false,
				isloading: false,
				addressId: "",
				proremarks: "",
				infotitle: "",
				infodetail: ''
			}
		},
		components: {
			componentCopyright
		},
		onLoad(params) {
			console.log(params, '路由参数')
			if(params.MxIdList) {
			  this.MxIdList = [...JSON.parse(params.MxIdList)]
			  console.log(this.MxIdList, 'MxIdList')
			}
			
			uni.$on('getaddressId', (data) => {
				console.log('我在子页面选择了:', data);
				this.addressId = data.current;
				console.log(this.addressId, '收货地址对应的id');
				this.getOrderinfo()
			});
				// setTimeout(() => {
				// 	this.$data.visible = true
				// }, 1200)
		},
		onShow() {
			if(this.addressId == '') {
				this.visible = false
				this.getOrderinfo()
				// setTimeout(() => {
				// 	this.$data.visible = true
				// }, 1500)
			} else {
				this.visible = false
				this.getOrderinfo()
			}
		},
		onReady() {
		},
		created() {
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			showMX() {
				this.$refs.MXPopup.open()
			},
			closeMXPopup() {
				this.$refs.MXPopup.close()
			},
			showinfo(title) {
				if(title == this.$t('search.cghj')) {
					this.infodetail = this.OrderInfo.cgHjfRemarks
				}
				if(title == this.$t('search.yhf')) {
					this.infodetail = this.OrderInfo.yhfRemarks
				}
				if(title == this.$t('search.yggjf')) {
					this.infodetail = this.OrderInfo.gjwlfRemarks
				}
				this.infotitle = title
				this.$refs.infoPopup.open()
			},
			closeinfo() {
				this.$refs.infoPopup.close()
			},
			editRemark() {
				// 备注编辑
				this.$refs.marksPopup.open()
			},
			closepopup() {
				this.$refs.marksPopup.close()
			},
			moveHandle() {},
			changeSelect() {
				this.ischecked = !this.ischecked
			},
			Purchservice() {
				//代购服务说明
				uni.navigateTo({
					url: '/pageu/purch_service/purch_service'
				})
			},
			addOrderInfo() {
				if(!this.ischecked) {
					uni.showToast({
						// title: '请勾选我已阅读并同意',
						title: this.$t('message.qgxwyyd'),
						icon: 'none'
					})
					return
				}
				
				// 如果没有设置地址请先去设置地址
				if(!this.OrderInfo.subInfo.addressId) {
					uni.showToast({
						// title: '请选择收货地址',
						title: this.$t('message.qxzshdz'),
						icon: 'none'
					})
					return
				}
				
				if (this.isloading) return;
				
				this.isloading = true
				// setTimeout(() => {
				// 	this.isloading = false
				// }, 2000)
				
				console.log(this.ischecked, 'ischecked')
				
				// console.log(this.OrderInfo, 'OrderInfo')
				// const dataList = {
				// 	orderDTO: this.OrderInfo
				// }
				api({
					    url: `car/lj/zf/order/info?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post',
						data: this.OrderInfo
					}).then(res => {
						if(res.code == 200) {
							console.log(res.data.data, '订单详细')
							const ConfirmOInfo = res.data.data
							console.log(ConfirmOInfo, 'ConfirmOInfo')
							// 进入支付页面
							uni.navigateTo({
								url: `/pages/digital-avatar/ConfirmOInfo?ConfirmOInfo=${encodeURIComponent(JSON.stringify(ConfirmOInfo))}`
							})
							// this.isloading = false
						} else {
							uni.showToast({
								title: '状态码异常',
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					})
				
			},
			setaddress() {
				// 设置地址
				uni.navigateTo({
					url: `/pageu/user-address/user-address?type=dz`
				})
			},
			Moreinfo(item, index) {
				console.log(item, index);
				const currentItem = this.OrderInfo.shopList[index];
				    this.$set(this.OrderInfo.shopList, index, {
				        ...currentItem,
				        ismore: !currentItem.ismore,
				        moretitle: currentItem.ismore ? this.$t('message.ckgd') : this.$t('message.sq')
				    });
				console.log(this.OrderInfo.shopList[index], '当前')
			},
			hdchange() {},
			getOrderinfo() {
				const dataList = {
					cartMxIdList:  this.MxIdList,
					addressId: this.addressId
				}
				api({
					    url: `car/sell/goods/info?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post',
						data: dataList
					}).then(res => {
						if(res.code == 200) {
							this.$data.visible = true
							// console.log(res, '订单详细')
							this.OrderInfo = res.data.data
							if(this.OrderInfo.shopList.length > 0) {
								this.OrderInfo.shopList.forEach((item) => {
									item.ismore = false
									item.moretitle = this.$t('message.ckgd')
								})
							}
							console.log(this.OrderInfo, '当前订单明细')
						} else {
							uni.showToast({
								title: '状态码异常',
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					})
			}
		}
	}
</script>

<style lang="scss" scoped>
.OrderInfo{
	.addressInfo{
		width: 97%;
		margin: 0 auto;
		background-color: #fff;
		margin-top: 15rpx;
		border-radius: 10rpx;
		.noaddress{
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.leftaddress{
				margin-left: 10rpx;
				display: flex;
				align-items: center;
				.title{
					margin-left: 10rpx;
					font-size: 28rpx;
					color: #333333;
				}
			}
		}
		
		.address{
			min-height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 20rpx;
			.addressdetail{
				width: 520rpx;
				.userinfo{
					text{
						color: #696969;
						font-size: 28rpx;
					}
				}
			}
		}
		
	}
	.shopinfoList{
		margin-top: 15rpx;
		width: 100%;
		.shopitem{
			background-color: #fff;
			margin-bottom: 15rpx;
			padding: 0 20rpx;
			.shopdetail{
				padding-bottom: 5rpx;
				.shopname{
					height: 80rpx;
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					.shoptitle{
						height: 40rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #333333;
						line-height: 40rpx;
						font-style: normal;
						text-transform: none;
						margin-left: 15rpx;
						}
					}
				.mxlist{
					.mxitem{
						margin-bottom: 10rpx;
						.mxdetail{
							display: flex;
							justify-content: space-between;
							.proright{
								padding: 20rpx 0;
								margin-left: 15rpx;
								flex: 1;
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								.protop{
									display: flex;
									justify-content: space-between;
									.name{
										font-size: 26rpx;
										color: #717171;
										font-family: DIN-Regular;
									}
									.price{
										font-size: 30rpx;
										font-weight: 600;
										color: #717171;
										font-family: DIN-Regular;
										margin-left: 10rpx;
									}
								}
								.procenter{
									display: flex;
									justify-content: space-between;
									.name{
										font-size: 20rpx;
										color: #717171;
										font-family: DIN-Regular;
									}
									.price{
										font-size: 26rpx;
										font-weight: 600;
										color: #717171;
										font-family: DIN-Regular;
										margin-left: 10rpx;
									}
								}
								.probottom{
									display: flex;
									justify-content: flex-end;
									.price{
										font-size: 24rpx;
										font-weight: 600;
										color: #717171;
										font-family: DIN-Regular;
									}
								}
								
							}
						}
					}
				}
				
				.masks{
					margin: 15rpx 0;
					display: flex;
					align-items: center;
					height: 60rpx;
					.title{
						margin-right: 30rpx;
						text{
							width: 50rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 24rpx;
							height: 40rpx;
							line-height: 40rpx;
							color: #AAAAAA;
							font-style: normal;
							text-transform: none;
						}
					}
					.remark{
						// width: 84%;
						flex: 1;
						height: 40rpx;
						background-color: #F4F4F4;
						display: flex;
						align-items: center;
						padding-right: 20rpx;
						justify-content: flex-end;
						.MarksPro{
							width: 500rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							display: inline-block;
						}
					}
				}
				.line{
					width: 100%;
					height: 2rpx;
					background-color: #EEEEEE;
				}
				.firstfk{
					margin: 30rpx 0 15rpx 0;
					.firsttitle{
						height: 40rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 30rpx;
						color: #333333;
						line-height: 40rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
					.content{
						.huil{
							display: flex;
							align-items: center;
							justify-content: space-between;
							height: 100rpx;
						}
						.totalproprice{
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							height: 100rpx;
						}
						.zgyf{
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							height: 100rpx;
						}
						.cghj{
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							height: 100rpx;
						}
						.dgfwf{
							margin-top: 10rpx;
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							height: 60rpx;
						}
						.yhf{
							display: flex;
							justify-content: space-between;
							height: 60rpx;
						}
						.jgzj{
							display: flex;
							justify-content: flex-end;
							height: 60rpx;
							.value{
								margin-left: 10rpx;
								color: #FF4000;
							}
						}
						.yggjyf{
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							height: 60rpx;
						}
						.title{
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 24rpx;
							color: #ababab;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}
						.price{
							.usd{
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 22rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;
								margin-bottom: 8rpx;
							}
							.base{
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 22rpx;
								color: #ababab;
								font-style: normal;
								text-transform: none;
							}
						}
					}
				}
				
				.moreinfo{
					.hdfk{
						height: 80rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
					}
					.toubao{
						height: 80rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.text{
							width: 48rpx;
							height: 40rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 24rpx;
							color: #a7a7a7;
							line-height: 40rpx;
							font-style: normal;
							text-transform: none;
						}
						.gzwp{
							color: #a7a7a7;
							display: flex;
							align-items: center;
							text{
								margin-right: 10rpx;
							}
						}
					}
					
					.jgfw{
						height: 120rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						.jgfwtitle{
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 28rpx;
							color: #333333;
							font-style: normal;
							text-transform: none;
							margin-bottom: 5rpx;
						}
						.jgcontent{
							display: flex;
							align-items: center;
							letter-spacing: 2rpx;
							text{
								color: #ccc;
							}
						}
					}
					
					.thsb{
						min-height: 180rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						margin-bottom: 15rpx;
						.thsbtitle{
							margin-bottom: 10rpx;
						}
						.thsbcontent{
							color: #ccc;
							margin-bottom: 10rpx;
						}
						.thsblink{
							
						}
					}
					.checkbox{
						height: 80rpx;
						display: flex;
						align-items: center;
					}
				}
			}
		}
	}
}

.totalbottom{
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 160rpx;
	z-index: 9;
	background-color: #fff;
	box-shadow: 0 -5rpx 5rpx rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 20rpx 10rpx 10rpx  10rpx;
	.toptitle{
		display: flex;
		align-items: center;
		margin-left: 10rpx;
	}
	.bottomcontent{
		display: flex;
		justify-content: space-between;
		height: 100rpx;
		.left{
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: center;
			margin-left: 15rpx;
			.totalprice{
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
				.value{
					color: #FF4000;
					margin: 0 10rpx;
					font-size: 28rpx;
					font-weight: 500;
				}
				.mx{
					color: #0F82E0;
				}
			}
			.noyf{
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #777777;
				font-style: normal;
				text-transform: none;
			}
		}
		.rightbtn{
			width: 180rpx;
			margin-right: 30rpx;
		}
	}
}

.remarksinfo{
		width: 650rpx;
		min-height: 550rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				height: 120rpx;
				line-height: 120rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
			.content{
				height: 300rpx;
				margin-bottom: 20rpx;
			}
			
			.bottombtn{
				display: flex;
				justify-content: space-between;
				.btnbtn{
					width: 45%;
					.cancelbtn{
						color: #FF4000 !important;
						border: 1rpx solid #FF4000 !important;
					}
				}
			}
		}
	}
	
	.Popupinfo{
		width: 650rpx;
		min-height: 400rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				height: 100rpx;
				line-height: 100rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
			.content{
				min-height: 150rpx;
			}
			
			.bottombtn{
				display: flex;
				margin: 30rpx 0;
				
			}
		}
	}
	
	.PopupinfoMx{
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 99;
		margin-bottom: -80rpx;
		height: 1200rpx;
		position: relative;
		.PopupCon{
			width: 100%;
			margin: 0 auto;
			padding-top: 30rpx;
			.topCon{
				display: flex;
				justify-content: space-between;
				height: 80rpx;
				padding: 0 40rpx;
				border-bottom: 1rpx solid #efefef;
				.title{
					color: #000;
					font-weight: 500;
					font-size: 32rpx;
					letter-spacing: 1rpx;
				}
			}
			.feettitle{
				min-height: 60rpx;
				background-color: #f5fbff;
				color: #0691ff;
				padding: 0 40rpx;
				display: flex;
				align-items: center;
			}
			
			.content{
				margin-top: 20rpx;
				padding: 0 40rpx;
				.Ctitle{
					.title{
						color: #a7a7a7;
					}
				}
				.totalproprice{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 90rpx;
				}
				.zgyf{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 90rpx;
				}
				.cghj{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 90rpx;
				}
				.yhf{
					display: flex;
					justify-content: space-between;
					height: 60rpx;
				}
				
				.dgfwf{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 60rpx;
				}
				.yggjyf{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 60rpx;
				}
				.jsbz{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 60rpx;
				}
				.huil{
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					height: 60rpx;
				}
				
			}
			
			.Mxbottombtn{
				position: absolute;
				bottom: 100rpx;
				left: 50%;
				transform: translateX(-50%);
				padding: 0 10rpx;
				display: flex;
				justify-content: space-between;
				height: 100rpx;
				width: 100%;
				.left{
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: center;
					margin-left: 15rpx;
					.totalprice{
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						.value{
							color: #FF4000;
							margin: 0 10rpx;
							font-size: 28rpx;
							font-weight: 500;
						}
						.mx{
							color: #0F82E0;
						}
					}
					.noyf{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #777777;
						font-style: normal;
						text-transform: none;
					}
				}
				.rightbtn{
					width: 180rpx;
					margin-right: 30rpx;
				}
			}
		}	
	}

</style>