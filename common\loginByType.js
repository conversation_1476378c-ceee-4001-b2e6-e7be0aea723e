import {
	postRegisterSendCode,
	postLoginRegister,
	postModifySendCode,
	postLoginrModify,
	postCodeSendCode,
	postCodeInfo,
	postLoginInfo,
	postWxAuthLogin,
	wxPhoneLogin
} from '@/common/request/api/login.js'
import store from '@/store/index.js'
import md5 from 'js-md5';
//注册验证码
const registerCode = (_this,data)=>{
	postRegisterSendCode(data).then(res => {
		uni.hideLoading();
		if (res.code == 200) {
			_this.$toastApp('验证码已发送');
			_this.$refs.uCode.start();
		}else{
			_this.$toastApp(res.msg)
		}
	}).catch(err => {
		_this.$toastApp(err.msg)
	})
}
//修改密码获取验证码
const modifyCode = (_this,data)=>{
	postModifySendCode(data).then(res => {
		uni.hideLoading();
		if (res.code == 200) {
			_this.$toastApp('验证码已发送');
			_this.$refs.uCode.start();
		}else{
			_this.$toastApp(res.msg)
		}
	}).catch(err => {
		_this.$toastApp(err.msg)
	})
}
//手机号登录获取验证码
const loginCode = (_this,data)=>{
	postCodeSendCode(data).then(res => {
		uni.hideLoading();
		if (res.code == 200) {
			_this.$toastApp('验证码已发送');
			_this.$refs.uCode.start();
		}else{
			_this.$toastApp(res.msg)
		}
	}).catch(err => {
		_this.$toastApp(err.msg)
	})
}
//注册
const register = (_this)=>{
	_this.loading = true
	let data = {
		phone: _this.formData.phone,
		password: md5(_this.formData.phone + md5(_this.formData.password)),
		code:_this.formData.code,
		areaCode:_this.formData.areaCode,
		authCode:_this.authCode
	}
	postLoginRegister(data).then(res=>{
		_this.loading = false
		if(res.code == 200){
			_this.$toastApp(res.data.data)
			setTimeout(()=>{
				_this.type = 1
			},500)
		}
	})
}
//修改密码
const modify = (_this)=>{
	_this.loading = true
	let data = {
		phone: _this.formData.phone,
		password: md5(_this.formData.phone + md5(_this.formData.password)),
		code:_this.formData.code
	}
	postLoginrModify(data).then(res=>{
		_this.loading = false
		if(res.code == 200){
			_this.$toastApp(res.data.data)
			setTimeout(()=>{
				_this.type = 1
			},500)
		}
	})
}

const weixin = (_this) => {
	_this.loading = true
	uni.getProvider({
		service: 'oauth',
		success: function(res) {
			if (res.provider.includes('weixin')) {
				uni.login({
					provider: 'weixin',
					"onlyAuthorize": true,
					success: function(loginRes) {
						// 调用登录
						const loginData = {
							code: loginRes.code,
						}
						loginAfter(postWxAuthLogin(loginData), _this)

					},
					fail: error => {
						_this.$toastApp("授权失败，请重新授权")
					},
					complete() {
						_this.loading = false
					}
				});
			}
		},
	});
}

// 系统密码，手机号登录
const pwd = (_this) => {
	_this.loading = true
	let loginData = {
		phone: _this.formData.phone,
		password: md5(_this.formData.phone + md5(_this.formData.password)),
		areaCode: _this.formData.areaCode
	}
	loginAfter(postLoginInfo(loginData), _this)
}
// 手机验证码登录
const sms = (_this) => {
	_this.loading = true
	let loginData = {
		phone: _this.formData.phone,
		phoneCode:_this.formData.code
	}
	loginAfter(postCodeInfo(loginData), _this)
}
//手机号登录
const phoneNmuber = (_this) => {
	_this.phoneloading = true
	let data = {code:_this.code,authCode:_this.authCode}
	loginAfter(wxPhoneLogin(data),_this)
}
/**
 * @param {Object} loginPromise 接口调用的Promise
 * @param {Object} _this 页面实例
 */
function loginAfter(loginPromise, _this) {
	loginPromise.then(res => {
		if (res.code == 200) {
			if (res.data.data.token) {
				// 正常登录
				store.commit('setToken', res.data.data.token)
				uni.setStorageSync("token", res.data.data.token)
				store.commit('setLogin', true)
				store.dispatch('getUserInfo')
				let pages = getCurrentPages();
				let prevPage = pages[pages.length - 2] || {};
				if (!prevPage || prevPage.route === 'pageo/login/login') {
					_this.$toHome()
				} else {
					_this.$back()
				}
			}
		} else {
			_this.$toastApp(res.msg)
		}
		_this.loading = false
		_this.phoneloading = false
	}).catch(err => {
		console.error(err)
		_this.loading = false
		_this.phoneloading = false
		_this.$toastApp("授权失败，请重新授权")
	})
}

export default {
	registerCode,
	modifyCode,
	loginCode,
	register,
	modify,
	weixin,
	sms,
	pwd,
	loginAfter,
	phoneNmuber
}