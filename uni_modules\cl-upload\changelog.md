## 1.4.0（2023-07-04）
新增unicloud启用目录选项
## 1.3.9（2023-06-28）
修复属性选择器警告提示
## 1.3.8（2023-06-02）
手动上传没有可上传文件时返回空数组; 提高代码质量和可阅读性;
## 1.3.7（2023-05-22）
新增自定义视频封面配置; 新增添加按钮插槽
## 1.3.6（2023-05-22）
添加自定义图片资源配置
## 1.3.5（2023-05-04）
修复小程序cloudType为other的时候获取不到视频删除按钮焦点问题
## 1.3.4（2023-04-27）
修复小程序unicloud自动上传不自动同步数据问题
## 1.3.3（2023-04-27）
修复删除按钮层级问题
## 1.3.2（2023-04-24）
修复安卓APP配置cloudType:other 无法显示http封面问题
## 1.3.1（2023-04-19）
修改版本信息
## 1.3.0（2023-04-19）
修复手动上传已知问题; 新增删除前，上传前钩子函数
## 1.2.9（2023-03-22）
修复已知问题
## 1.2.8（2023-03-22）
修复已知问题
## 1.2.7（2023-03-21）
修复部分视频封面白屏问题
## 1.2.6（2023-03-21）
修复部分视频封面白屏问题
## 1.2.5（2023-03-08）
兼容vue3中v-model数据绑定
## 1.2.4（2023-03-06）
1.修复低版本微信小程序video图层兼容问题;
2.修复unicloud上传v-model不同步问题;
## 1.2.3（2023-02-02）
添加限制图片视频大小功能
## 1.2.2（2023-02-01）
兼容支付宝小程序手动上传视频
## 1.2.1（2023-01-31）
更新图片资源地址
## 1.2.0（2022-12-12）
兼容uniCloud上传
## 1.1.9（2022-12-12）
1. 添加最大视频限制
2. 优化部分功能
## 1.1.8（2022-12-09）
修复部分设备上传视频第一帧黑屏问题
## 1.1.7（2022-12-01）
修复h5上传开启压缩后一直显示“正在压缩中”问题
## 1.1.6（2022-11-24）
兼容支付宝小程序手动上传
## 1.1.5（2022-11-07）
添加加载配置
## 1.1.4（2022-11-07）
修复图片限制数量后可以多次选择最大数量图片问题
## 1.1.3（2022-10-28）
添加提示弹窗配置
## 1.1.1（2022-10-28）
修复已知问题
## 1.1.0（2022-08-25）
修复base64压缩问题
## 1.0.9（2022-08-25）
修复服务器返回数据格式问题
## 1.0.8（2022-08-01）
优化代码格式、逻辑
## 1.0.6（2022-07-30）
修改已知问题
## 1.0.5（2022-07-30）
1. aspect-ratio兼容问题，添加height属性保底
2. 添加监听上传进度变化事件
## 1.0.3（2022-07-30）
添加按钮控制，事件说明
## 1.0.2（2022-07-30）
优化服务器接口返回数据上传
## 1.0.1（2022-07-29）
初始化组件
