<template>
	<view>
		<newNavBar :title="$t('accountUser.yhk')" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<no-data :propStatus="propStatus" v-if="isShow"></no-data>
		<view v-if="!isShow">
			<view class="flex flex-center u-p-20 u-m-b-10" v-for="item in addressList" :key="item.bankId" style="background-color: #ffffff;">
				<view class="flex-1 u-m-r-40" @tap="selectAddress(item)">
					<u--text :text="item.cardNo" :lines="2"></u--text>
					<view class="flex justify-between align-center u-m-t-10">
						<text>{{item.bank}} / {{item.branch}}</text>
						<view class="flex flex-center">
							<text class="u-m-r-20">{{item.createTime}}</text>
							<!-- <text :class="item.sortNumber==0 ? 'is-default' : 'no-default'" @tap.stop="setDefault(item)">{{item.sortNumber==0 ? '默认地址' : '设为默认'}}</text> -->
						</view>
					</view>
				</view>
				<view class="flex flex-col justify-between align-center">
					<view class="u-m-b-20" >
						<u-icon name="edit-pen" size="18" @click="editAddress(item)"></u-icon>
					</view>
					<view>
						<u-icon name="trash" size="18" @click="deleteAddress(item)"></u-icon>
					</view>
				</view>
			</view>
			<view style="height: 160rpx;"></view>
		</view>
		<view class="bot-button">
			<u-button color="linear-gradient(to right,#2A3A88FF , #3c9cff)" :text="$t('accountUser.xzhyk')" @tap="editAddress('')"></u-button>
		</view>
		<u-modal 
		:show="deleteShow" 
		:title="$t('accountUser.ts')" 
		:content="$t('message.sctx')"
		showCancelButton 
		closeOnClickOverlay
		@confirm="confirmDeleteAddress" 
		@cancel="()=>{deleteShow = false}"
		@close="()=>{deleteShow = false}"></u-modal>
	</view>
</template>

<script>
	import {getAddress,postUpdateIsDefault,deletedAddress, deletedtxdata, postSavetx, getTxtype,
	deletedbd, postSavebd, postEditbd, postbdIsDefault
	} from '@/common/request/api/user.js'
  import store from '@/store'
  import api from '@/common/request/index.js'
  
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"

	export default{
		data(){
			return {
				pageNum:1,
				addressList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				deleteShow:false,
				loadingStatus: true,
				type:"",
				params: {
					page: 1,
					size: 10
				},
			}
		},
		onLoad(params) {
			if(params.type){
				this.type = params.type
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		created() {
			// this.getAddress()
		},
		onShow() {
			this.params.page = 1
			this.params.size = 10
			this.addressList = []
			this.getAddress()
		},
		onReachBottom() {
			// 下拉触底
			console.log('下拉触底')
			this.loadingStatus = true
			this.params.page ++
			this.getAddress()
		},
		methods:{
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAddress(){
				api({
					url: `mb/bd/get/list?page=${this.params.page}&size=${this.params.size}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '会员银行卡信息')
						if(res.data.data.list.length){
							if(this.addressList.length == res.data.data.totalCount) {
								// 已获取到全部数据
								setTimeout(() => {
									this.loadingStatus = false
								}, 2500)
								console.log(this.addressList, '全部的数据')
							} else {
								this.addressList = [...this.addressList, ...res.data.data.list]
								console.log(this.addressList, 'addressList')
								this.isShow = false
							}
						}else{
							this.propStatus = 0
						}
					}
				})
			},
			setDefault(value){
				if(value.sortNumber == 0) return
				postUpdateIsDefault(value.memberId).then(res=>{
					if(res.code == 200){
						this.getAddress()
					}
				})
			},
			editAddress(value){
				console.log(value, '数据ID')
				uni.navigateTo({
					url: `/pageo/bank_cards/savebank_cards?infoData=${JSON.stringify(value)}`,
				});
				
			},
			deleteAddress(value){
				console.log(value, '银行卡信息')
				// if(value.isDefault == 1){
				// 	uni.showToast({
				// 		title:'默认地址禁止删除',
				// 		icon:'none'
				// 	})
				// 	return
				// }
				this.deleteId = value.bankId
				this.deleteShow = true
			},
			confirmDeleteAddress(){
				// deletedAddress(this.deleteId).then(res=>{
				// 	if(res.code == 200){
				// 		this.getAddress()
				// 		this.deleteShow = false
				// 	}
				// })
				
				api({
				    url: `mb/bd/deleted/${this.deleteId}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							// title: '操作成功',
							title: $t('message.czcg'),
							icon: 'none'
						})
						this.deleteShow = false
						this.addressList = []
						this.params.page = 1
						this.params.size = 10
						this.getAddress()
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
				
			},
			selectAddress(item){
				// if(!this.type) return
				// store.commit('setEphemeral',item)
				// this.$back()
				if(this.type == 'tx') {
					uni.navigateBack({
						delta:1,
						success: () => {
							uni.$emit('getbankId',{
								current:item.bankId
							})
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.is-default{
		color: #ffffff;
		background-color: #000000;
		padding: 4rpx 10rpx;
		font-size: 20rpx;
	}
	.no-default{
		color: #000000;
		background-color: #ffffff;
		padding: 4rpx 10rpx;
		border: 1rpx solid #000000;
		font-size: 20rpx;
	}
</style>