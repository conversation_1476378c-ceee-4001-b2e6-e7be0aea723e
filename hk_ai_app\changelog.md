## v4.0.0（2023-11-10）
1. 手机端全新UI、极致体验优化
2. 首页轮播支持背景色自动切换
3. 分配页面支持全部分配查看
4. 钱包支持转账
5. 新增扫码登录及付款
6. 新增商品魔方
7. 购物车新增商品展示
8. 问答支持评论和点赞
9. 客服和快捷导航优化
10. 商品详情分享和收藏优化


## v3.0.3（2023-09-18）
1. 组合搭配支持展示商品
2. 商品列表去除封面错误提示


## v3.0.2（2023-09-11）
1. 用户基础信息填写，昵称判断有误修复
2. 订单售后退货地址错误修复
3. 新增微信小程序隐私弹窗说明


## v3.0.1（2023-09-03）
1. 用户基础信息web端错误修复
2. 关闭按钮多端兼容性处理
3. 下单页面积分使用提示优化


## v3.0.0（2023-08-28）
1. 新增微信小程序可以提示完善头像及昵称
2. ipad模型下uniapp顶部错位
3. 分销支持订单信息插件及数据统计GMV维度、我的团队新增搜索条件
4. 秒杀适配v3.0新版本
5. 购物车列表默认选中
6. 购物车抛物线优化、搜索页面新增页面搜索框，分类页面支持搜索进入独立页面
7. 组合搭配支持首页展示和组建封装
8. 数据列表读取避免重复请求
9. 分类页面支持标签展示
10. 多商户支持评分、资质查看
11. 开票默认值优化
12. form表单支付错误处理
13. 购物车无效商品支持选择删除
14. 快捷加购错误修复
15. 批量下单数量更新价格错误修复
16. 单页打开领取优惠券提示
17. 分包优化


## v2.3.3（2023-04-10）
* 商品列表使用统一组件
* 商品参数新增弹窗展示
* 初始访问登录页面优化
* 公共url打开支持地图、电话、外部小程序协议方式
* 适配手机底部横线
* 轮播兼容iphone圆角失效问题
* 用户中心菜单支持列表展示样式
* 搜索页面支持九方格和列表展示样式
* 分类支持参数指定跳转
* icon导航图标支持纯图片
* 分类和门店详情适配规格起购数及限购数
* 起购数和限购数提升 到商品规格级别
* 地址新增编号快速搜索选择
* 订单售后页面新增客服展示
* 购买和加购分离统一组件
* 提现初始错误修复
* 立即购买支持多商品
* 积分使用兑换不足提示
* 博客新增评论、点赞
* 新增组合搭配
* 新增列表快捷加入购物车
* 商品列表新增错误提示
* 商家详情新增搜索全站开关控制
* 门店详情新增扫码开关控制
* 新增多规格批量下单
* 插件分包处理


## v2.3.2（2022-11-30）
* 门店详情支持多规格直接加购
* 分类页面支持多规格直接加购+购物车操作
* 新增会员码
* 新增钱包付款码
* 新增个人资料修改
* 新增手机号码修改
* 新增账号注销
* 新增条码二维码生成组件
* 新增用户ID展示
* 购物车分离优化
* 适配微信小程序登录新规
* 可视化新增图文、图片魔方、自定义html组件，商品支持左图右文样式
* 下单时间优化、支持默认提示


## v2.3.1（2022-10-23）
* 支付宝获取地图权限优化
* 头条小程序分类不铺满问题修复
* 优化商品分类滑动方案
* 位置选择优化


## v2.3.0（2022-08-16）
* 左右滚动最后一个元素显示不全修复
* 适配paypal支付
* 会员购买支付优化
* 去除头条小程序自定义导航
* 新增虚拟订单快速提交订单
* 头条小程序支持一键获取手机号码登录
* 多商户首页支持自动模式
* 优惠券支持多商户
* 商品错误情况下新增返回按钮
* 限时秒杀优化
* 博客搜索页面分享优化


## v2.2.9（2022-07-11）
* 首页插件数据支持按顺序渲染
* 商品分类支持商品列表模式展示
* 商品详情支持默认选中第一个有效规格
* 商品详情快捷加购自动返回
* 下单地址限制优化
* 多商户首页支持多种样式展示
* 商品url地址使用后端生成、购买导航新增url事件、左侧返回优化
* 登录绑定手机返回优化
* 搜索页面优化、避免返回事件重复加载


## v2.2.8（2022-05-20）
* 活动配置首页推荐支持（图文、九方格、左右滚动）样式展示
* 多商户新增店铺认证资质展示
* 门店详情支持二级分类
* 可视化数据处理错误修复
* 退出仅清除用户信息、微信自动登录强制绑定账号循环修复
* 主题色样式class错误修复
* 适配快手小程序


## v2.2.7（2022-04-22）
* 用户地址支持地理位置选择和地址信息智能识别
* 登录返回上一页，h5支持微信自动登录
* 门店首页新增地理位置选择弹窗、门店详情优化
* 商品详情页面导航返回逻辑优化
* 首页搜索和导航固定控制优化
* 博客详情新增分享入口
* 门店详情新增返回按钮关闭开关（适合独立打包）


## v2.2.6（2022-04-07）
* 集成新的客服系统、商品页调整为底部导航入口
* 下单可直接使用门店次卡消费
* 门店详情数据改为分页模式、提高效率
* 商品详情新增相关门店列表入口
* 商品详情底部导航购物车新增可控开关
* 商品详情页提示优化
* 新增可关闭原始购买功能、仅可进入门店购买
* 标签详情分享地址id为空修复
* 支持设置默认下单类型
* 新增商品详情购物车展示开关（App.vue中设置）
* 新增分享及转发使用页面设置的默认图片及系统默认图片开关（App.vue中设置）


## v2.2.5（2022-03-10）
* 基础组件类库更新
* 商品详情新增智能工具插件信息提示
* 订单取消后隐藏支付按钮，细节优化
* 登录加提现优化
* 去除微信圈子组件
* 分享默认地址优化
* 新增门店独立首页和搜索页
* 新增分享及转发使用页面设置的图片开关


## v2.2.4（2022-02-16）
* 商品海报配置优化
* 博客支持首页展示及优化
* 新增门店列表和详情
* 商品支持自定义返回
* 支持自定义购买模式
* 适配第三方登录插件
* 下单支持0元不用选择支付方式
* 下单页面支持指定时间选择
* 系统参数读取优化


## v2.2.3（2021-12-13）
* 整体适配H5端
* 订单、钱包、会员等级支付优化适配
* 支持（账号、手机、邮箱）登录注册方式
* 分享逻辑优化全局处理
* 分销新增上级用户、阶梯返佣提示
* 新增独立新增错误页面
* 适配第三方登录插件
* 支持线下支付自定义信息展示
* 规格切换购买数量错误修复
* 富文本详情支持视频、超链接、图片预览


## v2.2.2（2021-11-23）
* 限时秒杀插件支持独立首页
* 活动配置插件支持优惠价格设定
* 标签插件
* 中间广告插件
* 弹屏广告插件
* 哀悼插件
* 文章支持分类页面
* 博客插件
* 分销、会员等级增强版、钱包全面支持小程序
* 用户授权获取用户信息API
* 可视化索引读取错误修复
* 支持菜鸟物流查询


## v2.2.1（2021-10-24）
* 支持微信小程序（首页、分类、购物车、用户中心、商品搜索、商品详情、订单确认、授权登陆、订单管理、订单售后、商品收藏、商品足迹、直播、签到、积分商城、多商户、钱包、批发）