/**
* 搜索
*/
.search {
    border: 2rpx solid;
    padding: 2rpx;
}

.search button {
    width: 140rpx;
    height: 56rpx;
    line-height: 56rpx;
    padding: 0;
}

.search input {
    height: 56rpx;
    line-height: 56rpx;
}

.search-btn {
    width: 148rpx;
}

.search-btn button {
    width: 100%;
    padding: 0;
    height: 64rpx;
    line-height: 64rpx;
}

/**
* 头部
*/
.header {
    padding: 20rpx 24rpx;
    font-size: 24rpx;
}

.shop-logo {
    width: 90rpx;
}

.base-bottom image {
    width: 28rpx;
    height: 28rpx;
}

.header .base {
    width: calc(100% - 110rpx);
}

.shop-title {
    line-height: 40rpx;
}

/**
* 导航
*/
.nav {
    height: 80rpx;
}

.nav .nav-scroll {
    float: right;
    width: calc(100% - 164rpx);
}

.nav .nav-scroll .item.par {
    height: 56rpx;
    line-height: 56rpx;
}

.nav .item {
    padding: 0 20rpx;
}

.nav-shop-category {
    padding-right: 32rpx !important;
    background-size: 28rpx 28rpx;
    height: 56rpx;
    line-height: 56rpx;
}

.nav .nav-items {
    left: calc(50% - 212rpx);
    top: 322rpx;
    z-index: 1;
    border-radius: 0 0 8rpx 8rpx;
    box-shadow: 0 12rpx 12rpx rgb(0 0 0 / 10%);
}

.nav .nav-items .item {
    padding: 20rpx;
}

/**
* 导航商品分类
*/
.nav-category {
    z-index: 1;
    box-shadow: 0 12rpx 12rpx rgb(0 0 0 / 10%);
    border-bottom-right-radius: 8rpx;
}

.nav-category .category-scroll {
    max-height: 600rpx;
}

.nav-category .item {
    padding: 20rpx;
}

/**
* 商品分类切换
*/
.shop-category-list .item {
    margin: 20rpx 0;
}

.shop-category-list .item {
    margin-left: 20rpx;
}

.shop-category-list .item:last-child {
    margin-right: 20rpx;
}