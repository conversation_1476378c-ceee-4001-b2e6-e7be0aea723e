/**
 * 领券中心
 */
.plugins-coupon-container {
    padding-top: 408rpx;
    height: calc(100vh - 500rpx - var(--status-bar-height) - 5px);
    /* #ifdef H5 */
    padding-top: 408rpx;
    height: calc(100vh - 560rpx);
    /* #endif */
}

.coupon-content {
    border-top: 42rpx solid #fff;
    border-radius: 32rpx 32rpx 0 0;
    min-height: calc(100% - 42rpx);
}

.coupon-content .item {
    padding: 0 24rpx 24rpx 24rpx;
}

.popup-bottom {
    box-shadow: inset 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
    z-index: 2;
}

.popup-btn {
    border: 2rpx solid #FF6E01;
    color: #FF6E01;
    border-radius: 22px;
    font-size: 32rpx;
    padding: 20rpx 0;
}

@media screen and (min-width: 960px) {
    .plugins-coupon-container {
        /* #ifdef H5 */
        padding-top: 450px;
        height: calc(100vh - 525px);
        /* #endif */
    }
}