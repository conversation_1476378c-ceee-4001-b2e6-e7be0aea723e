<template>
	<view>
		<newNavBar :title="title" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view v-if="visible">
			<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
				<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
					<u-form-item :label="$t('accountUser.xm')" prop="adUserName" borderBottom labelWidth="200rpx">
						<u-input maxlength="8" v-model="formData.adUserName" border="none" :placeholder="$t('accountUser.xm')" />
					</u-form-item>
					<u-form-item :label="$t('accountUser.sjh')" prop="adContactInfo" borderBottom labelWidth="200rpx">
						<view class="flex justify-between align-center" style="width: 100%;">
							
							<u-input  v-model="formData.adContactInfo" type="number" border="none" :placeholder="$t('accountUser.sjh')" clearable >
								<view slot="prefix" style="color: #ccc;display: flex;align-items: center;width: 150rpx;">
									<text style="margin-right: 10rpx;">+{{SupportedCou.areaCode}}</text>
									<u-icon name="arrow-down" color="#cccccc" size="16"></u-icon>
								</view>
							</u-input>
						</view>
					</u-form-item>
					<!-- <u-form-item label="国家" prop="adAddress4" borderBottom labelWidth="200rpx">
						<view class="country" style="color: #ccc;">
							{{formData.adAddress4}}
						</view>
					</u-form-item> -->
					<u-form-item label="省市区" borderBottom labelWidth="150rpx">
						<view class="flex justify-between align-center" style="width: 100%;" @tap="()=>{pickerShow = true}">
							<view class="u-m-r-20">
								<u--text v-if="adAddressP" :text="adAddressP" :lines="1"></u--text>
								<u--text v-else text="省市区" color="#c0c4cc"></u--text>
							</view>
							<u-icon name="arrow-right" color="#c0c4cc"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="详细地址" prop="adAddressDetails" borderBottom labelWidth="150rpx">
						<u--textarea v-model="formData.adAddressDetails" placeholder="详细地址" autoHeight border="none" clearable
							disableDefaultPadding></u--textarea>
					</u-form-item>
					<u-form-item label="服务站" prop="adAddress5" borderBottom labelWidth="150rpx">
						<u--textarea v-model="formData.adAddress5" placeholder="请输入服务站" autoHeight border="none" clearable
							disableDefaultPadding></u--textarea>
					</u-form-item>
					
					<u-form-item label="地址照片(最多4张)" borderBottom labelWidth="300rpx" prop="transactionUrl">
						<!-- <view style="border: 1rpx dashed #ccc;width: 100rpx;height: 100rpx;" v-if="formData.transactionUrl == ''">
							<image style="width: 100rpx;height: 100rpx;"@tap="upload_img"
							 :src="common_static_url"></image>
						</view>
						<image v-if="formData.transactionUrl !== ''" style="width: 200rpx;height: 200rpx;"@tap="upload_img"
						 :src="formData.transactionUrl"></image> -->
						 <view style="display: flex;flex-wrap: wrap;">
						 	<view class="imglist" style="display: flex;flex-wrap: wrap">
						 		<view class="imgitem" v-for="(item, index) in formData.adAddress6List" :key="index" style="margin-right: 15rpx;margin-bottom: 10rpx;position: relative;">
						 			<image :src="item" mode="" style="width: 150rpx;height: 150rpx;border-radius: 30rpx;"></image>
									<image src="https://file.36sm.cn/xtjyjt/images/common/delicon.png" style="width: 30rpx;height: 30rpx;position: absolute;top: -10rpx;right: -10rpx;" @tap="delimg(item,index)"></image>
						 		</view>
								<view style="border: 1rpx dashed #ccc;width: 150rpx;height: 150rpx;">
									<image style="width: 150rpx;height: 150rpx;"@tap="upload_img" src="https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png"></image>
								</view>
						 	</view>
						 </view>
					</u-form-item>
					
					
					
					<u-form-item :label="$t('accountUser.mr')" labelWidth="150rpx">
						<u-switch v-model="formData.sortNumber" :activeValue="0" :inactiveValue="1" activeColor="#000000"
							size="18"></u-switch>
					</u-form-item>
				</u--form>
			</view>
			<view class="u-p-20">
				<u-button :text="$t('accountUser.bc')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
		
		<u-picker :show="pickerShow" ref="uPicker" :columns="columns" keyName="name" :itemHeight="50" closeOnClickOverlay
			@cancel="()=>{pickerShow = false}" @close="()=>{pickerShow = false}" @change="changeEvent" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
	import {
		getAreaList
	} from '@/common/request/api/map.js'
	import {
		postSaveAddress,
		postUpdateAddress
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				title: '新增地址',
				pickerShow: false,
				columns: [
					[],
					[],
					[]
				],
				type:0,
				loading:false,
				// common_static_url: require('https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png'),
				formData: {
					// 收货地址省
					adAddress1: "",
					
					// 收货地址市
					adAddress2: "",
					
					// 收货地址区/县
					adAddress3: "",
					
					// 收货地址国家
					adAddress4: "",
					
					
					// 收货地址详情
					adAddressDetails: "",
					
					// 服务站
					adAddress5: "",
					
					// 收货地址联系人区号(中国给86)
					adContactArea: "",
					
					// 收货地址联系人号码
					adContactInfo: "",
					
					// 收货地址联系人名称
					adUserName: "",
					// 地址图片
					adAddress6List: [
						// "https://testksjjkl.oss-cn-hangzhou.aliyuncs.com/.jpg/2024/12/09/b524cca5535d4d01853708f030b5f002.jpg"
					],
					// 收货地址ID
					addressId: "",
					
					// 是否设置为默认地址 0-默认 1-不是默认
					sortNumber: 1, 
					
				},
				// 省市区拼接
				adAddressP: '',
				rules: {
					adUserName: {
						required: true,
						// message: '请输入收货人姓名',
						message: this.$t('message.qscshrxm'),
						trigger: ['blur']
					},
					// adContactInfo: [
					// 	{
					// 		required: true,
					// 		// message: '请输入手机号',
					// 		message: this.$t('message.qsrsjh'),
					// 		trigger: ['blur'],
					// 	},
					// 	{
					// 		validator: (rule, value, callback) => {
					// 			return uni.$u.test.mobile(value);
					// 		},
					// 		// message: '手机号码不正确',
					// 		message: this.$t('message.sjhbzq'),
					// 		trigger: ['blur'],
					// 	}
					// ],
					adContactInfo: [
						{
							required: true,
							// message: '请输入手机号',
							message: this.$t('message.qsrsjh'),
							trigger: ['blur'],
						},
						// {
						// 	validator: (rule, value, callback) => {
						// 		 // const phoneRegex = /^\+855\d{9}$/;
						// 		 const phoneRegex = '/^' + this.SupportedCou.regex + '$/';
						// 		 console.log(phoneRegex)
						// 		// return uni.$u.test.mobile(value);
						// 		if (phoneRegex.test(value)) {
						// 			callback()
						// 		} else {
						// 			callback(new Error(this.$t('message.sjhbzq')))
						// 		}
						// 	},
						// 	// message: '手机号码不正确',
						// 	message: this.$t('message.sjhbzq'),
						// 	trigger: ['blur'],
						// }
					],
					adAddressDetails: {
						required: true,
						// message: '请输入详细地址',
						message: this.$t('message.qsrxxdz'),
						trigger: ['blur']
					},
					adAddress5: {
						required: true,
						message: '请输入服务站',
						// message: this.$t('message.qsrxxdz'),
						trigger: ['blur']
					}
				},
				SupportedCou: {},
				visible: false
			}
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			}
		},
		onLoad(opt) {
			console.log(opt, 'opt')
			if(opt.type == 1) {
				console.log('编辑地址')
				this.title = this.$t('accountUser.bjdz')
				this.type = 1
				const Address = JSON.parse(opt.address)
				this.formData = {...Address}
				
				console.log(this.formData, '地址详情信息')
				
				this.adAddressP = this.formData.adAddress1 + this.formData.adAddress2 + this.formData.adAddress3
			} else {
				console.log('新增地址')
				this.title = this.$t('accountUser.xzdz')
				this.type = 0
			}
			// if (JSON.parse(opt.address)) {
			// 	const Address = JSON.parse(opt.address)
			// 	console.log(Address, '地址详情')
			// 	this.title = '编辑地址'
			// 	this.type = 1
			// 	this.formData = {...Address}
			// } else {
			// 	this.title = '新增地址'
			// 	this.type = 0
			// }
		},
		onShow() {
			// this.visible = false
			this.getSupportC()
		},
		onReady() {
			// this.$refs.uForm.setRules(this.rules)
		},
		created() {
			this.getAreaList()
		},
		methods: {
			delimg(item, index) {
				this.formData.adAddress6List.splice(index, 1)
			},
			// 获取手机号支持的国家
			getSupportC() {
				api({
					    url: `bs/dl/zc/gj?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'get',
					}).then(res => {
						if(res.code == 200) {
							this.SupportedCou = res.data.data.find(item => item.areaCode == '86');
							console.log(this.SupportedCou, 'SupportedCou手机号支持的国家');
							this.formData.adAddress4 = this.SupportedCou.zhName
							this.formData.adContactArea = this.SupportedCou.areaCode
							setTimeout(() => {
								this.visible = true
							}, 1000)
						} else {
							uni.showToast({
								title: this.$t('message.ztmyc'),
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					})
			},
			upload_img() {
				if(this.formData.adAddress6List.length >= 4) {
					uni.showToast({
						title: '至多上传4张!',
						icon: 'none'
					})
					return
				}
				uni.showLoading({
					title: "上传文件"
				})
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						// console.log(res, 'res')
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							// url: 'http://************/khrWeb/oss/hteditor/upload',
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								this.formData.adAddress6List.push(Data.data.data.fileUrl)
								console.log(this.formData.adAddress6List, '地址图片')
								if (Data.code == 200) {
									uni.showToast({
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								} else {
					                // 上传失败
					                uni.showToast({
					                    title: "上传失败",
					                    icon: 'none'
					                });
					            }
					            uni.hideLoading();
							},
					        fail: () => {
					            // 上传失败的处理
					            uni.showToast({
					                title: "上传失败",
					                icon: 'none'
					            });
					            uni.hideLoading();
					        }
						})
					},
					fail: () => {
					    // 用户取消选择图片
					    uni.hideLoading();
						// uni.showToast({
						//     title: this.$t('message.yqx'),
						//     icon: 'none'
						// });
					}
				})
			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			async getAreaList(id=0,i=0) {
				let params = {
					parentId:id
				}
				let res1 = await getAreaList(params)
				if(res1.code == 200){
					console.log(res1, '地区')
					if(i==0){
						this.$set(this.columns,0,res1.data.data)
						params = {parentId:this.columns[0][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,1,res2.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res3 = await getAreaList(params)
						this.$set(this.columns,2,res3.data.data)
					}else if(i==1){
						this.$set(this.columns,1,res1.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,2,res2.data.data)
					}else if(i==2){
						this.$set(this.columns,2,res1.data.data)
					}
				}
			},
			changeEvent(e) {
				const {
					columnIndex,
					value,
					values,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex < 2) {
					this.getAreaList(value[columnIndex].areaId,columnIndex+1)
				}
			},
			confirm(e) {
				console.log('confirm', e)
				const {value} = e
				this.adAddressP = value.filter(a=>a != undefined).map(a=>a.name).join("")
				const province = value[0]?.name
				const city = value[1]?.name
				const district = value[2]?.name
				this.formData.adAddress1 = value[0]?.name
				this.formData.adAddress2 = value[1]?.name
				this.formData.adAddress3 = value[2]?.name
				
				console.log(province, city, district, '省 市 区')
				console.log(this.formData, 'formData')
				this.pickerShow = false
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res){
						
						const phoneRegex = new RegExp('^' + this.SupportedCou.regex + '$');
						 console.log(phoneRegex, '校验规则')
						 const phone = '+' + this.SupportedCou.areaCode  + this.formData.adContactInfo
						 
						 console.log(phone, 'phone')
						if (phoneRegex.test(phone)) {
						} else {
							uni.showToast({
								title: '手机号格式不对',
								icon:'none'
							})
							return
						}
						
						
						if(this.formData.adAddress6List.length == 0) {
							uni.showToast({
								title: '请至少上传一张地址照片',
								icon:'none'
							})
							return
						}
						if(!this.adAddressP) {
							uni.showToast({
								title: '请选择省市区',
								icon:'none'
							})
							return
						}
						
						console.log(this.formData, '提交参数')
						
						// return
						if(this.type == 1) {
							this.UpdateAddress()
						} else {
							this.SaveAddress()
						}
						
						// let fn = this.type ? postUpdateAddress : postSaveAddress
						// this.loading = true
						// fn(this.formData).then(res=>{
						// 	this.loading = false
						// 	if(res.code==200){
						// 		uni.showToast({
						// 			title:this.title + '成功',
						// 			icon:'none'
						// 		})
						// 		setTimeout(() => {
						// 			this.$back()
						// 		}, 500)
						// 	}
						// })
					}
				})
			},
			// 更新地址
			UpdateAddress() {
				// 更新
				this.loading = true
				setTimeout(() => {
					this.loading = false
				},2000)
				api({
					url: `mb/update/address`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '编辑成功',
							title: this.$t('message.bjcg'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
			},
			// 保存地址
			SaveAddress() {
				this.loading = true
				setTimeout(() => {
					this.loading = false
				},2000)
				api({
					url: `mb/save/address`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '新增成功',
							title: this.$t('message.xzsc'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
				
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>