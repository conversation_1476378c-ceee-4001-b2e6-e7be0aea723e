<template>
	<view class="page">
		<view class="search">
			<uni-nav-bar fixed="true" backgroundColor="#F2F6FA" height="100rpx">
				<uni-search-bar class="searchBar" placeholder="搜索" @focus="focus" @blur="blur" @input="input"
					@confirm="search" @clear="clearData">
				</uni-search-bar>
			</uni-nav-bar>
		</view>
		<view class="banner" v-show="searchPage">
			<view class="banner_top">
				<component-banner :propData="propData" @changeBanner="change_banner"></component-banner>
			</view>
		</view>
	</view>
</template>

<script>
	import componentBanner from '../../components/slider/slider.vue'
	export default {
		data() {
			return {
				searchPage: true,
				searchVal: ""
			}
		},
		props: {
			propData: {
				type: Array,
				default: function() {
					return []
				},
			},
			//searchType 0:数字人资讯 1:商品
			searchType: {
				type: String,
				default: ""
			}
		},
		methods: {
			change_banner() {},
			focus() {
				switch (this.searchType) {
					case "0":
						uni.$emit('closePage');
						break;
					case "1":
						uni.$emit('goodsClose')
						break;
					default:
						return;
				}
				this.searchPage = false
			},
			blur() {
				switch (this.searchType) {
					case "0":
						uni.$emit('openPage')
						break;
					case "1":
						uni.$emit('goodsOpen')
						break;
					default:
						return;
				}
				this.searchPage = true
			},
			clearData() {
				this.searchVal = ""
				switch (this.searchType) {
					case "0":
						this.get_news_search();
						break;
					case "1":
						this.get_goods_search()
						break;
					default:
						return;
				}
				this.searchPage = true
			},
			input() {},
			get_news_search() {
				this.$HTTP({
					url: 'v1/hteditor/bs/get/news/list',
					method: "GET",
					data: {
						projectName: this.searchVal
					}
				}).then((res) => {
					if (res.code == 200) {
						uni.$emit('searchResult', {
							searchList: res.data.data.list
						})
					} else {
						uni.showToast({
							title: "新闻请求数据",
							icon: 'none'
						})
					}
				}).catch((err) => {
					uni.showToast({
						title: "出错了",
						icon: "none"
					})
				})
			},
			get_goods_search() {
				this.$HTTP({
					url: 'v1/sp/product/get/list',
					method: "GET",
					data: {
						ProductName: this.searchVal
					}
				}).then((res) => {
					if (res.code == 200) {
						uni.$emit('goodsResult', {
							goodsList: res.data.data.list
						})
					} else {
						uni.showToast({
							title: "商品请求数据",
							icon: 'none'
						})
					}
				}).catch((err) => {
					uni.showToast({
						title: "出错了",
						icon: "none"
					})
				})
			},
			search(e) {
				this.searchVal = e.value
				switch (this.searchType) {
					case "0":
						this.get_news_search();
						break;
					case "1":
						this.get_goods_search()
						break;
					default:
						break;
				}
			}
		},
		components: {
			componentBanner
		},
		options: {
			styleIsolation: 'shared', // 解除样式隔离
		},

	}
</script>

<style scoped lang="scss">
	.page {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: #F2F6FA;
	}

	.search {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 95%;

		/deep/ .uni-navbar__content {
			border: none;
		}

		/deep/ .uni-navbar__header-btns {
			display: none;
		}

		.searchBar {
			width: 100%;
		}

		/deep/ .uni-searchbar {
			padding-top: 15rpx !important;
		}

		/deep/ .uni-searchbar__box-search-input {
			padding-left: 25rpx;
			border-left: 1rpx solid #D8D8D8;
			margin-left: 10rpx;
		}

		/deep/ .uni-searchbar {
			width: 100%;
			padding: 10rpx 0;
		}

		/deep/ .uni-searchbar__box {
			justify-content: flex-start;
			background-color: #FFFFFF !important;
			height: 73rpx;
			width: 100%;
		}

		/deep/ .uni-navbar__header-container {
			padding: 0 !important;
		}

		/deep/ .uni-searchbar__text-placeholder {
			padding-left: 25rpx;
			border-left: 1rpx solid #D8D8D8;
		}

		/deep/ .uni-input-form {
			padding-left: 25rpx;
			border-left: 1rpx solid #D8D8D8;
		}

		/deep/ .uni-searchbar__cancel {
			display: none;
		}

		/deep/ .uni-input-placeholder {
			padding-left: 25rpx;
			border-left: 1rpx solid #D8D8D8;
		}
	}

	.banner {
		width: 700rpx;
		height: 300rpx;
	}

	.banner_top {
		margin-top: 15rpx;
	}
</style>