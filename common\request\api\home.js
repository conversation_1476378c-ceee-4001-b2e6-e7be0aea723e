// 首页相关api接口

import api from '../index.js'

const location = uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh';

// 获取系统的交易信息
// export const getJyInfo= () =>
// 	api({
// 		url: 'mb/base/get/jy/list?location=CN',
// 		header: {
// 			"jarepair-platform": uni.getStorageSync("token")
// 		},
// 	})

export const getJyInfo = () =>
  api({
    url: `mb/base/get/jy/list?location=${location}`,  // 使用动态 location
    header: {
      "jarepair-platform": uni.getStorageSync("token")
    },
  })


	
//  首页轮播图
 // export const getBannerList = data =>
 // 	api({
 // 		url: 'bs/banner/get/list?location=CN&page=1&size=20',
 // 		header: {
 // 			"jarepair-platform": uni.getStorageSync("token")
 // 		},
 // 		method: 'get',
 // 		data
 // 	})
	
	export const getBannerList = data =>
	  api({
	    url: `bs/banner/get/list?location=${location}&type=${data.type}&page=1&size=20`,  // 使用动态 location
	    header: {
	      "jarepair-platform": uni.getStorageSync("token")
	    },
	    method: 'get',
	    data
	  })

	
	
	
// 获取一级的商品类型
 // export const getOneList = data =>
 // 	api({
 // 		url: 'bs/code/get/one/list?location=CN&page=1&size=20',
 // 		header: {
 // 			"jarepair-platform": uni.getStorageSync("token")
 // 		},
 // 		method: 'get',
 // 		data
 // 	})
	
	
	export const getOneList = data =>
	  api({
	    url: `bs/code/get/one/list?location=${location}&page=1&size=10`,  // 使用动态 location
	    header: {
	      "jarepair-platform": uni.getStorageSync("token")
	    },
	    method: 'get',
	    data
	  })
	
	
	
// 获取活动的名称和商品列表
// export const getMarket = data =>
//  	api({
//  		url: 'ac/mk/get/market?location=CN&page=1&size=20',
//  		header: {
//  			"jarepair-platform": uni.getStorageSync("token")
//  		},
//  		method: 'get',
//  		data
//  	})
	
	
export const getMarket = data =>
  api({
    url: `ac/mk/get/market?location=${location}&page=1&size=20`,  // 使用动态 location
    header: {
      "jarepair-platform": uni.getStorageSync("token")
    },
    method: 'get',
    data
  })
	
// 获取推荐的瀑布流数据
// export const getRecommend = data =>
//  	api({
//  		url: 'ac/mk/get/recommend/info?location=CN&page=1&size=20',
//  		header: {
//  			"jarepair-platform": uni.getStorageSync("token")
//  		},
//  		method: 'get',
//  		data
//  	})
	
	export const getRecommend = data =>
	  api({
	    url: `ac/mk/get/recommend/info?location=${location}&page=1&size=20`,  // 使用动态 location
	    header: {
	      "jarepair-platform": uni.getStorageSync("token")
	    },
	    method: 'get',
	    data
	  })
	
	
	// 获取活动分类名称列表数据
	
 //  export const getFiled = data =>
 //   	api({
 //   		url: 'ac/mk/get/field?location=CN',
 //   		header: {
 //   			"jarepair-platform": uni.getStorageSync("token")
 //   		},
 //   		method: 'get',
 //   		data
 //   	})
	
	
	export const getFiled = data =>
	  api({
	    url: `ac/mk/get/field?location=${location}`,  // 使用动态 location
	    header: {
	      "jarepair-platform": uni.getStorageSync("token")
	    },
	    method: 'get',
	    data
	  })