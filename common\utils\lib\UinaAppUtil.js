import store from '@/store'
import { objToUri } from '../utils'
// 消息提示
const Toast = {
	duration: 1000 // 弹窗显示时间
}
export default {
	init(Vue) {
		/**
		 * 此方法在APP下使用原生消息提示
		 * @method $toastApp
		 * @for UinAppUtil
		 * @param {msg} 提示内容
		 */
		const $toastApp = function(msg) {
			// #ifdef APP-PLUS
			plus.nativeUI.toast(msg)
			// #endif
			// #ifndef APP-PLUS
			$toast(msg)
			// console.warn("$toastApp 此方法APP使用最佳")
			// #endif
		}
		
		/**
		 * 消息提示
		 * @method $toast
		 * @for UinAppUtil
		 * @param {title, ok, mask, fail}  内容,icon类型,遮罩,失败回调,成功回调
		 */
		const $toast = function(msg, iType, mask, ok, fail) {
			uni.showToast({
				title: msg,
				icon: 'none',
				mask: mask || false,
				image: iType ? iType : '',
				duration: Toast.duration,
				success: ok,
				fail: fail
			})
		}
		/**
		 * 去登陆页面
		 */
		const $toLogin = function(callback) {
			// #ifdef APP-PLUS || H5 || PREVIEW
			uni.navigateTo({
				url: '/pages/public/account/appLogin',
				animationType: 'slide-in-bottom'
			})
			typeof callback == 'function' && callback()
			// #endif
			// // #ifdef MP-WEIXIN
			// if(Mp_To_Login) { // 需要提示
			// 	if(showLoginModel) { return }
			// 	let pages = getCurrentPages()[0]
			// 	if(pages && !Ignore_Auth_Pages.includes(pages.route)) {
			// 		showLoginModel = true
			// 		uni.showModal({
			// 			title: Vue.prototype.$t('common.您还未登录'),
			// 			// content: '您还未登录',
			// 			confirmText: Vue.prototype.$t('common.去登录'),
			// 			cancelColor: '#8c8c8c',
			// 			confirmColor: getApp().globalData.cssPrimaryColor || Vue.prototype.$cssPrimaryColor,
			// 			success: function(res) {
			// 				if (res.confirm) {
			// 					uni.navigateTo({
			// 						url: '/pages/public/account/wxLogin',
			// 						animationType: 'slide-in-bottom'
			// 					})
			// 				}
			// 				typeof callback == 'function' && callback()
			// 			},
			// 			complete: function () {
			// 				showLoginModel = false
			// 			}
			// 		})
			// 	}
			// } else {
			// 	uni.navigateTo({
			// 		url: '/pages/public/account/wxLogin',
			// 		animationType: 'slide-in-bottom'
			// 	})
			// 	typeof callback == 'function' && callback()
			// }
			// // #endif
		}
		/**
		 * 去首页页面
		 */
		const $toHome = function(callback) {
			$switchTab('/pages/home/<USER>')
		}
		/**
		 * 页面跳转
		 * @method $goTo
		 * @for UinAppUtil
		 * @param {url, hasLogin, options}  跳转链接,是否需要登录,参数
		 */
		const $goTo = function(url, hasLogin = false, options = {}) {
			if (typeof hasLogin !== 'boolean') {
				[hasLogin, options] = [false, hasLogin]
			}
			let goToUrl = "";
			if(Object.keys(options).length > 0) {
				if(url.includes("?")){
					goToUrl = `${url}&${objToUri(options)}`
				} else {
					goToUrl = `${url}?${objToUri(options)}`
				}
			} else {
				goToUrl = url
			}
			let loginState = store.state.user.isLogin
			if (hasLogin && !loginState) { // 未登录
				$toLogin()
				return
			}
			uni.navigateTo({
				url: goToUrl,
				success: (res) => {
					
				},
				fail: (err) => {
					console.log('navigateTo', err)
				}
			})
		}
		
		/**
		 * 页面跳转-关闭当前
		 * @method $goRedirectTo
		 * @for UinAppUtil
		 * @param {url, hasLogin, options}  跳转链接,是否需要登录,参数
		 */
		const $goRedirectTo = function(url, hasLogin = false, options = {}) {
			if (typeof hasLogin !== 'boolean') {
				[hasLogin, options] = [false, hasLogin]
			}
			let loginState = store.state.user.isLogin
			let goToUrl = Object.keys(options).length > 0 ? `${url}?${objToUri(options)}` : url
			if (hasLogin && !loginState) {
				$toLogin()
				return
			}
			// console.log('不需要登录', hasLogin);
			uni.redirectTo({
				url: goToUrl,
				fail: (err) => {
					console.log('redirectTo', err)
				}
			})
		}
		
		/**
		 * 页面跳转-自定义跳转tabbar页
		 * @method $switchTab
		 * @for UinAppUtil
		 * @param {url,options}  跳转链接,参数
		 */
		const $switchTab = function(url, options = {}) {
			// #ifdef MP-WEIXIN
			uni.switchTab({
				url
			})
			// #endif
			// #ifndef MP-WEIXIN
			let goToUrl = Object.keys(options).length > 0 ? `${url}?${objToUri(options)}` : url
			uni.reLaunch({
				url: goToUrl
			})
			// #endif
		}
		
		/**
		 * 页面返回方法
		 */
		const $back = function(delta = 1) {
			let pages = getCurrentPages()
			let prevPage = pages[pages.length - (1 + delta)]
			if (!prevPage) {
				$switchTab('/pages/home/<USER>')
			} else {
				uni.navigateBack({
					delta
				})
			}
		}
		Vue.prototype.$toastApp = $toastApp
		Vue.prototype.$toast = $toast
		Vue.prototype.$toLogin = $toLogin
		Vue.prototype.$toHome = $toHome
		Vue.prototype.$goTo = $goTo
		Vue.prototype.$goRedirectTo = $goRedirectTo
		Vue.prototype.$switchTab = $switchTab
		Vue.prototype.$back = $back
	}
}
