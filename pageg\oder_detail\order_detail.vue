<template>
	<view class="page">
		<view class="header" :style="{ 'lineHeight': navBarHeight + 'rpx' }">
			<view class="box-bg">
				<view class="back" @click="goBack">
					<image src="https://file.36sm.cn/xtjyjt/images/common/bback.png"
						style="width: 22rpx;height: 33rpx;"></image>
				</view>
				<view class="viewtitle">
					<text>订单详情</text>
				</view>
				<view class="">
				</view>
			</view>
		</view>
		<view class="OrderDetail">
			<!-- 预约订单才展示店铺信息 -->
			<view class="shopinfo" v-if="orderDettail.typeOrder == 0">
				<view class="shopde">
					<view class="topinfo">
						<view class="leftV">
							<text class="placeName">
								{{ shopDetail.placeName }}
							</text>
							<view class="distance">
								<image src="/static/images/navgo.png" mode=""
									style="width: 28rpx;height: 28rpx;margin-right: 4rpx;">
								</image>
								<view class="">
									<text>{{ shopDetail.jlInfo }}</text>
								</view>
							</view>
						</view>
						<view class="rightV" @click="callphone">
							<image src="/static/images/phone.png" mode="" style="width: 50rpx;height: 50rpx"></image>
						</view>
					</view>

					<view class="bottominfo">
						<view class="yysj">
							<view class="busineTime">
								<text>10:00-20:00</text>
							</view>
							<view class="line"></view>
							<view class="nearest">
								<text>就近</text>
							</view>
						</view>

						<view class="shpDetail" @click="openlocation(shopDetail)">
							<view class="content">{{ shopDetail.adDetails }}</view>
							<image src="/static/images/address.png" mode="" style="width: 48rpx;height: 48rpx;"></image>
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="orderinfo" v-if="orderDettail.orderStatus ==4 && !isQrcode">
				<view class="Storeinfo">
					<view class="storeName">
						<view class="name">
							<text>核销码</text>
						</view>
					</view>
				</view>
				<view class="baseImg" style="display: flex;align-items: center;justify-content: center;">
					<image :src="'data:image/jpg;base64,' + baseImg" style="width: 400rpx;height: 400rpx;"></image>
				</view>
			</view> -->

			<view class="orderinfo">
				<view class="Storeinfo">
					<view class="storeName">
						<view class="name">
							<text>订单信息</text>
						</view>
					</view>
				</view>
				<view class="mxList">
					<view class="mxitem" v-for="(items, i) in orderDettail.mxList" :key="i">
						<view class="mxdetail">
							<view class="">
								<image :src="items.productMainImg" mode=""
									style="width: 150rpx; height: 150rpx;border-radius: 50%;">
								</image>
							</view>
							<view class="right">
								<view class="righttop">
									<view class="proname">
										{{ items.productTitle }}
									</view>
									<!-- <view class="serviceTime">
										<text>服务开始倒计时: 48小时16分</text>
									</view> -->
								</view>
								<view class="rightcenter">
									<view class="">
										<text>{{ items.extendDt1 || '预约的日期数据' }}</text>
									</view>
									<!-- 商品类型的订单显示数量 -->
									<view class="sellQuantity" v-if="orderDettail.typeOrder == 1">
										<text>× {{ items.sellQuantity }}</text>
									</view>
								</view>
								<view class="skudetail">
									<view class="sku">
										{{ items.extendDt2 || '预约的时间数据' }}
									</view>
									<view class="proPrice">
										￥ {{ items.payOrgPriceTrade }}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="totalPrice" v-if="item.orderStatus == 1">
						<text class="statusName">{{ item.orderStatusName }}:</text>
						<text class="pricevalue">￥ {{ item.payAmountTrade }}</text>
					</view>

					<view class="totalPrice" v-if="orderDettail.typeOrder == 1">
						<text class="statusName">{{ orderDettail.orderStatusName }}:</text>
						<text class="pricevalue">￥ {{ orderDettail.payAmountTrade }}</text>
					</view>
				</view>
				<view class="totalPrice" v-if="orderDettail.orderStatus == 1">
					<text class="statusName">{{ orderDettail.orderStatusName }}:</text>
					<text class="pricevalue">￥ {{ orderDettail.payAmountTrade }}</text>
				</view>

			</view>

			<view class="payinfo">
				<view class="infopay">
					<view class="paytitle">
						<view class="name">
							<text>支付信息</text>
						</view>
					</view>
					<view class="ordernum">
						<view class="title">
							<text>订单编号</text>
						</view>
						<view class="rightN"  @click="CopyID(orderDettail.orderId)">
							<text>{{ orderDettail.orderId }}</text>
							<image src="https://file.36sm.cn/jaxinfo/2025/02/26/593681d8bf134f65826e97ed0557721f.png"
								mode="" style="width: 30rpx;height: 30rpx;margin-left: 10rpx;"></image>
						</view>
					</view>
					<view class="paytype">
						<view class="title">
							<text>支付方式</text>
						</view>
						<view class="rightN">
							<text>微信支付</text>
						</view>
					</view>

					<view class="paytype">
						<view class="title">
							<text>付款金额</text>
						</view>
						<view class="rightN">
							<text>￥{{ orderDettail.payAmountTrade }}</text>
						</view>
					</view>
					<view class="paytime">
						<view class="title">
							<text>支付时间</text>
						</view>
						<view class="rightN">
							<text>{{ orderDettail.payTime || orderDettail.createTime }}</text>
						</view>
					</view>
					<view class="paytype" v-if="orderDettail.ddRemarks">
						<view class="title">
							<text>订单备注</text>
						</view>
						<view class="orderremarks">
							<text>{{ orderDettail.ddRemarks || '无' }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="servicePro">
				<view class="CDetail">
					<view class="protitle">
						<view class="name">
							<text>服务流程</text>
						</view>
					</view>
					<view class="processlist">
						<view class="process">
							<view class="processDetail">
								<view class="step-item" v-for="(item, index) in stepList" :key="index">
									<view class="stepImg">
										<image :src="item.img" mode="" style="width: 35rpx;height: 35rpx;"></image>
									</view>
									<view v-if="index < stepList.length - 1" class="line"></view>
								</view>
							</view>
							<view class="steps-label">
								<view v-for="(item, index) in stepList" :key="index" class="step-item">
									<view class="step-name">
										<text>{{ item.name }}</text>
									</view>
								</view>
							</view>
						</view>
						<view class="bottomTitle">
							<text>见安多年品质承诺: 对症无效, 一键退款</text>
						</view>
					</view>
				</view>
			</view>
			<view style="height: 160rpx;">

			</view>
		</view>

		<uni-popup ref="BtnPopup" type="bottom" :mask-click="false">
			<view class="PopupBtn">
				<view class="PopupConttent">
					<view class="topContent">
						<view class="left">
						</view>
						<view class="cententtitle">
							<text>合同列表</text>
						</view>
						<view class="rightclose" @click="closePopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="centercontent">
						<view class="htlist" v-for="(item, index) in htlist" :key="index">
							<view class="htitem">
								<view class="leftName">
									<text>{{ item.htName }}</text>
								</view>
								<view class="rightbtn" @click="htSign(item)" v-if="item.sfqm == 0">
									<text class="qsqs">签署</text>
								</view>

								<view class="rightbtn  issign" v-if="item.sfqm == 1" @click="htSign(item)">
									<text class="qsqs">已签</text>
								</view>
							</view>
						</view>
					</view>

					<view class="htBtn">
						<u-button color="linear-gradient(to right,#FF6900  , #FF6900)" text="核销" @click="Verification"
							:loading="hxloading"
							:custom-style="{ borderRadius: '50rpx', height: '80rpx', width: '300rpx', color: '#fff' }"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- <view class="bottomBtn" v-if="isQrcode"> -->
		<!-- <view class="bottomBtn" @click="showContract" :disabled="orderDettail.xxxx == 0 ? true : false"> -->
		<view class="bottomBtn" @click="showContract" :class="orderDettail.xxxx == 0 ? 'isnoOur' : ''">

			<view>{{ orderDettail.orderStatusName }}</view>
		</view>
	</view>
</template>

<script>
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				navBarHeight: '',
				orderDettail: {
					xxxx: 0
				},
				// 门店id
				placeId: '',
				visible: false,
				shopDetail: {},
				stepList: [{
						name: '预约',
						step: 1,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/529a4fc7bfc84214af0342e9966427da.png'
					},
					{
						name: '到店',
						step: 2,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/d4f39f3a30354475a0b70681c8d8af3c.png'
					},
					{
						name: '推拿',
						step: 3,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/45ecce1e4ebf40e1bb89b2e360019a4c.png'
					},
					{
						name: '评价',
						step: 4,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/4e039232fff843a095ca5dc77fb74fba.png'
					}
				],
				// 用来鉴定  当前页面是 从支付订单过来的详情还是 个人中心订单列表过来的1
				isDetail: 0,
				baseImg: '',
				orderId: '',
				isQrcode: false,
				htlist: [],
				hxloading: false,
				adLatitude: uni.getStorageSync('adLatitude'),
				adLongitude: uni.getStorageSync('adLongitude'),
				Issubmit: 0
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		onLoad(opt) {
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			console.log(opt, '路径参数')
			if (opt.orderId) {
				this.orderId = opt.orderId
				this.isQrcode = opt.isQrcode
				this.getOrderDetail(this.orderId)
			}

			uni.$on('isComfirm', (data) => {
				console.log('是否已提交会员内容:', data);
				if (data) {
					this.Issubmit = data.isComfirm
					
				}
			});
		},
		onShow() {
			console.log('onshow-onshow')
			// this.gethxht()
			if (this.orderDettail.xxxx == 1) {
				// xxxx 为0 表示不能核销  为1 表示可以核销
				this.gethxht()
			}
		},
		methods: {
			CopyID(id) {
				console.log(id, '复制的id')
				uni.setClipboardData({
					data: id,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success',
							duration: 1000
						});
					},
					fail: (err) => {
						console.log(err, '报错信息')
						uni.showToast({
							title: '复制失败，请重试',
							icon: 'none',
							duration: 1000
						});
					}
				});
			},
			Verification() {
				console.log(this.htlist, '进行核销')
				// if(this.htlist.length == 0) {
				// 	uni.showToast({
				// 		title: '暂无合同列表',
				// 		icon: "none"
				// 	});
				// 	return
				// }
				const allSfqmAreOne = this.htlist.every(item => item.sfqm == 1);
				if (allSfqmAreOne) {
					if(this.Issubmit == 0) {
						// 此时还未进行 会员内容提交
						uni.navigateTo({
							url: `/pageg/question/index?placeId=${this.shopDetail.placeId}&memberId=${this.orderDettail.memberId}`
						})
						return
					}
					if (this.hxloading) {
						return
					}
					this.hxloading = true
					const params = {
						orderId: this.orderId,
						qmList: this.htlist
					}
					console.log(this.params, '核销订单参数')
					api({
						url: `goods/order/hx/info`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'POST',
						data: params
					}).then(res => {
						if (res.code == 200) {
							console.log(res, '核销接口的信息')
							uni.showToast({
								title: '订单核销成功',
								icon: "none"
							});
							setTimeout(() => {
								this.$refs.BtnPopup.close()
							}, 100)


							uni.navigateBack({
								success: () => {},
								fail: (err) => {
									uni.reLaunch({
										url: "/pages/home/<USER>"
									})
								}
							})

						} else {
							uni.showToast({
								title: res.msg,
								icon: "none"
							});
						}
						this.hxloading = false
					}).catch(err => {
						uni.showToast({
							title: err.msg || '请求超时',
							icon: "none"
						});
					})


				} else {
					uni.showToast({
						title: '还有未签名的合同',
						icon: 'none'
					})
					return
				}


			},
			htSign(item) {
				console.log(item, '签合同')
				uni.showLoading({
					title: '加载中'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: `/pageg/pdf_view/index?qmitem=${JSON.stringify(item)}`
					})
					uni.hideLoading()
				}, 1000)
			},
			showContract() {
				if (this.orderDettail.xxxx == 0) return
				this.gethxht()
				this.$refs.BtnPopup.open()
			},
			closePopup() {
				this.$refs.BtnPopup.close()
			},
			getOrderDetail(orderId) {
				let Rurl = ''; // 提前声明 Rurl
				if (this.isQrcode) {
					// 扫码进入订单的详情
					Rurl = `goods/get/cd/order/list?location=zh&orderStatus=100&page=1&size=1&orderId=${orderId}`;
				} else {
					Rurl = `goods/get/js/order/list?location=zh&orderStatus=100&page=1&size=1&orderId=${orderId}`;
				}

				console.log(Rurl, 'RurlRurlRurlRurl'); // 在此处可以使用 Rurl
				api({
					url: Rurl,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					console.log(res.data.data.list, '订单详情')
					if (res.code == 200) {
						this.orderDettail = res.data.data.list[0]
						if (this.orderDettail.typeOrder == 0) {
							this.placeId = this.orderDettail.adUserName
							this.getShopDetail(this.placeId)
						}
						if (this.orderDettail.codeBase64) {
							this.baseImg = this.orderDettail.codeBase64.replace(/^data:image\/[a-zA-Z]+;base64,/,
								"");
						}

						this.orderId = this.orderDettail.orderId;
					} else {
						uni.showToast({
							title: err.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
				// if (this.isDetail) {
				// 	uni.navigateBack({
				// 		success: () => {},
				// 		fail: (err) => {
				// 			uni.reLaunch({
				// 				url: "/pages/home/<USER>"
				// 			})
				// 		}
				// 	})
				// } else {
				// 	uni.navigateTo({
				// 		url: '/pageu/user-order/user-order'
				// 	});
				// }
			},
			gethxht() {
				const params = {
					orderId: this.orderId
				}
				api({
					url: `goods/order/hx/ht/info`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.htlist = [...res.data.data]
						console.log(this.htlist, '合同列表')
						// this.$refs.BtnPopup.open()
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			getShopDetail(id) {
				const params = {
					adLongitude: this.adLongitude,
					adLatitude: this.adLatitude
				}
				api({
					url: `tx/place/id/info/${id}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					setTimeout(() => {
						this.$data.visible = true
					}, 1500)
					if (res.code == 200) {
						console.log(res.data.data, '店铺详情数据')
						this.shopDetail = res.data.data
						console.log(this.shopDetail, '店铺详情数据')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			openlocation(detail) {
				// 打开门店详情
				uni.openLocation({
					latitude: detail.adLatitude,
					longitude: detail.adLongitude,
					scale: 18,
					success() {
						console.log('打开地图成功')
					},
					fail(error) {
						uni.showToast({
							title: '地图打开失败',
							icon: "none"
						});
					}
				})
			},
			callphone() {
				if (this.shopDetail.contactsInfoList.length == 0) {
					uni.showToast({
						title: '暂无联系电话',
						icon: "none"
					});
					return
				}
				uni.makePhoneCall({
						phoneNumber: this.shopDetail.contactsInfoList[0].phone
					})
					.catch((e) => {
						// console.log (e)
					});
			}
		}

	}
</script>

<style>
	.page {
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.header {
		width: 100%;
		padding-top: 110rpx;
		height: 60rpx;
		background-color: #FFFFFF;
		position: fixed;
		z-index: 999;

		.box-bg {
			height: 40rpx;
			line-height: 40rpx;
			width: 94%;
			margin: 0 auto;
			// background-color: skyblue;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.viewtitle {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.OrderDetail {
		height: calc(100vh - 180rpx);
		overflow-y: auto;
		width: 94%;
		margin: 0 auto;
		margin-top: 190rpx;

		.shopinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.shopde {
				width: 94%;
				margin: 0 auto;
				padding: 25rpx 0;

				.topinfo {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 80rpx;

					// background-color: skyblue;
					.leftV {
						display: flex;
						align-items: center;

						.placeName {
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 32rpx;
							color: #000;
							font-style: normal;
							text-transform: none;
							margin-right: 10rpx;
						}

						.distance {
							display: flex;
							align-items: center;
						}
					}
				}

				.bottominfo {
					margin-top: 25rpx;

					.yysj {
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;

						.busineTime {
							color: #b8b8b8;
						}

						.line {
							width: 4rpx;
							height: 16rpx;
							background: #E2DED4;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
							margin: 0 8rpx;
						}

						.nearest {
							width: 84rpx;
							height: 32rpx;
							line-height: 32rpx;
							border-radius: 24rpx;
							border: 2rpx solid #FF6900;
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 26rpx;
							color: #FF6900;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}

					.shpDetail {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.content {
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 28rpx;
							color: #b8b8b8;
							// background-color: skyblue;
							font-style: normal;
							text-transform: none;
							width: 600rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}
			}
		}

		.orderinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;
			padding: 0 0 25rpx 0;

			.Storeinfo {
				width: 94%;
				margin: 0 auto;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #ededed;

				.storeName {
					display: flex;
					align-items: center;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.orderstatus {
					color: #ccc;
					font-size: 25rpx;
					letter-spacing: 2rpx;
					font-weight: 300;
				}
			}

			.mxList {
				.mxitem {
					width: 94%;
					margin: 0 auto;
					padding: 25rpx 0;
					// border-bottom: 1rpx solid #ededed;

					.mxdetail {
						display: flex;
						justify-content: space-between;

						.right {
							flex: 1;
							margin-left: 15rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;

							.righttop {
								font-weight: 600;
								color: #000;
								display: flex;
								justify-content: space-between;
								margin-bottom: 10rpx;

								.proname {}

								.serviceTime {
									font-family: PingFang SC, PingFang SC;
									font-weight: 600;
									font-size: 28rpx;
									color: #FF6900;
									line-height: 40rpx;
									font-style: normal;
									text-transform: none;
								}
							}

							.rightcenter {
								display: flex;
								justify-content: space-between;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 28rpx;
								color: #999999;
								font-style: normal;
								text-transform: none;
							}

							.skudetail {
								display: flex;
								justify-content: space-between;
								margin-top: 10rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;

								.sku {
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									font-size: 28rpx;
									color: #333333;
									font-style: normal;
									text-transform: none;
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

							}

							.bottomC {
								color: #ccc;
								font-weight: 300;
							}
						}
					}
				}

				.totalPrice {
					margin: 0 auto;
					width: 94%;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					padding-top: 5rpx;

					.statusName {
						margin-right: 8rpx;
					}

					.pricevalue {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}
				}
			}

			.showMore {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #c1c1c1;
			}



			.OrderTime {
				display: flex;
				align-items: center;
				padding: 0 30rpx;

				.title {
					color: #ccc;
					margin-right: 20rpx;
				}
			}

			.totalPrice {
				margin: 0 auto;
				padding-top: 25rpx;
				width: 94%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-bottom: 2rpx;

				.statusName {
					margin-right: 8rpx;
				}

				.pricevalue {
					font-weight: 600;
					font-family: PingFang SC, PingFang SC;
					font-size: 28rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
				}
			}

			.zfbtn {
				margin: 0 auto;
				width: 94%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;

				.qxdd {
					width: 100rpx;
					margin-right: 20rpx;
				}

				.ljzf {
					padding: 10rpx 20rpx;
					color: #FF6900;
					border-radius: 94rpx;
					border: 2rpx solid #FF6900;
				}
			}
		}

		.payinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.infopay {
				padding: 0 0 15rpx 0;
				margin: 0 auto;
				width: 94%;

				.paytitle {
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					// border-bottom: 1rpx solid #ededed;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.ordernum,
				.paytype,
				.paytime {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 60rpx;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #bababa;
						font-style: normal;
						text-transform: none;
					}

					.rightN {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}

					.orderremarks {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						max-width: 500rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						// background-color: skyblue;
					}
				}

				.ordernum {
					.rightN {
						display: flex;
						align-items: center;
					}
				}
			}
		}

		.servicePro {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.CDetail {
				width: 94%;
				margin: 0 auto;

				.protitle {
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.processlist {
					width: 100%;
					margin: 0 auto;

					.process {
						// border-bottom: 1rpx solid #ededed;
						// background-color: skyblue;
						padding: 0 0 20rpx 0;

						.processDetail {
							display: flex;
							justify-content: space-between;
							align-items: center;

							.step-item {
								position: relative;
								text-align: center;
								flex: 1;
								display: flex;
								justify-content: center;

								.stepImg {
									width: 44rpx;
									height: 44rpx;
									border-radius: 50%;
									display: flex;
									justify-content: center;
									align-items: center;
									font-size: 22rpx;
									font-weight: bold;
									margin-bottom: 10rpx;
								}

								.line {
									height: 2px;
									border-bottom: 3rpx dashed #ccc;
									margin-left: 10px;
									width: 60rpx;
									left: 70%;
									top: 45%;
									position: absolute;
								}
							}
						}

						.steps-label {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.step-item {
								width: 25%;
								display: flex;
								justify-content: center;

								.step-name {
									padding: 5rpx 10rpx;
									border-radius: 30rpx;
									color: #fff;
									background: #FF6900;
									font-size: 22rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									color: #FFFFFF;
									font-style: normal;
									text-transform: none;
								}
							}
						}
					}

					.bottomTitle {
						width: 80%;
						margin: 0 auto;
						border-top: 1rpx solid #ededed;
						padding: 20rpx 0;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #999999;
						font-style: normal;
						text-transform: none;
						letter-spacing: 2rpx;
					}
				}
			}
		}
	}

	.bottomBtn {
		position: fixed;
		width: 70%;
		background-color: #FF6900;
		border-radius: 70rpx;
		padding: 30rpx;
		color: #FFFFFF;
		bottom: 50rpx;
		z-index: 9;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		font-weight: 600;
		font-size: 32rpx;
		justify-content: center;
	}

	.isnoOur {
		background-color: #ccc;
	}

	.PopupBtn {
		width: 100%;
		min-height: 450rpx;
		background-color: #EEEEEE;
		margin-bottom: -80rpx;
		z-index: 999 !important;
		position: relative;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
		font-style: normal;
		text-transform: none;

		.PopupConttent {
			width: 92%;
			margin: 0 auto;
			padding-top: 30rpx;

			.topContent {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;

				.cententtitle {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #000000;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}

			.centercontent {
				max-height: 300rpx;
				overflow-y: auto;

				.htlist {
					.htitem {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 120rpx;
						line-height: 120rpx;
						margin-bottom: 20rpx;

						.leftName {
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 32rpx;
							color: #333333;
							font-style: normal;
							text-transform: none;
						}

						.rightbtn {
							height: 55rpx;
							line-height: 55rpx;
							width: 150rpx;
							text-align: center;
							// padding: 5rpx 30rpx;
							border: 1rpx solid #ccc;
							font-weight: 400;
							font-size: 28rpx;
							color: #333333;
							font-style: normal;
							text-transform: none;
							border-radius: 10rpx;

						}

						.issign {
							background-color: #FF6900;
							color: #fff;
						}
					}
				}
			}

			.htBtn {
				// margin-top: 50rpx;
				margin-top: 20rpx;
			}
		}
	}
</style>