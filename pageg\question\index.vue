<template>
	<view class="page">
		<newNavBar :title="title" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
				<u-form-item label="位置" prop="position" borderBottom labelWidth="300rpx">
					<uni-data-select v-model="formData.position" :localdata="locationList"></uni-data-select>
				</u-form-item>
				<u-form-item label="类别" prop="titleList" borderBottom labelWidth="300rpx">
					<uni-data-select :localdata="typeList" multiple="true"
						@change="positionChange"></uni-data-select>
				</u-form-item>
				<u-form-item label="问题描述" prop="details" borderBottom labelWidth="300rpx">
					<u--textarea v-model="formData.details" placeholder="请输入问题描述" count border="none" height="200"
						maxlength="200"></u--textarea>
				</u-form-item>

				<u-form-item label="图片内容" borderBottom labelWidth="300rpx" prop="imageInfoList">
					<view class="imgupload">
						<view class="imgList">
							<view class="imgitem" v-for="item in formData.imageInfoList" :key="item">
								<view class="imgdetail">
									<image :src="item" mode=""
										style="width: 120rpx;height: 120rpx;border-radius: 10rpx;"
										@tap="onTapimage(item)"></image>
									<image @tap="delimg(item)" class="imgicon"
										src="https://file.36sm.cn/xtjyjt/images/common/delicon.png" mode=""
										style="width: 25rpx; height: 25rpx;"></image>
								</view>
							</view>
						</view>
						<view @tap="upload_img"
							style="min-width: 120rpx;height: 120rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;background-color: #f9f9f9;border-radius: 10rpx;">
							<image style="width: 120rpx;height: 120rpx;"
								src="https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png">
							</image>
							<!-- <text style="font-size: 18rpx;margin-top: 5rpx;">{{$t('accountUser.tpbc')}}</text> -->
						</view>
					</view>
				</u-form-item>

			</u--form>
		</view>

		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right, #FF6900 , #FF6900)" @click="submit"
				:loading="loading"></u-button>

		</view>
	</view>
</template>

<script>
	const app = getApp();

	import {
		deletedczdata,
		postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'


	export default {
		data() {
			return {
				type: 0,
				loading: false,
				// 金额单位
				coinName: "",
				// 充值地址二维码
				czqrImg: "",
				formData: {
					// 详情内容
					details: '',
					// 位置
					position: '',
					//问题类型
					titleList: [],
					// 图片内容
					imageInfoList: [
						// "https://img.keaitupian.cn/uploads/2020/12/21/****************.jpg",
						// "https://c-ssl.duitang.com/uploads/blog/202204/17/20220417194312_65b3f.jpg"

					],
					memberId: ''
				},
				placeId: '',
				title: '问题记录',
				parentCodeIdList: [
					'1927269373161652224',
					'1927259656867692544',
					// '1864238902068383744'
				],
				rules: {
					position: {
						required: true,
						message: '请选择位置',
						trigger: ['blur']
					},
					// title: {
					// 	required: true,
					// 	message: '请选择问题类型',
					// 	trigger: ['blur']
					// },
					details: {
						required: true,
						message: '请输入问题描述(反馈建议)',
						trigger: ['blur']
					}
				},
				locationList: [],
				typeList: []
			}
		},
		onLoad(data) {
			if (data.placeId) {
				this.placeId = data.placeId
				this.schemeList()
			}
			if (data.memberId) {
				this.formData.memberId = data.memberId
			}
		},
		onReady() {},
		created() {},
		methods: {
			positionChange(e) {
				console.log(e, '下拉选')
				if (!this.formData.titleList.includes(e)) {
					this.formData.titleList.push(e);
				}
			},
			schemeList() {
				//获取数据
				const dataList = {
					parentCodeIdList: this.parentCodeIdList,
					placeId: this.placeId
				}
				console.log(dataList, '接口请求参数');
				api({
					url: `tx/code/get/sub/list/map1`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: dataList
				}).then(res => {
					console.log(res, '分类列表')
					if (res.code == 200) {
						console.log(res);
						const data = res.data.data
						console.log(data, '分类数据数据')
						this.locationList = data.A1927259656867692544.map(item => {
							return {
								value: item.name,
								text: item.name
							};
						})
						this.typeList = data.A1927269373161652224.map(item => {
							return {
								value: item.name,
								text: item.name
							};
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			delimg(item) {
				const index = this.formData.imageInfoList.indexOf(item);
				if (index !== -1) {
					this.formData.imageInfoList.splice(index, 1);
				}
			},
			upload_img() {
				// 限制最多只能上传4张图片
				if (this.formData.imageInfoList.length > 3) {
					uni.showToast({
						// title: "最多上传4张图片!",
						title: this.$t('message.zdsc'),
						icon: 'none'
					})
					return
				}
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							// url: 'http://************/khrWeb/oss/hteditor/upload',
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								this.formData.imageInfoList.push(Data.data.data.fileUrl)
								console.log(this.formData.imageInfoList, '上传的图片数组')
								if (Data.code == 200) {
									uni.showToast({
										// title: "上传成功",
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			onTapimage(src) {
				uni.previewImage({
					urls: [src],
					current: src
				});

			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if (res) {
						console.log(this.formData, '提交参数')
						if (this.formData.imageInfoList.length == 0) {
							uni.showToast({
								title: this.$t('message.zsxzyz'),
								icon: 'none'
							})
							return
						}
						this.loading = true
						setTimeout(() => {
							this.loading = false
						}, 2000)
						api({
							url: `tx/mbProblemInfo/save`,
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: this.formData
						}).then(res => {
							if (res.code == 200) {
								uni.showToast({
									title: '问题内容提交成功',
									icon: 'none'
								})
								setTimeout(() => {
									uni.navigateBack({
										delta: 1,
										success: () => {
											uni.$emit('isComfirm', {
												isComfirm: 1
											})
										}
									})
								}, 300)
							}
						})

					}
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() => {
					this.formData.amount = temp
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.imgupload {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.imgList {
			display: flex;
			align-items: center;

			.imgitem {
				margin-right: 30rpx;

				.imgdetail {
					position: relative;

					.imgicon {
						position: absolute;
						right: -10rpx;
						top: -10rpx;
					}
				}
			}
		}
	}
</style>