<template>
	<view v-if="visible">
		<view class="top-status-bar" id="topStatusBar" :style="[{height: statusBarHeight + 'px'},statusStyle]">
			<view class="slot-view" :style="[{paddingTop: slotMarginTop},statusStyle]">
				<view class="topTab u-px-20 flex justify-between align-center status-content"
					:style="[{width : slotWidth,height:titleHeight}]">
					<view class="tabLeft">
						<view class="imgView" @click="goBack">
							<image src="https://file.36sm.cn/xtjyjt/images/common/detail/back.png" mode=""></image>
						</view>
					</view>
					<view class="tabRight">
					</view>
				</view>
				<slot></slot>
			</view>
		</view>

		<view class="detail_view" v-if="detailInfo">
			<view class="product_detail">
				<view class="Img_pro" v-if="detailInfo.itemImgs && detailInfo.itemImgs.length > 0">
					<view class="sliderView">
						<swiper class="banner" :indicator-dots="false" :autoplay="true" :interval="3000"
							:duration="1000" :circular="true" indicator-color="transparent"
							indicator-active-color="#ffc47c" @change="onSwiperChange">
							<swiper-item v-for="(item, i) in detailInfo.itemImgs" :key="i">
								<image :src="item.url" style="width: ;" mode="aspectFill"></image>
							</swiper-item>
						</swiper>
						<view class="tabBtn">
							<view :class="showActive ? 'isavtive' : ''" class="imgbtn">
								{{$t('search.tp')}} {{currentIndex}}/{{ detailInfo.itemImgs.length }}
							</view>
							<view :class="!showActive ? 'isavtive' : ''" class="videobtn" @tap="Prodetail"
								v-if="detailInfo.video && detailInfo.video.url">
								<text>视频</text>
							</view>
						</view>
					</view>
				</view>
				<view class="InfoData">
					<view class="InfoDetail">
						<view class="Protitle">
							<text class="title">
								{{detailInfo.title}}
							</text>
						</view>
						<view class="RangeInfo">
							<view class="inforange">
								<view class="CoinName" v-if="detailInfo.priceRangeInfo.priceList">
									<view class="itemcoin" v-for="(item, index) in detailInfo.priceRangeInfo.priceList"
										:key="index">
										<view class="coindetail">
											<view class="top">
												<view class="USD" v-if="index == 0">
													{{item.baseCoinName}}
												</view>
												<view class="price">
													{{item.price_base}}
												</view>

											</view>
											<view class="num" v-if="laddertype == 3"
												style="font-size: 20rpx;color: #cc;">
												{{item.jtMinNub}}-{{item.jtMaxNub}}
												{{detailInfo.priceRangeInfo.sellUnit}}
											</view>
										</view>
										<view :class="laddertype == 3 ? 'blx' : ''"
											v-if="detailInfo.priceRangeInfo.priceList.length > 1 && index !== detailInfo.priceRangeInfo.priceList.length - 1">
											<image src="https://file.36sm.cn/xtjyjt/images/common/blx.png" mode=""
												style="width: 80rpx;height: 60rpx;"></image>
										</view>
									</view>

								</view>
							</view>

						</view>
						<view v-if="resourceType == 1" class="pqView">
							<!-- <text>{{PQmoney}}</text> -->
							<view class="content">
								<view class="title">
									<image
										src="https://file.36sm.cn/tbfile/2025/04/11/be4a4ca600ea40288454cfe427551978.png"
										mode="" style="width: 32rpx;height: 32rpx;margin-right: 20rpx;"></image>
									<text>拼钱奖励</text>
								</view>
								<view class="share">
									<text>分享好友下单</text>
								</view>
								<view class="fanxian">
									<text>返现 {{PQmoney}}元</text>
								</view>
								<view class="fxtext">
									<text>每成功邀请一位好友下单,立即返现 {{PQmoney}}元</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- <view class="sellerInfo" v-if="detailInfo.sellerInfo">
					<view class="sellerleft">
						<view class="imgView">
							<image src="https://file.36sm.cn/jaxinfo/2025/02/12/574d5959ba454fd9840e0a5e8ab549dc.png"
								mode="" style="border-radius: 50%;"></image>
						</view>
						<view class="seller">
							<uni-rate :size="18" v-model="rateValue" @change="onChange" active-color="#FF6900" />
							<view style="margin-top: 10rpx;font-size: 24rpx;letter-spacing: 1rpx;">
								{{detailInfo.sellerInfo.nick}}
							</view>
						</view>
					</view>
					<view class="sellerright" @tap="goShop(detailInfo.sellerInfo)">
						<text>见安足疗</text>
					</view>
				</view> -->

				<!-- <view class="freight_view">
			<view class="freight_top" style="display: flex;justify-content: space-between;color: #777777;">
				<view style="display: flex;align-items: center">
					<image src="https://file.36sm.cn/xtjyjt/images/common/account/n.png" mode="" style="width: 30rpx;height: 24rpx;margin-right: 12rpx;"></image>
					<text style="color: #bbbbbb">
						商家
					</text>
				</view>
				<view class="">
					<text style="color: #bbbbbb; font-size: 26rpx;">
						见安修脚仓库
						</text>
				</view>
				<view style="display: flex;align-items: center; color: #000;font-size: 28rpx;" @tap="showyf">
					运费规则
					<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
						style="height:14rpx;width: 9rpx;margin-left: 10rpx;" />
				</view>
			</view>
			
			<view class="freight_center" >
				<view class="left">
					<text>中国段</text>
				</view>
				<view class="right">
					<text>跨境段</text>
				</view>
			</view>
			
			<view class="freight_bottom">
				<view class="left">
				   <text>下单时支付</text>
				</view>
				<view class="right">
					<text>支付跨境运费(第二次付费)</text>
				</view>
			</view>
		</view> -->

				<view class="main_paraPm" v-if="resourceType == 1">

					<view class="sycontent">
						<view class="toptitle">
							<text>使用规则</text>
						</view>
						<view class="itemb">
							<view class="circle"></view>
							<view class="title">
								<text class="Con">适用门店:</text>
								<text>{{ShopName}}</text>
							</view>
						</view>
						<view class="itemb">
							<view class="circle"></view>
							<view class="title">
								<text class="Con">适用项目:</text>
								<text>{{detailInfo.hdProjectList[0].projectName}}</text>
							</view>
						</view>
						<view class="itemb">
							<view class="circle"></view>
							<view class="title">
								<text class="Con">是否限制新客:</text>
								<text v-if="detailInfo.sfXzXk == 0">否</text>
								<text v-if="detailInfo.sfXzXk == 1">是</text>
							</view>
						</view>

						<!-- <view class="itemb">
							<view class="title">
								<text class="Con">是否限制员工:</text>
								<text v-if="detailInfo.sfXzYg == 0">否</text>
								<text v-if="detailInfo.sfXzYg == 1">是</text>
							</view>
						</view> -->

						<view class="itemb" style="border-bottom: none;">
							<view class="circle"></view>
							<view class="title">
								<text class="Con">奖励条件展示:</text>
								<text v-if="detailInfo.jlTj == 0">购买后才能奖励</text>
								<text v-if="detailInfo.jlTj == 1">无是非购买限制</text>
							</view>
						</view>
					</view>
				</view>
				<view class="main_param">
					<view class="itemb" @click="openSkuPopup" v-if="detailInfo.gzTs">
						<view class="topNav">
							<text>规格提示</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
								style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
						</view>
						<view class="content">

						</view>
					</view>
					<view class="itemb" @click="PopupPurch" v-if="kefuinfo" style="border: none;">
						<view class="topNav">
							<text>购买须知</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
								style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
						</view>
						<view class="content">

						</view>
					</view>

				</view>
				<view class="detail_title">
					- 详情 -
				</view>
				<view class="detail_content" style="">
					<u-parse :content="detailInfo.desc"></u-parse>
				</view>

				<view class="main_param">
					<view class="itemb" style="border: none;" @click="PopupAfter" v-if="detailInfo.shfw">
						<view class="topNav">
							<text>售后服务</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" alt=""
								style="height: 14rpx;width: 9rpx;margin-left: 10rpx;" />
						</view>
						<view class="content">

						</view>
					</view>
				</view>

			</view>

			<view class="" style="height: 120rpx;"></view>

		</view>
		<!-- popup弹窗 -->
		<uni-popup ref="skuPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup" style="height: 1200rpx;">
				<view class="PopupCon">
					<view class="topCon" style="padding-bottom: 10rpx;">
						<view class="topPro">
							<image :src="PopupProUrl" mode="" v-if="PopupProUrl" @tap="onTapimage(PopupProUrl)"></image>
							<view class="centerinfo">
								<view class="CoinName" v-if="detailInfo.priceRangeInfo.priceList && showSku"
									style="padding-top: 20rpx;">
									<view class="coinitem" v-for="(item, index) in detailInfo.priceRangeInfo.priceList"
										:key="index">
										<view class="top">
											<view class="USD">
												{{item.tradeCoinName}}
											</view>
											<view class="price">
												{{item.price_trade}}
											</view>
										</view>
										<view class="bottom">
											<view style="display: flex;align-items: center;">
												<view class="USD" style="font-size: 22rpx;">
													{{item.baseCoinName}}
												</view>
												<view class="price" style="font-size: 22rpx;">
													{{item.price_base}}
												</view>
											</view>
										</view>
									</view>
								</view>
								<view v-if="!showSku && showladder" class="CoinName" style="margin-top: 20rpx;">
									<view class="coinitem">
										<view class="SkuTitle">
											{{selectPro.productTitle}}
										</view>
										<view class="top">
											<view class="USD">
												{{showladder.tradeCoinName}}
											</view>
											<view class="price">
												{{showladder.price_trade}}
											</view>
										</view>
										<view class="bottom">
											<view style="display: flex;align-items: center;">
												<view class="USD" style="font-size: 22rpx;">
													{{showladder.baseCoinName}}
												</view>
												<view class="price" style="font-size: 22rpx;">
													{{showladder.price_base}}
												</view>
											</view>
										</view>
									</view>
								</view>
								<view class="wallet">
									<view style="display: flex;align-items: center;"
										v-if="pursebalance && Object.keys(pursebalance).length > 0">
										<view class="balance">
											{{pursebalance.coinName}}
										</view>
										<view class="value">
											{{pursebalance.balance}}
										</view>
									</view>
									<view class="Integral">
										<view style="display: flex;align-items: center;"
											v-if="purseintegral && Object.keys(purseintegral).length > 0">
											<view class="Integral">
												{{purseintegral.coinName}}
											</view>
											<view class="value">
												{{purseintegral.balance}}
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="topClose" @click="closePopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>

					<scroll-view style="height: 600rpx; overflow-y: scroll; margin: 15rpx 0 30rpx 0;" scroll-y>
						<view class="skuList">
							<view v-for="(item, index) in detailInfo.skuItemList" :key="index">
								<view class="skuitem">
									<view class="skutitle">
										<text>{{item.name}}</text>
										<text style="margin-left: 10rpx;color: #FF806F;"
											v-if="index == 0">{{skuNameSelected}}</text>
									</view>
									<view class="skuchild">
										<view v-for="(itemchild, childindex) in item.itemList" :key="childindex"
											@click="skuclick(itemchild, index)">
											<view class="childitem">
												<view class="numimg" v-if="itemchild.bhImage"
													:class="bhsku[0] == itemchild.bh ? 'SelectedImg' : ''">
													<image :src="itemchild.bhImage" mode=""></image>
													<view class="numbuy" v-if="itemchild.buyNum">
														{{itemchild.buyNum}}
													</view>
												</view>

												<view v-else class="bhname"
													:class="bhsku[index] == itemchild.bh ? 'SelectedName' : ''">
													{{ itemchild.bhName }}
												</view>
												<view class="titlesku">
													{{itemchild.bhName}}
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>

						<view class="flex justify-between align-center">
							<view class="flex align-end">
								<u--text :text="$t('accountUser.sl')" size="28rpx"></u--text>
								<text style="font-size: 24rpx;color: #878c90;margin-left: 20rpx;"
									v-if="stockNum">(库存{{stockNum}})</text>
							</view>
							<u-number-box v-model="buyNum" :min="0" :max="MaxNum" integer
								@change="changeBuyNum"></u-number-box>
						</view>

						<view class="message">
							{{$t('search.hyzazxd')}}
						</view>
					</scroll-view>



					<view class="totalNum" v-if="totalNum > 0">
						<view class="Numtop">
							<view class="lefttotalNum">
								{{$t('search.zj')}}<text style="color: #FF6900;">{{totalNum}}</text>
								<text>{{detailInfo.priceRangeInfo.sellUnit}}</text>
							</view>
							<view class="righttotalprice" v-if="detailInfo.priceRangeInfo.priceList">
								<text style="margin-right: 5rpx;">合计: </text><text
									style="color: #FF6900;">{{detailInfo.priceRangeInfo.priceList[0].baseCoinName}}{{totalPrice_base}}</text>
							</view>
						</view>
						<!-- <view class="Numbottom" v-if="detailInfo.priceRangeInfo.priceList">
								<text>{{detailInfo.priceRangeInfo.priceList[0].baseCoinName}} {{totalPrice_base}}</text>
							</view> -->
					</view>
					<view class="bottombtn" v-if="addOrder">
						<view class="bot-button">
							<u-button color="linear-gradient(to right, #FF6900, #FF6900 )" :text="btnText"
								@tap="submitBuy" :disabled="totalNum < minNum || totalNum == 0" :loading="submitloading"
								:loadingText="$t('search.jzjz')"></u-button>
						</view>
					</view>

					<view class="bottombtn" v-if="!addOrder">
						<view class="bot-button">
							<u-button color="linear-gradient(to right, #FF6900, #FF6900)" :text="btnText" @tap="addCart"
								:disabled="totalNum < minNum || totalNum == 0" :loading="addCartloading"
								:loadingText="$t('search.jzjz')"></u-button>
						</view>
					</view>

				</view>
			</view>
		</uni-popup>

		<uni-popup ref="SpecPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							{{$t('search.ggcs')}}
						</view>
						<view class="topClose" @click="closeSpecPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>

					<view class="props" v-if="detailInfo.props"
						style="margin-top: 30rpx; height: 1200rpx;overflow-y: auto;">
						<view v-for="(item,index) in detailInfo.props" :key="index">
							<view class="propsitem">
								<view class="name">
									{{item.name}}
								</view>
								<view class="value">
									{{item.value}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="SkuPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							<text>规格提示</text>
						</view>
						<view class="topClose" @click="closeSkuPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Ccontent">
						<u-parse :content="detailInfo.gzTs"></u-parse>
					</view>
					<view style="height: 100rpx;">
					</view>
				</view>
			</view>
		</uni-popup>


		<uni-popup ref="PurchasPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							{{$t('search.gmxz')}}
						</view>
						<view class="topClose" @click="closePurchasPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Ccontent">
						<u-parse :content="detailInfo.gmxz"></u-parse>
					</view>
					<view style="height: 100rpx;">
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="AftersalePopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							{{$t('search.shfw')}}
						</view>
						<view class="topClose" @click="closeAftersalePopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Ccontent">
						<u-parse :content="detailInfo.shfw"></u-parse>
					</view>
					<view style="height: 100rpx;">
					</view>
				</view>
			</view>
		</uni-popup>

		<view class="bottom-status-bar" v-if="visible">
			<!-- <view class="lefticon">
			   <view class="item_icon" @tap="goShop(detailInfo.sellerInfo)">
			   	<image src="https://file.36sm.cn/xtjyjt/images/common/detail/dianpu.png" mode="" v-if="!dpActive"></image>
				<image src="https://file.36sm.cn/xtjyjt/images/common/detail/dianpu1.png" mode="" v-if="dpActive"></image>
			   	<text>{{$t('search.dp')}}</text>
			   </view>
			   <view class="item_icon" @tap="showkf">
			   	<image src="https://file.36sm.cn/mttttxxs/2025/01/15/faccc830855340e6bf6687dbd984bb10.png" mode=""></image>
			   	<text>{{$t('search.kf')}}</text>
			   </view>
			   <view class="item_icon" @tap="collectP">
			   	<image src="https://file.36sm.cn/xtjyjt/images/common/detail/shouc.png" mode="" v-if="!isCollect"></image>
				<image src="https://file.36sm.cn/xtjyjt/images/common/detail/isshouc.png" mode="" v-if="isCollect"></image>
			   	<text>{{$t('search.scsc')}}</text>
			   </view>
		   </view> -->
			<view :class="resourceType == 1 ? 'righticon' : 'centericon'">
				<view class="cart" @click="Share" v-if="resourceType == 1">
					<text>{{StatusBtn.fxTitle}}</text>
				</view>
				<view class="buybtn" @click="addOrderlist"
					:style="{ backgroundColor: (resourceType == 1 && StatusBtn.gmStatus == 0) ? '#ccc' : '#FF6900',}">
					<text>{{StatusBtn.gmTitle || '立即购买'}}</text>

				</view>
			</view>
		</view>

		<uni-popup ref="kefuPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							购买须知
						</view>
						<view class="topClose" @click="closekefuPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Ccontent">
						<u-parse :content="kefuinfo"></u-parse>
					</view>
					<view style="height: 100rpx;">
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="yfPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							运费规则
						</view>
						<view class="topClose" @click="closeyfPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Ccontent">
						<u-parse :content="yfinfo"></u-parse>
					</view>
					<view style="height: 100rpx;">
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="QrimgPopup" type="center" :is-mask-click="false">
			<view class="qrPopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view class="title">
							<text></text>
						</view>
						<view class="title">
							<text>二维码详情</text>
						</view>
						<view class="Close" @click="closeinfoPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode=""
								style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="content">
						<image :src="QRimg" style="width: 450rpx;height: 450rpx;" v-if="QRimg"></image>
						<image src="https://file.36sm.cn/mttttxxs/2025/01/15/99a8a9a165424bbc9bedad16d10f2da8.gif"
							style="width: 450rpx;height: 450rpx;" v-else></image>
					</view>
					<view class="btn" v-if="QRimg" style="margin-top: 150rpx;">
						<button style="color:#fff;background-color:#FF6900;font-size: 30rpx;" @click="downimg"
							:loading="downloading">下载</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="MemberPopup" type="center" :is-mask-click="false">
			<view class="popupMember">
				<view class="topView">
					<view class="title">
						<text></text>
					</view>
					<view class="title">
						<text>开卡会员</text>
					</view>
					<view class="Close" @click="closeMemberPopup">
						<image src="https://file.36sm.cn/jaxinfo/2025/02/15/d1360f78eb22474e8590431e65c47724.png"
							mode="" style="width: 40rpx;height: 40rpx;"></image>
					</view>
				</view>
				<!-- <view class="SearchView">
					<view style="width: 420rpx;	">
						<u--input type="text" name="phone" v-model="Memparams.phone" maxlength="11" placeholder="请输入手机号查询"
							:custom-style="{height: '40rpx'}"></u--input>
					</view>
					<u-button text="查询" @click="SearchBtN"
						:custom-style="{  backgroundColor: '#FF6900', borderRadius: '10rpx',color: '#fff', width: '150rpx', height: '60rpx'}"
						style="margin-left: 20rpx;" :loading="SearchLoading"></u-button>
				</view> -->

				<view class="SearchView">
					<view style="width: 420rpx;	">
						<u--input type="text" name="phone" v-model="Memparams.phone" maxlength="11"
							placeholder="请输入手机号查询" :custom-style="{height: '32rpx'}"></u--input>
					</view>
					<u-button text="查询" @click="SearchBtN"
						:custom-style="{  backgroundColor: '#FF6900', borderRadius: '10rpx',color: '#fff', width: '160rpx', height: '60rpx'}"
						style="margin-left: 25rpx;" :loading="SearchLoading"></u-button>
				</view>
				<view class="MemberList">
					<view class="MemberView" v-for="(item,index) in memberList" :key="index"
						@click="SelectMember(item,index)">
						<view class="MemberDetail" :class="yhMemBerId == item.memberId ? 'isActive' : ''">
							<view class="leftImg">
								<image :src="item.logoUrl" mode="" style=""></image>
							</view>
							<view class="userPhone">
								<text>{{item.phone}}</text>
							</view>
							<view class="userName">
								<text>{{item.nickName}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="ConfirmBtn" @click="BtnConfirm">
					<text>确认</text>
				</view>
			</view>
		</uni-popup>
	</view>

	<cc-gifLoading v-else
		gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import api from '@/common/request/index.js'


	import {
		getJyInfo
	} from "@/common/request/api/home.js"

	export default {
		name: "top-status-bar",
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			},
			isrefresh: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				currentIndex: 1,
				btnText: '我要报名',
				pursebalance: {},
				purseintegral: {},
				skuNameSelected: '',
				PopupProUrl: "",
				visible: false,
				ImageIndex: 0,
				slotObserver: null,
				slotHeight: 0,
				productCode: "",
				detailInfo: {},
				JyData: {},
				rateValue: 5,
				totalNum: 0,
				// 购买数量
				buyNum: 0,
				// 商品库存
				stockNum: 0,
				// 当前商品最大购买量
				MaxNum: 0,
				// 当前商品最少购买量
				minNum: 0,
				// 选取的规格对应的商品
				selectPro: {},
				// 所选取商品的规格
				bhsku: [],
				// 提交的商品列表
				infoList: [],
				// 我要报名loading
				submitloading: false,
				// 添加购物车loading
				addCartloading: false,
				showSku: true,

				// 收藏限制loading
				collecting: false,

				// 取消收藏限制loading
				delcollecting: false,
				// 当前商品是否收藏
				isCollect: false,
				// 店铺点击
				dpActive: false,
				resourceType: 0,
				// 是直接下单还是先添加到购物车
				addOrder: true,
				skulist: {},
				laddertype: '',
				// 阶梯价格列表
				ladderList: [],
				// 所选商品对应规格名
				productSkuName: "",
				// 当前选取商品对应的规格
				bhskustr: "",
				// location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				kefuinfo: "",
				yfinfo: "",
				showActive: true,
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				orderId: '',
				orderList: [],
				// 订单支付成功的状态
				isSuccessful: false,
				StatusBtn: {
					fxStatus: '',
					fxTitle: '',
					gmStatus: '',
					gmTitle: ''
				},
				QRimg: '',
				downloading: false,
				userInfo: {},
				yqMemberId: '',
				// 次卡开单用户
				yhMemBerId: '',
				memberList: [],
				Memparams: {
					page: 1,
					size: 50,
					phone: ''
				},
				SearchLoading: false
			}
		},
		components: {},
		computed: {
			...mapState({
				topStatusBarData: state => state.init.topStatusBarData
			}),
			// location() {
			// 	return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			// },
			showladder() {
				// 展示阶梯价格列表
				if (this.laddertype == 3) {
					// 如果购买数量小于第一阶段的最小数量
					if (this.totalNum < this.ladderList[0].jtMinNub) {
						return this.selectPro;
					}
					for (let i = 0; i < this.ladderList.length; i++) {
						const priceInfo = this.ladderList[i];
						// 判断totalNum是否在当前价格区间内
						if (this.totalNum >= priceInfo.jtMinNub && this.totalNum <= priceInfo.jtMaxNub) {
							return priceInfo
						}
					}
					const lastPriceInfo = this.ladderList[this.ladderList.length - 1];
					if (this.totalNum > lastPriceInfo.jtMaxNub) {
						return lastPriceInfo;
					}

				} else {
					return this.selectPro
				}
			},
			// 拼钱奖励
			PQmoney() {
				// 判断 skuPriceList 是否为空
				if (!this.detailInfo.skuPriceList || this.detailInfo.skuPriceList.length === 0) {
					return '0'; // 如果没有数据，返回 0
				}

				// 如果只有一个对象
				if (this.detailInfo.skuPriceList.length === 1) {
					return `${this.detailInfo.skuPriceList[0].jlJe}`;
				}

				// 如果有多个对象，计算最小值和最大值
				const priceBases = this.detailInfo.skuPriceList.map(item => item.jlJe);
				const minPrice = Math.min(...priceBases);
				const maxPrice = Math.max(...priceBases);

				return `${minPrice} - ${maxPrice}`;
			},
			ShopName() {
				if (!this.detailInfo.hdPlaceList || this.detailInfo.hdPlaceList.length === 0) {
					return '-'; // 如果没有数据，返回 0
				}
				return this.detailInfo.hdPlaceList.map(place => place.placeName).join('、');
			},
			// trade价格
			totalPrice_trade() {
				let totalprice = 0;
				if (this.laddertype == 3) {
					// 如果购买数量小于第一阶段的最小数量 
					if (this.totalNum < this.ladderList[0].jtMinNub) {
						// return this.totalNum * this.selectPro.price_trade;
						totalprice = this.totalNum * this.selectPro.price_trade
					}

					// 遍历阶梯价格数组，找到满足条件的价格区间
					for (let i = 0; i < this.ladderList.length; i++) {
						const priceInfo = this.ladderList[i];
						// 判断totalNum是否在当前价格区间内
						if (this.totalNum >= priceInfo.jtMinNub && this.totalNum <= priceInfo.jtMaxNub) {
							// return this.totalNum * priceInfo.price_trade;  // 返回该区间对应的总价格
							totalprice = this.totalNum * priceInfo.price_trade
						}
					}

					// 如果购买数量大于阶梯最大值 
					const lastPriceInfo = this.ladderList[this.ladderList.length - 1];
					if (this.totalNum > lastPriceInfo.jtMaxNub) {
						// return this.totalNum * lastPriceInfo.price_trade;
						totalprice = this.totalNum * lastPriceInfo.price_trade
					}

				} else {
					// return this.totalNum * this.selectPro.price_trade
					totalprice = this.totalNum * this.selectPro.price_trade
				}
				return totalprice.toFixed(2)
			},
			// base价格
			totalPrice_base() {
				let totalprice = 0;
				if (this.laddertype == 3) {
					// 如果购买数量小于第一阶段的最小数量
					if (this.totalNum < this.ladderList[0].jtMinNub) {
						// return this.totalNum * this.selectPro.price_base;
						totalprice = this.totalNum * this.selectPro.price_base;
					}
					// 遍历阶梯价格数组，找到满足条件的价格区间
					for (let i = 0; i < this.ladderList.length; i++) {
						const priceInfo = this.ladderList[i];
						// 判断totalNum是否在当前价格区间内
						if (this.totalNum >= priceInfo.jtMinNub && this.totalNum <= priceInfo.jtMaxNub) {
							// return this.totalNum * priceInfo.price_base;  // 返回该区间对应的总价格

							totalprice = this.totalNum * priceInfo.price_base
						}
					}
					// 如果购买数量大于阶梯最大值 
					const lastPriceInfo = this.ladderList[this.ladderList.length - 1];
					if (this.totalNum > lastPriceInfo.jtMaxNub) {
						// return this.totalNum * lastPriceInfo.price_base;
						totalprice = this.totalNum * lastPriceInfo.price_base
					}
				} else {
					// return this.totalNum * this.selectPro.price_base
					// this.detailInfo.skuPriceList
					totalprice = this.totalNum * this.selectPro.price_base
				}
				return totalprice.toFixed(2)
			},
			statusBarHeight() {

				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight', h)
				return h
			},
			slotMarginTop() {
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				// #endif
				return sw
			},
			titleHeight() {
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			},
			IshaveBalance() {
				// 下单才校验余额和订单总额
				// if(this.addOrder) {
				// 	if(this.pursebalance.balance - (this.buyNum * this.selectPro.price_trade || '0') >= 0) {
				// 		this.btnText = '我要报名'
				// 		return true
				// 	} else {
				// 		this.btnText = '余额不足'
				// 		return false
				// 	}
				// }
			}
		},
		mounted() {
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
		},
		onShareAppMessage() {
			return {
				title: this.detailInfo.title,
				path: `/pageo/product_details/product_details?acid=${this.productCode}&yqMemberId=${this.userInfo.memberId}&resourceType=${this.resourceType}`,
				content: this.detailInfo.title
			}
		},
		onLoad(params) {
			console.log(params, '路由参数')
			// 遍历 params 对象，去掉所有的 'amp;' 前缀
			this.getMember()
			for (let key in params) {
				if (params.hasOwnProperty(key)) {
					// 删除 'amp;' 前缀
					let newKey = key.replace(/^amp;/, ''); // 去除key中的amp;
					let newValue = params[key].replace(/^amp;/, ''); // 去除value中的amp;

					// 更新params对象
					params[newKey] = newValue;
					if (newKey !== key) {
						delete params[key]; // 删除原来的带有amp;前缀的键
					}
				}
			}
			// console.log(params, '路由参数after')
			this.userInfo = this.$store.state.user.userInfo
			console.log(this.userInfo, '当前用户信息')
			if (params.acid) {
				this.productCode = params.acid
			}
			if (params.resourceType !== null && params.resourceType !== undefined) {
				this.resourceType = params.resourceType
				console.log(this.resourceType, 'resourceType')
			}
			if (params.yqMemberId) {
				this.yqMemberId = params.yqMemberId
			} else {
				this.yqMemberId = ''
			}
			this.getProduct();
			this.getKfInfo()
		},
		onshow() {},
		created() {
			this.getJyInfo()
		},
		destroyed() {

		},
		watch: {
			// 监听 buyNum 的变化
			buyNum(newValue, oldValue) {}
		},
		methods: {
			...mapMutations(['setTopStatusBarData']),
			// 调整商品的数量
			changeBuyNum(e) {
				console.log(e.value, '当前数量')
				if (e.value > this.stockNum) {
					uni.showToast({
						title: `限购${this.stockNum}`,
						icon: "none"
					});

					return
				}
				console.log(this.bhskustr, '当前商品规格集合字段')

				this.detailInfo.skuPriceList.find(info => info.properties == this.bhskustr).buyNum = e.value
				this.totalNum = this.detailInfo.skuPriceList.reduce((sum, item) => sum + item.buyNum, 0);
				const targetBh = this.bhskustr.split(';')[0];
				// 计算skuPriceList中所有匹配该bh的buyNum的总和
				const totalBuyNum = this.detailInfo.skuPriceList.filter(item => item.properties.split(';')[0] === targetBh)
					.reduce((sum, item) => sum + item.buyNum, 0);
				// 更新skuItemList中的对应bh值的buyNum
				const targetItem = this.detailInfo.skuItemList[0].itemList.find(item => item.bh === targetBh);
				if (targetItem) {
					targetItem.buyNum = totalBuyNum;
				}
				console.log(this.detailInfo.skuItemList[0].itemList, 'detailInfo.skuItemList[0]');
				// const params = {
				// 	properties: this.selectPro.properties,
				// 	productCode: this.selectPro.productCode,
				// 	productMainImg: this.PopupProUrl,
				// 	productSkuCode: this.selectPro.skuId,
				// 	productSkuId: this.selectPro.skuId,
				// 	productSkuImg: this.PopupProUrl,
				// 	productSkuName: this.selectPro.propertiesName,
				// 	productTitle: this.selectPro.productTitle,
				// 	sellProductPriceBase: this.selectPro.price_base,
				// 	sellProductPriceTrade: this.selectPro.price_trade,
				// 	sellQuantity: e.value,
				// 	shopCode: this.detailInfo.sellerInfo.sid,
				// 	shopName: this.detailInfo.sellerInfo.title
				// }

				const params = {
					productCode: this.selectPro.productCode,
					productMainImg: this.PopupProUrl,
					sellProductPriceTrade: this.selectPro.price_trade,
					productSkuCode: this.selectPro.skuId,
					productSkuId: this.selectPro.skuId,
					productSkuName: this.selectPro.propertiesName,
					productSkuImg: this.PopupProUrl,
					productTitle: this.selectPro.productTitle,
					sellQuantity: e.value,
				}
				console.log(params, '加入的商品')
				if (this.infoList.length > 0) {
					console.log(params, '添加的商品');
					let found = false;
					this.infoList.forEach((item, index) => {
						if (item.properties == params.properties) {
							// 更新 sellQuantity
							if (params.sellQuantity === 0) {
								// 如果 sellQuantity 为 0，删除当前商品
								this.infoList.splice(index, 1);
							} else {
								item.sellQuantity = params.sellQuantity;
							}
							found = true;
						}
					});

					// 如果没有找到相同的商品，则push新商品
					if (!found) {
						this.infoList.push(params);
					}
				} else {
					// 如果 infoList 为空，直接添加商品
					this.infoList.push(params);
				}

				console.log(this.infoList, '当前商品列表列表')

			},
			SearchBtN() {
				if (this.SearchLoading) {
					return
				}
				this.SearchLoading = true
				this.getMember()
			},
			closeMemberPopup() {
				this.$refs.MemberPopup.close()
			},
			SelectMember(item, index) {
				console.log(item, index, '选择的用户')
				this.yhMemBerId = item.memberId
				console.log(this.yhMemBerId, '当前选择的用户ID')
			},
			BtnConfirm() {
				if (this.yhMemBerId == '') {
					uni.showToast({
						title: '您还未选择开卡的用户',
						icon: "none"
					});
					return
				}
				this.$refs.MemberPopup.close()
			},
			getMember() {
				api({
					url: `js/kd/member/get/list?page=${this.Memparams.page}&size=${this.Memparams.size}&phone=${this.Memparams.phone}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data.list
						this.memberList = [...data]
						console.log(this.memberList, '会员列表数据')
						setTimeout(() => {
							this.$refs.MemberPopup.open()
						}, 500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.SearchLoading = false
					}, 400)
				}).catch(err => {
					uni.showToast({
						title: err.msg || '请求超时',
						icon: "none"
					});
				});
			},
			// 
			locationPlace() {
				console.log(this.detailInfo.adLatitude, '维度')
				console.log(this.detailInfo.adLongitude, '经度')
				console.log(this.detailInfo.adProvince, '省')
				console.log(this.detailInfo.adCity, '市')
				console.log(this.detailInfo.adDistrict, '区')
				console.log(this.detailInfo.adDetails, '详情地址')

				const Latitude = this.detailInfo.adLatitude
				const Longitude = this.detailInfo.adLongitude
				uni.openLocation({
					latitude: Latitude,
					longitude: Longitude,
					address: this.detailInfo.adDetails,
					// 缩放比例  默认为18
					scale: 18,
					success() {
						console.log('打开地图成功')
					},
					fail(error) {
						uni.showToast({
							title: '地图打开失败',
							icon: "none"
						});
					}
				})
			},
			// 收藏
			subcollect() {
				if (this.collecting) {
					return
				}
				this.collecting = true
				uni.showLoading({
					title: this.$t('message.scz')
				});

				setTimeout(() => {
					this.collecting = false
				}, 6000)
				this.isCollect = true
				// 收藏商品
				const infoList = {
					productCode: this.detailInfo.productCode,
					productMainImg: this.detailInfo.itemImgs[0].url,
					productSkuCode: this.detailInfo.skuPriceList[0].skuId,
					productSkuId: this.detailInfo.skuPriceList[0].skuId,
					productSkuImg: this.detailInfo.itemImgs[0].url,
					productSkuName: this.detailInfo.skuItemList[0].itemList[0].bhName,
					productTitle: this.detailInfo.title,
					sellProductPriceBase: this.detailInfo.skuPriceList[0].price_base,
					sellProductPriceTrade: this.detailInfo.skuPriceList[0].price_trade,
					sellQuantity: 1,
					shopCode: this.detailInfo.sellerInfo.sid,
					shopName: this.detailInfo.sellerInfo.title
				}
				const params = {
					infoList: infoList
				}
				console.log(params, '提交参数')
				api({
					url: `collect/add/goods/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code === 200) {
						setTimeout(function() {
							uni.hideLoading();
						}, 2000);
						console.log(res, '接口参数')
						setTimeout(() => {
							uni.showToast({
								// title: '收藏成功',
								title: this.$t('message.sccgl'),
								icon: "none"
							});
							this.isCollect = true
							this.collecting = false
						}, 1500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			delcollect() {
				// 取消收藏
				if (this.delcollecting) {
					return
				}
				this.delcollecting = true
				uni.showLoading({
					// title: '取消中',
					title: this.$t('accountUser.jzz')
				});
				const params = {
					productCode: this.detailInfo.productCode
				}
				console.log(params, '提交参数')

				setTimeout(() => {
					this.delcollecting = false
				}, 6000)

				this.isCollect = false
				api({
					url: `collect/qx/goods/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code === 200) {
						setTimeout(function() {
							uni.hideLoading();
						}, 2000);
						setTimeout(() => {
							uni.showToast({
								// title: '取消成功',
								title: this.$t('message.qxcg'),
								icon: "none"
							});
							this.isCollect = false
							this.delcollecting = false
						}, 1500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			collectP() {
				// 避免收藏重复点击
				if (this.collecting) {
					return
				}

				// 避免取消收藏重复点击
				if (this.delcollecting) {
					return
				}
				// detailInfo.collectStatus == 0  收藏
				if (!this.isCollect) {
					// 收藏
					this.subcollect()
				} else {
					// 取消收藏
					console.log('取消收藏')
					this.delcollect()
				}
				// detailInfo.collectStatus != 0  取消收藏

			},
			// 获取用户收藏列表
			getCollection() {
				api({
					url: `collect/get/car/list?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
				}).then(res => {
					if (res.code === 200) {
						const data = res.data.data
						// console.log(data, '用户收藏数据列表')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || this.$t('message.qqsb'),
						icon: "none"
					});
				});
			},
			// 获取用户收藏列表
			getStatusBtn(productCode) {
				api({
					url: `goods/get/bt/status/info/${productCode}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					if (res.code === 200) {
						const data = res.data.data
						console.log(data, '拼钱的详情的按钮状态')
						this.StatusBtn = data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || this.$t('message.qqsb'),
						icon: "none"
					});
				});
			},
			moveHandle() {
				// 禁止popup弹窗 遮罩层下的页面滚动
			},
			setGlobalData(data) {
				this.setTopStatusBarData(data)
			},
			// 规格参数 弹窗
			SpecificationPopup() {
				this.$refs.SpecPopup.open()
			},
			PopupPurch() {
				this.$refs.PurchasPopup.open()
			},
			PopupAfter() {
				this.$refs.AftersalePopup.open()
			},
			closeSpecPopup() {
				this.$refs.SpecPopup.close()
			},
			closeSkuPopup() {
				this.$refs.SkuPopup.close()
			},
			openSkuPopup() {
				this.$refs.SkuPopup.open()
			},
			closePurchasPopup() {
				this.$refs.PurchasPopup.close()
			},
			closeAftersalePopup() {
				this.$refs.AftersalePopup.close()
			},
			closekefuPopup() {
				this.$refs.kefuPopup.close()
			},
			closeyfPopup() {
				this.$refs.yfPopup.close()
			},
			closeinfoPopup() {
				this.$refs.QrimgPopup.close()
				this.downloading = false
				uni.hideLoading();
			},
			downimg() {
				if (this.downloading) {
					return
				}
				this.downloading = true;
				this.downSaveImage(this.QRimg)
			},
			downSaveImage(imgurl) {
				let that = this;
				uni.showLoading({
					title: '图片下载中',
				});
				uni.getSetting({
					success(res) {
						if (res.authSetting['scope.writePhotosAlbum']) {
							// 已授权，直接保存图片
							uni.downloadFile({
								url: imgurl,
								success: (res) => {
									if (res.statusCode == 200) {
										uni.saveImageToPhotosAlbum({
											filePath: res.tempFilePath,
											success: function() {
												uni.showToast({
													title: '分享图片已保存，请到相册中查看',
													duration: 2000,
												})
												uni.hideLoading();
												that.downloading = false
											},
											fail: function() {
												uni.showToast({
													title: '保存失败，请稍后重试',
													icon: 'none',
												})
												uni.hideLoading();
												that.downloading = false
											},
										})
									}
								},
							})
						} else if (res.authSetting['scope.writePhotosAlbum'] === false) {
							// 用户已拒绝授权，提示用户授权
							uni.showModal({
								title: '提示',
								content: '您未授权保存图片到相册，是否前往设置页面进行授权？',
								success: function(res) {
									if (res.confirm) {
										uni.openSetting({
											success: function(res) {
												if (res.authSetting[
														'scope.writePhotosAlbum']) {
													uni.downloadFile({
														url: imgurl,
														success: (res) => {
															if (res
																.statusCode ==
																200) {
																uni.saveImageToPhotosAlbum({
																	filePath: res
																		.tempFilePath,
																	success: function() {
																		uni.showToast({
																			title: '分享图片已保存，请到相册中查看',
																			duration: 2000,
																		})
																		uni
																			.hideLoading();
																		that.downloading =
																			false
																	},
																	fail: function() {
																		uni.showToast({
																			title: '保存失败，请稍后重试',
																			icon: 'none',
																		})
																		uni
																			.hideLoading();
																		that.downloading =
																			false
																	},
																})
															}
														},
													})
												}
											},
										})
									} else if (res.cancel) {
										uni.showToast({
											title: '您取消了授权',
											icon: 'none',
											duration: 2000,
										})
										uni.hideLoading();
										that.downloading = false
									}
								},
							})
						} else {
							// 用户第一次调用，调用授权接口
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success() {
									uni.downloadFile({
										url: imgurl,
										success: (res) => {
											if (res.statusCode === 200) {
												uni.saveImageToPhotosAlbum({
													filePath: res.tempFilePath,
													success: function() {
														uni.showToast({
															title: '分享图片已保存，请到相册中查看',
															duration: 2000,
														})
														uni.hideLoading();
														that.downloading = false
													},
													fail: function() {
														uni.showToast({
															title: '保存失败，请稍后重试',
															icon: 'none',
														})
														uni.hideLoading();
														that.downloading = false
													},
												})
											}
										},
									})
								},
								fail() {
									uni.showToast({
										title: '授权失败，请稍后重试',
										icon: 'none',
										duration: 2000,
									})
									uni.hideLoading();
									that.downloading = false
								},
							})
						}
					},
				})
			},
			clickIcon(item) {
				if (item.title == this.$t('search.dp')) {
					console.log(this.detailInfo.sellerInfo, 'detailInfo.sellerInfo')
					uni.navigateTo({
						url: `/pageu/shopdetail/shopdetail?shopCode=${this.detailInfo.sellerInfo.user_num_id}&shopName=${this.detailInfo.sellerInfo.nick}`
					})
				}

				if (item.title == this.$t('search.kf')) {
					console.log('客服弹窗')
					this.getKfInfo()
					this.$refs.kefuPopup.open()
				}
			},

			showkf() {
				// this.getKfInfo()
				this.$refs.kefuPopup.open()
			},
			showyf() {
				this.$refs.yfPopup.open()
			},
			changeActive() {
				this.showActive = !this.showActive
			},
			Prodetail() {
				if (!this.detailInfo.video.url) {
					uni.showToast({
						title: '暂无视频',
						icon: "none"
					});
					return
				}
				uni.navigateTo({
					// url: `/pages/branded-space/videodetail?videourl=${JSON.stringify(item.tradeCoinName)}`
					url: `/pages/branded-space/videodetail?videourl=${this.detailInfo.video.url}`
				})
			},
			// 获取客服信息
			getKfInfo() {
				api({
					url: `bs/kf/fb/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					if (res.code === 200) {
						const data = res.data.data
						// console.log(data, '客服信息提示')
						this.kefuinfo = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || this.$t('message.qqsb'),
						icon: "none"
					});
				});
			},
			// 运费规则
			getyfInfo() {
				api({
					url: `bs/yw/gz/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					if (res.code === 200) {
						const data = res.data.data
						// console.log(data, '运费信息提示')
						this.yfinfo = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || this.$t('message.qqsb'),
						icon: "none"
					});
				});
			},
			Share() {
				this.$refs.QrimgPopup.open()
				// const loadParams = {
				// 	acid: this.productCode,
				// 	yqMemberId: this.userInfo.memberId,
				// 	resourceType: this.resourceType
				// }
				const params = {
					path: `/pageo/product_details/product_details?acid=${this.productCode}&yqMemberId=${this.userInfo.memberId}&resourceType=${this.resourceType}`,
				}
				api({
					url: `dt/details/path/info/code`,
					header: {
						"jzpmarket-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, '二维码资源');
						this.QRimg = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			// 提交订单
			addOrderlist() {
				if (this.resourceType == 1 && this.StatusBtn.gmStatus == 0) {
					// 当前是拼钱商品 并且新用户不能购买
					return
				}

				if (!this.yhMemBerId) {
					// 技师 需要先选择对应的客户 才能开单 
					uni.showToast({
						title: '开卡客户还未选择',
						icon: "none"
					});
					this.$refs.MemberPopup.open()
					return
				}
				this.addOrder = true
				if (this.resourceType == 0) {
					// this.btnText = '我要购买'
				}
				if (this.resourceType == 1) {
					// this.btnText = '我要拼钱'
				}
				// <text>{{StatusBtn.gmTitle || '立即购买'}}</text>
				this.btnText = this.StatusBtn.gmTitle || '立即购买'
				// 获取当前用户的钱包信息
				if (!this.visible) {
					return
				}

				// 阶梯价格
				if (this.bhsku.length > 0) {
					this.showSku = false
				} else {
					this.showSku = true
				}
				this.PopupProUrl = this.detailInfo.itemImgs[0].url
				// skuclick(itemchild, index)
				console.log(this.detailInfo.skuItemList, 'detailInfo.skuItemList')
				this.skuclick(this.detailInfo.skuItemList[0].itemList[0], 0)
				this.changeBuyNum({
					name: '',
					value: 1
				})
				this.buyNum = 1
				this.$refs.skuPopup.open()


				console.log(this.infoList, 'infoList商品列表')
			},
			//  购物车
			addCartlist() {
				this.addOrder = false
				this.btnText = '加入购物车'
				if (!this.visible) {
					return
				}
				// 阶梯价格
				if (this.bhsku.length > 0) {
					this.showSku = false
				} else {
					this.showSku = true
				}
				this.PopupProUrl = this.detailInfo.itemImgs[0].url
				// 添加购物车
				this.$refs.skuPopup.open()
				console.log(this.infoList, 'infoList商品列表')
			},
			submitBuy() {
				if (this.infoList.length == 0) {
					uni.showToast({
						title: '还未选择商品',
						icon: "none"
					});
					return
				}
				if (this.totalNum == 0 || this.totalNum < this.minNum) {
					return
				}

				if (this.submitloading) {
					return
				}

				this.infoList = this.infoList.map(item => {
					// 计算商品总价并保留两位小数
					const productPriceTotalTrade = (item.sellQuantity * item.sellProductPriceTrade).toFixed(2);
					return {
						...item,
						productPriceTotalTrade: productPriceTotalTrade // 转换回数字类型
					};
				});

				this.submitloading = true
				setTimeout(() => {
					this.submitloading = false
				}, 4000)
				const params = {
					orderMxList: this.infoList,
					productPriceTotalTrade: (this.infoList.reduce((total, item) => {
						return total + (item.sellProductPriceTrade * item.sellQuantity);
					}, 0)).toFixed(2),
					orderRemarks: '默认的订单备注',
					yqMemberId: this.yqMemberId,
					memberId: this.yhMemBerId
				}
				console.log(params, '添加购物车接口参数')
				api({
					url: `goods/save/order?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.getStausOList()
						const data = res.data.data
						console.log(data, '生成的订单和支付信息')
						this.orderId = data.orderId
						
						
						//  payAmountTrade 为 0 则不需要拉起支付  直接跳转到支付成功的订单详情页面
						if (data.payAmountTrade == 0) {
							setTimeout(() => {
								uni.navigateTo({
									url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(this.orderList))}`
								})
							}, 200)
							return
						}
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: data.timeStamp,
							nonceStr: data.nonceStr,
							package: data.packageValue,
							signType: data.signType,
							paySign: data.paySign,
							success: (res) => {
								api({
									url: "/car/weixin/order/status/query",
									header: {
										"jarepair-platform": uni.getStorageSync("token")
									},
									method: 'POST',
									data: {
										outTradeNo: this.orderId
									}
								}).then((res) => {
									console.log(res, '支付成功的信息')
									if (res.code == 200) {
										// 支付成功  进入到订单页面
										this.isSuccessful = true
										uni.showToast({
											title: '支付成功',
											icon: "none"
										});
									} else {
										this.isSuccessful = false
										uni.showToast({
											title: res.msg,
											icon: "none"
										});
									}
									setTimeout(() => {
										uni.navigateTo({
											// url: `/pageg/oder_detail/index?orderDetail=${JSON.stringify(this.orderList)}`
											url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(this.orderList))}`

										})
									}, 500)
								})
							},
							fail: (res) => {
								console.log(res)
								if (res.errMsg.includes("fail cancel")) {
									uni.showToast({
										title: '支付取消',
										icon: "none"
									});
								} else {
									uni.showToast({
										title: res.errMsg,
										icon: "none"
									});
								}
								setTimeout(() => {
									uni.navigateTo({
										url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(this.orderList))}`
										// url: `/pageg/oder_detail/index?orderDetail=${JSON.stringify(this.orderList)}`
									})
								}, 300)
							}
						})

					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.submitloading = false
					}, 1500)
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
					setTimeout(() => {
						this.submitloading = false
					}, 1500)
				});
			},
			// 获取最新的订单列表
			getStausOList() {
				api({
					url: `goods/get/order/list?location=zh&orderStatus=100&page=1&size=1`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						this.orderList = res.data.data.list[0]
						console.log(this.orderList, '当前最新的订单详情')
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			addCart() {
				// infoList
				if (this.infoList.length == 0) {
					uni.showToast({
						// title: '还未选择商品',
						title: this.$t('message.hwxzsp'),
						icon: "none"
					});
					return
				}

				if (this.totalNum == 0 || this.totalNum < this.minNum) {
					return
				}
				if (this.addCartloading) {
					return
				}
				this.addCartloading = true
				// setTimeout(() => {
				// 	this.addCartloading = false
				// }, 1500)
				const params = {
					orderMxList: this.infoList,
					productPriceTotalTrade: (this.infoList.reduce((total, item) => {
						return total + (item.sellProductPriceTrade * item.sellQuantity);
					}, 0)).toFixed(2),
					orderRemarks: '默认的订单备注',
				}
				console.log(params, '添加购物车接口参数')
				api({
					url: `goods/save/order?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code === 200) {
						console.log(res, '接口参数')
						this.$refs.skuPopup.close()
						uni.showToast({
							// title: '成功加入购物车',
							title: this.$t('message.cgjrgwc'),
							icon: "none"
						});
						this.addCartloading = false
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			closePopup() {
				this.$refs.skuPopup.close()
				this.showSku = true
			},
			onTapimage(src) {
				console.log(src, 'src')
				uni.previewImage({
					urls: [src],
					current: src
				});

			},
			onSwiperChange(e) {
				// console.log(e, 'e')
				this.currentIndex = e.detail.current + 1
			},
			concatenateArray(arr) {
				if (arr.length === 1) {
					return arr[0]; // 如果数组只有一个元素，直接返回该元素
				}
				return arr.join(';'); // 如果有多个元素，用 ; 连接数组元素
			},
			skuclick(item, index) {
				this.skulist = item
				// this.skulist.buyNum 
				this.detailInfo.skuItemList[0].itemList.forEach(item => {
					item.buyNum = 0
				})
				this.detailInfo.skuPriceList.forEach(item => {
					item.buyNum = 0
				})
				this.totalNum = 0
				this.infoList = []
				console.log(this.skulist, index, '选取的规格')
				if (item.bhImage) {
					this.PopupProUrl = item.bhImage
				}
				// 选取的第一个规格item中对应的规格名
				if (index == 0) {
					this.productSkuName = item.bhName
				}
				this.bhsku[index] = item.bh
				if (!this.bhsku[0]) {
					// 没选择完规格
					return
				}

				const skulen = this.detailInfo.skuItemList.length
				if (skulen == this.bhsku.length) {
					console.log(this.bhsku, '当前选取的规格', '选取了所有商品规格')
					this.bhskustr = this.concatenateArray(this.bhsku)
					console.log(this.bhskustr, '选取完后的规格集合字段')
					this.selectPro = this.detailInfo.skuPriceList.find(info => info.properties == this.bhskustr) || {}
					if (this.selectPro) {
						this.showSku = false
						console.log(this.selectPro, '有相应商品')
						this.stockNum = this.selectPro.quantity
						this.MaxNum = this.detailInfo.priceRangeInfo.maxNum
						this.minNum = this.detailInfo.priceRangeInfo.minNum
						// 当前商品的购买数量
						this.buyNum = this.selectPro.buyNum
					} else {
						console.log('无相应商品')
					}
				}
			},
			onChange(e) {
				console.log(e, '评分调整')
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			goTOrecharge() {
				// pageo/my_top_ups/my_top_ups
				uni.navigateTo({
					url: '/pageo/my_top_ups/my_top_ups'
				})
			},
			// 获取商品详情
			getProduct() {
				api({
					url: `goods/get/${this.productCode}?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code === 200) {
						this.$data.visible = true
						this.setData({
							detailInfo: res.data.data
						});

						// 给商品规格第一个属性对象 添加购买数量
						if (this.detailInfo.skuItemList.length > 0) {
							this.detailInfo.skuItemList[0].itemList.forEach(item => {
								item.buyNum = 0
							})
						}

						// 每个商品的购买数量赋值为0
						this.detailInfo.skuPriceList.forEach(item => {
							item.buyNum = 0
						})
						console.log(this.detailInfo, '商品详情')
						if (this.resourceType == 1) {
							this.getStatusBtn(this.detailInfo.productCode)
						}
						// 阶梯价类型
						this.laddertype = res.data.data.priceRangeInfo.type
						console.log(this.laddertype, '阶梯type')

						if (this.laddertype == 3) {
							this.ladderList = [...res.data.data.priceRangeInfo.priceList]
							console.log(this.ladderList, '阶梯价格列表')
						}


						if (this.detailInfo.collectStatus == 1) {
							//收藏
							this.isCollect = true
						} else {
							// 取消收藏
							this.isCollect = false
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			// 获取当前用户钱包信息
			previewImage(e) {
				// this.detailInfo.itemImgs
				console.log(e, '图片预览')
				uni.previewImage({
					current: 1,
					urls: e.target.dataset.src
				})
			},
			goShop(item) {
				return
			},
			async getJyInfo() {
				const res = await getJyInfo()
				if (res.code == 200) {
					this.JyData = res.data.data
					console.log(this.JyData, '交易信息数据')
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		z-index: 999;
		background-color: transparent;

		.slot-view {
			.topTab {
				padding: 0 50rpx;

				.tabLeft {
					.imgView {
						width: 60rpx;
						height: 60rpx;

						image {
							width: 60rpx;
							height: 60rpx;
						}
					}
				}

				.tabRight {
					display: flex;

					.imgView {
						width: 60rpx;
						height: 60rpx;
						margin-left: 30rpx;

						image {
							width: 60rpx;
							height: 60rpx;
						}
					}
				}
			}
		}
	}

	.bottom-status-bar {
		width: 100%;
		position: fixed;
		bottom: 0rpx;
		z-index: 9;
		height: 120rpx;
		line-height: 120rpx;
		background-color: #fff;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		// justify-content: flex-end;
		justify-content: center;

		.lefticon {
			display: flex;
			justify-content: space-between;

			.item_icon {
				width: 65rpx;
				// height: 100%;
				margin-top: 50rpx;
				margin-right: 40rpx;
				display: flex;
				align-items: center;
				flex-direction: column;

				image {
					width: 45rpx;
					height: 40rpx;
				}

				text {
					margin-top: -30rpx;
					color: #333333;
				}
			}
		}

		.righticon {
			width: 700rpx;
			height: 90rpx;
			line-height: 90rpx;
			display: flex;
			align-items: center;

			.cart {
				width: 120rpx;
				text-align: center;
				letter-spacing: 2rpx;
				margin-right: 20rpx;
			}

			.buybtn {
				background-color: #FF6900;
				width: 500rpx;
				height: 90rpx;
				line-height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 200;
				font-size: 38rpx;
				color: #FFFFFF;
				border-radius: 20rpx;
				font-style: normal;
				text-transform: none;
			}
		}

		.centericon {
			width: 700rpx;
			height: 90rpx;
			line-height: 90rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.buybtn {
				background-color: #FF6900;
				width: 500rpx;
				height: 90rpx;
				line-height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 200;
				font-size: 38rpx;
				color: #FFFFFF;
				border-radius: 20rpx;
				font-style: normal;
				text-transform: none;
			}
		}
	}


	.detail_view {
		.product_detail {
			width: 100%;

			.Img_pro {
				.pro_img {
					width: 100%;

					.imageView {
						position: relative;
						width: 100%;

						image {
							z-index: -999;
							width: 100%;
							height: 620rpx;
						}

						.tabBtn {
							position: absolute;
							bottom: 10%;
							left: 50%;
							transform: translate(-50%, 0);
							background: #fff;
							color: #000;
							padding: 0 20rpx;
							border-radius: 20rpx;
							z-index: 1;

							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 22rpx;
							color: #333333;
							line-height: 40rpx;
							font-style: normal;
							text-transform: none;
						}
					}
				}

				.sliderView {
					width: 100%;
					margin: 0 auto;
					height: 620rpx;
					position: relative;

					.banner {
						width: 100% !important;
						height: 620rpx !important;

						image {
							width: 100% !important;
							height: 620rpx !important;
						}
					}

					.radio {
						width: 100% !important;
						height: 620rpx !important;
						position: relative;
						/* 确保容器不被视频遮挡 */
						z-index: -1;

						video {
							width: 100% !important;
							height: 100% !important;
							position: absolute;
							/* 避免布局破坏 */
							z-index: 1;
							/* 降低视频层级 */
						}
					}

					.tabBtn {
						position: absolute;
						bottom: 5%;
						left: 50%;
						transform: translate(-50%, 0);
						background: rgba(228, 228, 228, 0.5);
						color: #000;
						border-radius: 20rpx;
						z-index: 1;

						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 22rpx;
						color: #333333;
						height: 40rpx;
						line-height: 40rpx;
						font-style: normal;
						text-transform: none;
						display: flex;
						align-items: center;

						.imgbtn {
							border-radius: 20rpx;
							padding: 0 10rpx;
							min-width: 140rpx;
							text-align: center;
						}

						.videobtn {
							border-radius: 20rpx;
							padding: 0 10rpx;
							min-width: 100rpx;
							text-align: center;
						}

						.isavtive {
							background-color: #fff;
						}
					}

				}
			}

			.SRMarket {
				width: 750rpx;
				height: 100rpx;
				background: linear-gradient(90deg, #FF6900 0%, #3c9cff 39%, #3c9cff 73%, #FF4D06 98%);
				border-radius: 0rpx 0rpx 0rpx 0rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx;

				.left {
					display: flex;
					justify-content: space-between;
					align-items: center;
					color: #FFFFFF;

					.title {
						font-size: 38rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						color: #FFFFFF;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}

					.icon {
						font-weight: 100;
						margin: 0 20rpx;
					}

				}

				.right {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 280rpx;
					height: 60rpx;
					line-height: 60rpx;
					background-color: #fff;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					border-radius: 40rpx;

					.rate {
						font-size: 25rpx;
						letter-spacing: 1rpx;
						margin-left: 10rpx;
					}
				}
			}

			.InfoData {
				width: 100%;
				margin: 0 auto;
				// background: linear-gradient( 90deg, #2A3A88FF 0%, #3c9cff 39%, #3c9cff 73%, #FF4D06 98%);
				background: #f87a3f;
				border-top-right-radius: 40rpx;
				border-top-left-radius: 40rpx;

				.InfoDetail {
					background-color: #fff;
					// padding: 20rpx 0;
				}

				.RangeInfo {
					width: 100%;

					.inforange {
						width: 95%;
						margin: 0 auto;
						// background-color: skyblue;
						// padding: 30rpx 30rpx 15rpx 30rpx;
						// padding: 10rpx 30rpx 15rpx 30rpx;

						.CoinName {
							display: flex;

							.itemcoin {
								display: flex;
								align-items: center;
								justify-content: space-between;

								// width: 180rpx;
								// margin: 0 12rpx 0 20rpx;
								.coindetail {
									flex: 1;
									display: flex;
									flex-direction: column;
									align-items: center;
									justify-content: center;

									.top {
										display: flex;
										align-items: center;
										color: #FF6900;

										.USD {
											font-size: 26rpx;
										}

										.price {
											font-weight: 600;
											margin-left: 5rpx;
											font-size: 36rpx;
										}
									}

									.bottom {
										display: flex;
										align-items: center;
										color: #777777;
										font-size: 26rpx;

										.price {
											margin-left: 5rpx;
										}
									}

									.num {
										min-width: 150rpx;
										display: flex;
										align-items: center;
										justify-content: center;
									}
								}

								.blx {
									margin-left: -20rpx;
									display: flex;
									align-items: center;
									justify-content: center;
									// background-color: skyblue;
								}
							}

							// .itemcoin::after {
							//   content: '';
							//     position: absolute;
							//     top: 68%; /* 垂直居中 */
							//     left: 70%;
							//     width: 80rpx; /* 设置波浪线的宽度 */
							//     height: 50px; /* 设置波浪线的高度 */
							//     background: url('https://file.36sm.cn/xtjyjt/images/common/blx.png');
							//     background-size: contain; /* 保持图片的原始比例 */
							//     transform: translateY(-50%); /* 使波浪线在垂直方向上完全居中 */
							// }
						}



					}

				}

				.pqView {
					// background-color: #FF6900;
					background: linear-gradient(to right, #ff8903, #FF6900);
					color: #fff;
					border-bottom-right-radius: 20rpx;
					border-bottom-left-radius: 20rpx;
					width: 98%;
					margin: 0 auto;

					.content {
						padding: 20rpx;

						.title {
							display: flex;
							align-items: center;
							margin-bottom: 20rpx;

							text {
								font-size: 32rpx;
							}
						}

						.share {}

						.fanxian {
							font-size: 42rpx;
							font-weight: 600;
							margin: 10rpx 0 20rpx 0;
						}

						.fxtext {
							color: #f3f3f3;
						}
					}


				}

				.Protitle {
					// background-color: #fff;
					display: flex;
					padding: 10rpx 30rpx 0 20rpx;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 30rpx;
						color: #333333;
						text-align: left;
						font-style: normal;
						text-transform: none;

						.caresel {
							margin-right: 10rpx;
							background: linear-gradient(225deg, #DF9141 0%, #f0a865 100%);
							border-radius: 4rpx;
							width: 130rpx;
							height: 28rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 300;
							padding: 0 5rpx;
							letter-spacing: 1rpx;
							font-size: 22rpx;
							color: #FFFFFF;
							line-height: 28rpx;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}
				}

				.monthsale {
					// background-color: skyblue;
					display: flex;
					margin: 15rpx 0 5rpx 30rpx;

					.titleCon {
						display: flex;
						align-content: center;
						justify-content: center;
						// width: 210rpx;
						padding: 5rpx 20rpx;
						// height: 48rpx;
						// line-height: 48rpx;
						background: #FCF5E6;
						margin-right: 10rpx;

						image {
							width: 28rpx;
							height: 28rpx;
						}

						.salevalue {
							margin-left: 5rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 24rpx;
							color: #FAAB0C;
							font-style: normal;
							text-transform: none;
						}
					}

				}
			}


			.sellerInfo {
				width: 96%;
				margin: 0 auto;
				background-color: #fff;
				margin-top: 15rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-radius: 8rpx;
				height: 130rpx;
				padding: 0 20rpx;

				.sellerleft {
					display: flex;

					.imgView {
						image {
							width: 72rpx;
							height: 72rpx;
						}

						margin-right: 20rpx;
					}

					.seller {}
				}

				.sellerright {
					min-width: 152rpx;
					padding: 0 20rpx;
					height: 54rpx;
					line-height: 54rpx;
					color: #fff;
					font-size: 24rpx;
					font-weight: 300;
					text-align: center;
					background: #FF6900;
					border-radius: 82rpx 82rpx 82rpx 82rpx;
					letter-spacing: 2rpx;
				}
			}

			.freight_view {
				width: 96%;
				margin: 0 auto;
				margin-top: 15rpx;
				background-color: #fff;
				border-radius: 8rpx;
				padding: 15rpx 25rpx;

				.freight_top {
					text {
						font-size: 28rpx;
					}
				}

				.freight_center {
					display: flex;
					justify-content: space-around;
					margin: 25rpx 0;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 26rpx;
					color: #FFFFFF;

					.left {
						padding: 0 35rpx;
						height: 36rpx;
						line-height: 36rpx;
						background: #D9D9D9;
						text-align: center;
						border-radius: 104rpx;

						text {
							font-size: 22rpx;
							letter-spacing: 1rpx;
						}
					}

					.right {
						padding: 0 35rpx;
						height: 36rpx;
						line-height: 36rpx;
						background: #FF6900;
						text-align: center;
						border-radius: 104rpx;

						text {
							font-size: 22rpx;
							letter-spacing: 1rpx;
						}
					}
				}

				.freight_bottom {
					display: flex;
					justify-content: space-between;
					margin: 30rpx 0 10rpx 0;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20rpx;
					color: #333333;
					text-align: left;
					font-style: normal;
					text-transform: none;
					letter-spacing: 2rpx;

					.left {
						width: 260rpx;
						height: 50rpx;
						line-height: 50rpx;
						background: #EEEEEE;
						border-radius: 12rpx;
						text-align: center;
					}

					.right {
						width: 394rpx;
						height: 50rpx;
						line-height: 50rpx;
						text-align: center;
						background: #EEEEEE;
						border-radius: 12rpx;
					}
				}
			}

			.main_param {
				width: 96%;
				padding: 10rpx 20rpx;
				background-color: white;
				border-radius: 14rpx;
				margin-top: 15rpx;
				margin-left: 15rpx;

				.itemb {
					width: 100%;
					height: 80rpx;
					line-height: 80rpx;
					margin-bottom: 10rpx;
					border-bottom: 1rpx solid #E2E2E2;

					.topNav {
						display: flex;
						justify-content: space-between;
						align-items: center;
					}

					text {
						font-size: 25rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						color: #3D3D3D;
						letter-spacing: 1px;
					}
				}
			}

			.main_paraPm {
				// width: 96%;
				// height: 390rpx;
				// padding: 10rpx 20rpx;
				// background-color: white;
				// border-radius: 14rpx;
				// margin-top: 15rpx;
				// margin-left: 15rpx;

				width: 96%;
				margin: 30rpx auto;
				// height: 475rpx;
				// padding: 20rpx;
				background-color: white;
				border-radius: 14rpx;

				.sycontent {
					padding: 20rpx;

					.toptitle {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 32rpx;
						color: #000;
						font-style: normal;
						text-transform: none;
						margin-bottom: 20rpx;

					}

					.itemb {
						display: flex;
						align-items: center;
						/* 使内容上下居中 */
						margin-bottom: 6px;

						.circle {
							width: 5px;
							height: 5px;
							/* 圆形的高度 */
							background-color: #FF6900;
							border-radius: 50%;
							/* 使它成为圆形 */
							margin-right: 8px;
							/* 圆形与文本之间的间距 */
						}

						.title {
							font-weight: 500;
							color: #333;
						}
					}

					// .itemb {
					// 	display: flex;
					// 	justify-content: space-between;
					// 	align-items: center;
					// 	width: 100%;
					// 	height: 90rpx;
					// 	margin-bottom: 2rpx;
					// 	border-bottom: 1rpx solid #E2E2E2;

					// 	.title {
					// 		display: flex;
					// 		align-items: center;

					// 		image {
					// 			margin-right: 10rpx;
					// 		}

					// 		.Con {
					// 			margin-right: 15rpx;
					// 		}
					// 	}

					// 	text {
					// 		font-size: 25rpx;
					// 		font-family: Source Han Sans, Source Han Sans;
					// 		font-weight: 500;
					// 		color: #3D3D3D;
					// 		letter-spacing: 1px;
					// 		max-width: 600rpx;
					// 		// background-color: skyblue;
					// 		overflow: hidden;
					// 		white-space: nowrap;
					// 		text-overflow: ellipsis;
					// 	}
					// }
				}

			}

			.recommendstore {
				width: 96%;
				padding: 10rpx 20rpx;
				background-color: white;
				border-radius: 14rpx;
				margin: 0 auto;
				margin-top: 20rpx;

				.title_top {
					display: flex;
					justify-content: space-between;
					align-items: center;
					width: 100%;
					height: 80rpx;
					margin-bottom: 2rpx;
				}

				.recommendList {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;

					.recommenditem {
						width: 220rpx;
						margin: 0 0 15rpx 0;
						padding-bottom: 12rpx;

						image {
							width: 220rpx;
							height: 220rpx;
							border-radius: 8rpx 8rpx 0rpx 0rpx;
						}

						.titlename {
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 20rpx;
							color: #333333;
							line-height: 30rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.trade {
							display: flex;
							width: 124rpx;
							height: 40rpx;
							font-family: DIN, DIN;
							font-weight: 400;
							font-size: 20rpx;
							color: #FF6900;
							line-height: 40rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;

							.price {
								margin-left: 5rpx;
								font-size: 26rpx;
								font-weight: 600;
							}
						}

						.base {
							display: flex;
							width: 176rpx;
							height: 20rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 18rpx;
							color: #777777;
							line-height: 20rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;

							.price {
								margin-left: 5rpx;
							}
						}
					}
				}
			}

			.detail_title {
				width: 300rpx;
				height: 40rpx;
				margin: 20rpx auto;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 30rpx;
				color: #9e9e9e;
				text-align: center;
				line-height: 40rpx;
				font-style: normal;
				text-transform: none;
			}

			.detail_content {
				width: 96%;
				background-color: white;
				border-radius: 5rpx;
				margin: 0 auto;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;
				/* Prevent overflow */
				position: relative;
				/* To contain children inside */

				u-parse {
					display: block !important;
					text-align: center !important;
					width: 100%;
					max-height: 100%;
					/* Prevent overflow within u-parse */
					overflow: hidden;
					/* Hide overflow within u-parse */
					text-overflow: ellipsis;
					/* Add ellipsis for text truncation */
					line-height: 1.5;
					/* Optional, adjust for better text spacing */
				}

				.u-parse p,
				.u-parse img {
					text-align: center !important;
					width: 100% !important;
					max-width: 100%;
					/* Ensure images don't stretch out */
				}

				/* If you want to limit the content to a specific number of lines, use line-clamp */
				.u-parse {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					/* Limit to 3 lines (adjust as needed) */
					overflow: hidden;
				}

				u-parse p,
				u-parse img {
					text-align: center !important;
					width: 100% !important;
				}
			}

		}
	}

	.InfoPopup {
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		min-height: 500rpx;
		max-height: 1500rpx;

		.PopupCon {
			width: 92%;
			margin: 0 auto;
			padding-top: 30rpx;

			.topCon {
				display: flex;
				justify-content: space-between;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #000000;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}

				.topPro {
					display: flex;

					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 10rpx;
					}

					.centerinfo {
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.CoinName {
							display: flex;
							align-items: center;

							.coinitem {
								margin: 0 10rpx 0 15rpx;
								height: 40rpx;
								line-height: 40rpx;

								.SkuTitle {
									width: 450rpx;
									overflow: hidden;
									/* 隐藏超出的文本 */
									text-overflow: ellipsis;
									/* 显示省略号 */
									display: -webkit-box;
									/* 必须使用 flexbox 布局 */
									-webkit-box-orient: vertical;
									/* 设置垂直排列 */
									-webkit-line-clamp: 2;
									/* 限制显示两行 */
									white-space: normal;
									/* 允许文本换行 */
								}

								.top {
									display: flex;
									align-items: center;
									color: #FF6900;

									.USD {
										font-size: 22rpx;
									}

									.price {
										font-weight: 600;
										margin-left: 5rpx;
										font-size: 26rpx;
									}
								}
							}
						}

						.wallet {
							display: flex;
							align-items: center;
							color: #777777;
							font-size: 26rpx;
							margin-left: 15rpx;

							.price {
								margin-left: 5rpx;
							}

							.Integral {
								margin-left: 30rpx;
								display: flex;
								align-items: center;

								.value {
									color: #FAAB0C;
								}
							}

							.value {
								margin-left: 20rpx;
								color: #FAAB0C;
							}
						}
					}
				}

				.topClose {
					image {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}


			.Ccontent {
				// background-color: skyblue;
				overflow-y: auto;
				max-height: 1200rpx;
			}

			.props {
				margin-top: 40rpx;
				width: 90%;
				overflow-y: auto;
				// height: 1200rpx;
				margin: 0 auto;

				.propsitem {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					min-height: 78rpx;
					border-bottom: 1rpx solid #EEEEEE;

					.name {
						width: 50%;
					}

					.value {
						width: 50%;
						flex-wrap: wrap;
					}
				}
			}

			.skuList {
				width: 100%;
				margin-top: 30rpx;

				.skuitem {
					margin-bottom: 40rpx;

					.skutitle {
						font-size: 28rpx;
						font-weight: 500;
					}

					.skuchild {
						display: flex;
						justify-content: flex-start;
						// justify-content: space-around;
						align-items: center;
						margin-top: 10rpx;
						flex-wrap: wrap;

						.childitem {
							margin: 0 12rpx 10rpx 0;

							.numimg {
								height: 138rpx;
								position: relative;

								image {
									width: 130rpx;
									height: 130rpx;
									// margin: 0 auto;
								}

								.numbuy {
									position: absolute;
									right: -10rpx;
									top: -12rpx;
									min-width: 30rpx;
									height: 30rpx;
									background-color: red;
									font-size: 22rpx;
									color: #fff;
									border-radius: 50%;
									display: flex;
									align-items: center;
									justify-content: center;
								}
							}

							.titlesku {
								margin-top: 5rpx;
								width: 125rpx;
								overflow: hidden;
								/* 隐藏超出的文本 */
								text-overflow: ellipsis;
								/* 显示省略号 */
								display: -webkit-box;
								/* 必须使用 flexbox 布局 */
								-webkit-box-orient: vertical;
								/* 设置垂直排列 */
								-webkit-line-clamp: 2;
								/* 限制显示两行 */
								white-space: normal;
								/* 允许文本换行 */
							}

							.SelectedImg {
								border: 4rpx solid #FE0035;
							}

							.bhname {
								height: 60rpx;
								padding: 10rpx;
								background-color: #f6f7ff;
								color: #181919;
								margin-right: 20rpx;
								border-radius: 5rpx;
							}

							.SelectedName {
								background: rgba(255, 128, 111, 0.1);
								border-radius: 16rpx 16rpx 16rpx 16rpx;
								border: 1rpx solid #FF806F;
								color: #ff473a;
							}
						}
					}
				}
			}

			.message {
				color: #a4aaaf;
				margin: 60rpx 0;
			}

			.totalNum {
				.Numtop {
					display: flex;
					justify-content: space-between;
				}

				.Numbottom {
					display: flex;
					flex-direction: row-reverse;
				}
			}

			.bottombtn {
				.tishi {
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #FFF8EE;
					border-radius: 16rpx;
					height: 56rpx;
					margin-bottom: 10rpx;

					text {
						margin-left: 10rpx;
						width: 168rpx;
						font-family: Source Han Sans SC, Source Han Sans SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #FAAB0C;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
				}

				.bot-button {
					border-radius: 15rpx !important;

				}
			}

			.nobalance {
				.tishi {
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #FFF8EE;
					border-radius: 16rpx;
					height: 56rpx;

					image {
						width: 40rpx;
						height: 40rpx;
					}

					text {
						margin-left: 10rpx;
						width: 168rpx;
						font-family: Source Han Sans SC, Source Han Sans SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #FAAB0C;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
				}

				.btn {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.btnbal {
						width: 48%;
					}

					.btncz {
						width: 48%;
					}
				}
			}

		}
	}


	.qrPopup {
		width: 600rpx;
		min-height: 700rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 99999;
		margin-bottom: -60rpx;
		padding-bottom: 20rpx;

		.PopupCon {
			width: 92%;
			margin: 0 auto;

			.topCon {
				display: flex;
				justify-content: space-between;
				height: 100rpx;
				align-items: center;

				.Close {
					color: #c1c1c1;
					font-size: 28rpx;
				}

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #333333;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}

			.content {
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}


	}

	.popupMember {
		width: 650rpx;
		height: 960rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 20rpx 0;

		.topView {
			width: 94%;
			margin: 0 auto;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 50rpx;
			font-family: PingFang SC, PingFang SC;
			font-size: 30rpx;
			color: #333333;
			font-weight: 600;
			font-style: normal;
			text-transform: none;
			letter-spacing: 1rpx;
		}

		.SearchView {
			height: 60rpx;
			width: 94%;
			margin: 20rpx auto;
			display: flex;
			align-items: center;
		}

		.MemberList {
			width: 94%;
			margin: 0 auto;
			margin-bottom: 30rpx;
			height: 660rpx;
			overflow-y: auto;

			.MemberView {
				padding: 0 10rpx;

				.MemberDetail {
					display: flex;
					align-items: center;
					margin: 10rpx 0;
					justify-content: space-between;
					height: 100%;
				
					.leftImg {
						width: 90rpx;
						height: 92rpx;
						border-radius: 50%;
					}
				
					image {
						border-radius: 50%;
						width: 100%;
						height: 100%;
					}
				}

				.isActive {
					border: 1rpx solid #ff6900;
				}
			}
		}

		.ConfirmBtn {
			width: 240rpx;
			height: 70rpx;
			margin: 0 auto;
			line-height: 70rpx;
			background: #FF6900;
			border-radius: 94rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
	}
</style>