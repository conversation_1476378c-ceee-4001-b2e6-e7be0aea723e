<template>
	<view>
		<newNavBar :title="title" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="left" :model="formData" :rules="rules" ref="uForm">
				<u-form-item :label="$t('accountUser.xm')" prop="adUserName" borderBottom labelWidth="200rpx">
					<u-input maxlength="8" v-model="formData.adUserName" border="none" :placeholder="$t('accountUser.xm')"  @blur="validateField('adUserName')" />
				</u-form-item>
				<u-form-item :label="$t('accountUser.sjh')" prop="adContactInfo" borderBottom labelWidth="200rpx">
					<u-input v-model="formData.adContactInfo" type="number" border="none" :placeholder="$t('accountUser.sjh')" clearable />
				</u-form-item>
				<u-form-item :label="$t('accountUser.dq')" prop="adAddress5" borderBottom labelWidth="150rpx">
					<view class="flex justify-between align-center" style="width: 100%;" @tap="()=>{pickerShow = true}">
						<view class="u-m-r-20">
							<u--text v-if="formData.adAddress5" :text="formData.adAddress5" :lines="1"></u--text>
							<u--text v-else :text="$t('accountUser.dq')" color="#c0c4cc"></u--text>
						</view>
						<u-icon name="arrow-right" color="#c0c4cc"></u-icon>
					</view>
				</u-form-item>
				<u-form-item :label="$t('accountUser.dz')" prop="adAddressDetails" borderBottom labelWidth="150rpx">
					<u--textarea v-model="formData.adAddressDetails" :placeholder="$t('accountUser.dz')" autoHeight border="none" clearable
						disableDefaultPadding></u--textarea>
				</u-form-item>
				<u-form-item label="服务站" prop="adAddress5" borderBottom labelWidth="150rpx">
					<u--textarea v-model="formData.adAddress5" placeholder="请输入服务站" autoHeight border="none" clearable
						disableDefaultPadding></u--textarea>
				</u-form-item>
				<u-form-item :label="$t('accountUser.mr')" labelWidth="150rpx">
					<u-switch v-model="formData.sortNumber" :activeValue="0" :inactiveValue="1" activeColor="#000000"
						size="18"></u-switch>
				</u-form-item>
			</u--form>
		</view>
		<view class="u-p-20">
			<u-button :text="$t('accountUser.bc')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
		<u-picker :show="pickerShow" ref="uPicker" :columns="columns" keyName="name" :itemHeight="50" closeOnClickOverlay
			@cancel="()=>{pickerShow = false}" @close="()=>{pickerShow = false}" @change="changeEvent" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
	import {
		getAreaList
	} from '@/common/request/api/map.js'
	import {
		postSaveAddress,
		postUpdateAddress
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				title: '新增地址',
				pickerShow: false,
				columns: [
					[],
					[],
					[]
				],
				type:0,
				loading:false,
				formData: {
					// 收货地址省
					adAddress1: "",
					
					// 收货地址市
					adAddress2: "",
					
					// 收货地址区/县
					adAddress3: "",
					
					// 收货地址国家
					adAddress4: "中国",
					
					
					// 收货地址详情
					adAddressDetails: "",
					
					// 服务站
					adAddress5: "",
					
					// 收货地址联系人区号(中国给86)
					adContactArea: "86",
					
					// 收货地址联系人号码
					adContactInfo: "",
					
					// 收货地址联系人名称
					adUserName: "",
					
					// 收货地址ID
					addressId: "",
					
					// 是否设置为默认地址 0-默认 1-不是默认
					sortNumber: 1, 
					
				},
				rules: {
					adUserName: {
						required: true,
						// message: '请输入收货人姓名',
						message: this.$t('message.qscshrxm'),
						trigger: ['blur']
					},
					adContactInfo: [{
							required: true,
							// message: '请输入手机号',
							message: this.$t('message.qsrsjh'),
							trigger: ['blur'],
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.mobile(value);
							},
							// message: '手机号码不正确',
							message: this.$t('message.sjhbzq'),
							trigger: ['blur'],
						}
					],
					adAddress5: {
						required: true,
						// message: '请选择所在地区',
						message: this.$t('message.qxzdq'),
						trigger: ['blur']
					},
					adAddressDetails: {
						required: true,
						// message: '请输入详细地址',
						message: this.$t('message.qsrxxdz'),
						trigger: ['blur']
					},
					adAddress5: {
						required: true,
						message: '请输入服务站',
						// message: this.$t('message.qsrxxdz'),
						trigger: ['blur']
					}
				}
			}
		},
		onLoad(opt) {
			console.log(opt, 'opt')
			if(opt.type == 1) {
				console.log('编辑地址')
				this.title = this.$t('accountUser.bjdz')
				this.type = 1
				const Address = JSON.parse(opt.address)
				this.formData = {...Address}
				
				console.log(this.formData, '地址详情信息')
			} else {
				console.log('新增地址')
				this.title = this.$t('accountUser.xzdz')
				this.type = 0
			}
			// if (JSON.parse(opt.address)) {
			// 	const Address = JSON.parse(opt.address)
			// 	console.log(Address, '地址详情')
			// 	this.title = '编辑地址'
			// 	this.type = 1
			// 	this.formData = {...Address}
			// } else {
			// 	this.title = '新增地址'
			// 	this.type = 0
			// }
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules)
		},
		created() {
			this.getAreaList()
		},
		methods: {
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			async getAreaList(id=0,i=0) {
				let params = {
					parentId:id
				}
				let res1 = await getAreaList(params)
				if(res1.code == 200){
					console.log(res1, '地区')
					if(i==0){
						this.$set(this.columns,0,res1.data.data)
						params = {parentId:this.columns[0][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,1,res2.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res3 = await getAreaList(params)
						this.$set(this.columns,2,res3.data.data)
					}else if(i==1){
						this.$set(this.columns,1,res1.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,2,res2.data.data)
					}else if(i==2){
						this.$set(this.columns,2,res1.data.data)
					}
				}
			},
			changeEvent(e) {
				const {
					columnIndex,
					value,
					values,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex < 2) {
					this.getAreaList(value[columnIndex].areaId,columnIndex+1)
				}
			},
			confirm(e) {
				console.log('confirm', e)
				const {value} = e
				this.formData.adAddress5 = value.filter(a=>a != undefined).map(a=>a.name).join("")
				const province = value[0]?.name
				const city = value[1]?.name
				const district = value[2]?.name
				this.formData.adAddress1 = value[0]?.name
				this.formData.adAddress2 = value[1]?.name
				this.formData.adAddress3 = value[2]?.name
				
				console.log(province, city, district, '省 市 区')
				console.log(this.formData, 'formData')
				this.pickerShow = false
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res){
						console.log(this.formData, '提交参数')
						// return
						if(this.type == 1) {
							this.UpdateAddress()
						} else {
							this.SaveAddress()
						}
						
						// let fn = this.type ? postUpdateAddress : postSaveAddress
						// this.loading = true
						// fn(this.formData).then(res=>{
						// 	this.loading = false
						// 	if(res.code==200){
						// 		uni.showToast({
						// 			title:this.title + '成功',
						// 			icon:'none'
						// 		})
						// 		setTimeout(() => {
						// 			this.$back()
						// 		}, 500)
						// 	}
						// })
					}
				})
			},
			// 更新地址
			UpdateAddress() {
				// 更新
				this.loading = true
				setTimeout(() => {
					this.loading = false
				},2000)
				api({
					url: `mb/update/address`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '编辑成功',
							title: this.$t('message.bjcg'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
			},
			// 保存地址
			SaveAddress() {
				this.loading = true
				setTimeout(() => {
					this.loading = false
				},2000)
				api({
					url: `mb/save/address`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '新增成功',
							title: this.$t('message.xzsc'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
				
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>