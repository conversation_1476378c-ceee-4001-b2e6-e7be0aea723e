<template>
	<view class="page">
		<view class="header">
			<u-tabs :list="typeList" @click="sectionChange" :current="current" keyName="statusName"
			  :itemStyle="{height: '44px',width:(22)+'%',boxSizing: 'border-box'}" lineColor="#2A3A88 " lineWidth="50" padding="10rpx" :activeStyle="{ color: '#2A3A88',
			 fontWeight: 'bold', transform: 'scale(1.05)'}"></u-tabs>
		</view>
		<view class="Contenlist" v-if="visible">
			<scroll-view class="scroll-container" scroll-y="true"  @scrolltolower="onscrollBottom">
				<view class="payList">
						<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
								:use-page-scroll="true" :show-refresher-when-reload="true" :auto-show-back-top-top="true" :auto="false">
							<refresh-loading slot="refresher"></refresh-loading>
							<view class="PaymentList" v-if="ListPayment.length > 0">
								<view class="Payitem" v-for="(item, index) in ListPayment" :key="index">
									<view class="paydetail">
										<view class="top">
											<view class="toptitle">
												<text>{{item.companyName}}({{item.estimateNub}})</text>
											</view>
											<view class="beOverdue" v-if="item.hkStatus == 0 && item.beOverdueDay > 0">
												<text>逾期{{item.beOverdueDay}}天</text>
											</view>
										</view>
										<view class="content">
											<view class="topC">
												<view class="totalnum">
													<text style="margin-right: 15rpx;">项目总金额: </text>
													<text>{{item.totalAmount}}</text>
												</view>
												<view class="">
													<text style="margin-right: 15rpx;">已回款金额: </text>
													<text>{{item.totalAmount}}</text>
												</view>
											</view>
											<view class="sychk" style="margin-bottom: 15rpx;">
												<image src="https://file.36sm.cn/xtjyjt/images/common/naoz.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
												<text style="margin-right: 15rpx;">上一次回款日期: </text>
												<text>{{item.lastReturnTime.slice(0, 10)}}</text>
											</view>
											<view class="Ccenter" v-if="item.hkStatus == 0">
												<view class="left">
													<view class="xycck" style="margin-bottom: 15rpx;">
														<image src="https://file.36sm.cn/xtjyjt/images/common/rli.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
														<text style="margin-right: 15rpx;">下一次回款日期: </text>
														<text>{{item.nextReturnTime.slice(0, 10)}}</text>
													</view>
													<view class="xycck" style="margin-bottom: 15rpx;">
														<image src="https://file.36sm.cn/xtjyjt/images/common/rli.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
														<text style="margin-right: 15rpx;">上一次催款日期: </text>
														<text>{{item.lastDemandTime.slice(0, 10)}}</text>
													</view>
												</view>
												<view class="right" style="width: 150rpx; border: 1rpx solid #2A3A88FF; border-radius: 8rpx;height: 58rpx;">
													<u-button text="催款" @tap="Dunning(item)" size="small"></u-button>
												</view>
											</view>
											<view class="" style="margin-bottom: 15rpx;">
												<text style="margin-right: 15rpx;">{{item.contactName}}(男) </text>
												<text>{{item.contactPhone}}</text>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="noOrder" v-if="ListPayment.length == 0">
								<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
								<text class="notitle">暂无数据</text>
							</view>
							<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
							<component-bottom-line propMsg="数据加载完全" :propStatus="bottom_line_status" v-if="!loadingStatus && ListPayment.length > 0"></component-bottom-line>
						</z-paging>
					</view>
			</scroll-view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
			
			
		<uni-popup ref="contactPopup" type="center" :is-mask-click="true">
			<view class="DunningPopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title" style="color: #000;">
							<text>催款</text>
						</view>
						<view class="topClose" @click="closeConPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Concontent">
						<view class="name" style="display: flex; align-items: center;margin-bottom: 20rpx;">
							<view style="display: flex;align-items: center;width: 180rpx;">
								<text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 联系人名称</text>
							</view>
							<u--input placeholder="联系人名称" v-model="FormData.contactName" maxlength="20"></u--input>
						</view>
						<view class="name" style="display: flex; align-items: center;margin-bottom: 20rpx;">
							<view style="display: flex;align-items: center;width: 180rpx;">
								<text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 手机号</text>
							</view>
							<u--input placeholder="手机号" v-model="FormData.contactPhone" maxlength="13" type="number"></u--input>
						</view>
					</view>
					
					<view class="confirmbtn">
						<u-button color="linear-gradient(to right, #2A3A88FF , #2A3A88FF)" text="保存" @tap="changeconfirm" :loading="Cloading"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>	
	</view>
</template>

<script>
import api from '@/common/request/index.js'
import componentBottomLine from '../../components/bottom-line/bottom-line';
import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	export default {
		data() {
			return {
				typeList: [],
				current: 0,
				Params: {
					page: 1,
					size: 10,
					hkStatus: ''
				},
				visible: false,
				ListPayment: [],
				// 下拉触底加载效果
				loadingStatus: true,
				bottom_line_status: false,
				FormData: {
					estimateId: '',
					contactPhone: '',
					contactName: ''
				},
				Cloading: false
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
		},
		onLoad() {
			this.getTypeList()
		},
		methods: {
			closeConPopup() {
				this.$refs.contactPopup.close()
				this.FormData.estimateId = ''
				this.FormData.contactName = ''
				this.FormData.contactPhone = ''
			},
			moveHandle() {},
			changeconfirm() {
				if(this.FormData.contactName == '') {
					uni.showToast({
						title: '联系人名称不能为空',
						icon: "none"
					})
					return
				}
				if(this.FormData.contactPhone == '') {
					uni.showToast({
						title: '手机号不能为空',
						icon: "none"
					})
					return
				}
				if(this.Cloading) {
					return
				}
				console.log(this.FormData, '提交参数')
				this.Cloading = true
				api({
					url: `hkd/gyd/bz3`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.FormData
				}).then(res=>{
					if(res.code == 200){
						uni.showToast({
							title: '催款成功',
							icon:'none'
						})
						setTimeout(() => {
							this.Cloading = false
							this.closeConPopup()
							this.Params.page = 1
							this.getPayList()
						}, 500)
					} else {
						this.Cloading = false
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				})
				
			},
			Dunning(item) {
				console.log(item,'催款')
				this.FormData.estimateId = item.estimateId
				this.FormData.contactName = item.contactName
				this.FormData.contactPhone = item.contactPhone
				console.log(this.FormData, 'FormData')
				this.$refs.contactPopup.open()
			},
			getTypeList() {
				api({
				    url: 'hkd/get/type/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '状态列表')
						this.typeList = res.data.data
						this.Params.hkStatus = this.typeList[0].status
						this.getPayList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: "none"
					})
				})
			},
			getPayList() {
				api({
					url: `hkd/get/page/list?page=${this.Params.page}&size=${this.Params.size}&hkStatus=${this.Params.hkStatus}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					setTimeout(() => {
						this.visible = true
					}, 1200)
					if(res.code == 200) {
						const data = res.data.data
						console.log(data, '回款列表数据')
						if(this.ListPayment.length == data.totalCount) {
							setTimeout(() => {
								this.loadingStatus = false;
								this.bottom_line_status = true
							}, 500)
						} else {
							if(this.Params.page == 1) {
								this.ListPayment = [...data.list]
								if(this.ListPayment.length == data.totalCount) {
									this.bottom_line_status = true
								}
							} else {
								this.ListPayment = [...this.ListPayment, ...data.list]
							}
							setTimeout(() => {
								this.loadingStatus = false;
							}, 500)
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
						uni.showToast({
							title: err.msg,
							icon: "none"
						})
					})
			},
			queryList() {
				this.Params.page = 1
				this.Params.size = 10
				this.getPayList()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1000)
			},
			onscrollBottom() {
				console.log('下拉触底')
				this.loadingStatus = true
				this.Params.page ++
				this.getPayList()
			},
			sectionChange(item) {
				console.log(item, 'item')
				this.Params.hkStatus = item.status
				this.current = item.index
				this.Params.page = 1
				this.Params.size = 10
				this.ListPayment = []
				// this.visible = false
				this.getPayList()
			}
		}
	}
</script>

<style>
	.page {
		/* background-color: #fff; */
		background-color: #eef6ff;
		width: 100vw;
		height: 100%;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.page{
		.header{
			width: 100%;
			height: 90rpx;
		}
		.Contenlist{
			.scroll-container{
				height: calc(100vh - 90rpx);
				overflow-y: auto;
				.PaymentList{
					width: 98%;
					margin: 0 auto;
					.Payitem{
						background-color: #fff;
						margin-bottom: 15rpx;
						border-radius: 10rpx;
						// color: #ababab;
						letter-spacing: 2rpx;
						.paydetail{
							padding: 5rpx 15rpx;
							.top{
								 height: 60rpx;
								 display: flex;
								 justify-content: space-between;
								.toptitle{
									// color: #333;
									font-size: 32rpx;
									// font-weight: 600;
									margin-top: 15rpx;
								}
								.beOverdue{
									padding: 5rpx 10rpx;
									height: 48rpx;
									line-height: 48rpx;
									text-align: center;
									background: #f40101;
									border-radius: 0rpx 16rpx 0rpx 16rpx;
									color: #fff;
									right: 0;
									top: 0;
									text{
										font-family: PingFang SC, PingFang SC;
										font-weight: 500;
										font-size: 24rpx;
										color: #FFFFFF;
										font-style: normal;
										text-transform: none;
									}
								}
							}
							.content{
								.topC{
									display: flex;
									justify-content: flex-start;
									margin: 15rpx 0;
									color: #000;
									.totalnum{
										margin-right: 40rpx;
									}
								}
								.sychk{
									display: flex;
									align-items: center;
									background: #EAEBF3;
									border-radius: 8rpx;
									padding: 5rpx 10rpx;
									color: #2A3A88;
									width: 440rpx;
									image{
										margin-right: 5rpx;
									}
								}
								.Ccenter{
									display: flex;
									justify-content: space-between;
									align-items: center;
									.left{
										.xycck{
											display: flex;
											align-items: center;
											background: rgba(225, 37, 36, 0.2);
											border-radius: 8rpx;
											padding: 5rpx 10rpx;
											color: #E12524;
											width: 440rpx;
											image{
												margin-right: 5rpx;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			.noOrder{
				height: 1000rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.notitle{
					margin-top: 50rpx;
				}
			}
		}
		
		.DunningPopup{
			width: 580rpx;
			min-height: 520rpx;
			background-color: #fff;
			border-radius: 30rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				padding-top: 30rpx;
				.topCon{
					display: flex;
					justify-content: space-between;
					margin-bottom: 60rpx;
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 32rpx;
						color: #000000;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
				.Concontent{
					width: 90%;
					min-height: 240rpx;
					margin: 0 auto;
				}
				.confirmbtn{
					width: 80%;
					margin: 0 auto;
					margin-top: 30rpx;
				}
			}
		}
		
	}
</style>