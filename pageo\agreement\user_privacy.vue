<template>
	<view>
		<!-- <newNavBar :title="$t('search.xxxq')" width="100vw" @clickLeftIcon="goBack"></newNavBar> -->
		<newNavBar title="隐私政策" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<view v-if="visible">
			<view class="content">
				<u-parse :content="contentinfo"></u-parse>
			</view>
			<!-- <view class="messageInfo">
				<view class="messagedetail">
					<view class="usename">
						<text>{{messageInfo.msName}}</text>
					</view>
					<view class="Mescenter">
						<view class="labelTitleList" v-for="(item, i) in messageInfo.labelTitleList" :key="i">
							<view class="item">
								{{item}}
							</view>
						</view>
						
						<view class="time">
							{{messageInfo.createTime}}
						</view>
					</view>
					
					<view class="content">
						<u-parse :content="messageInfo.basicInfo"></u-parse>
					</view>
				</view>
			</view> -->
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
import store from '@/store'
import api from '@/common/request/index.js'
import componentBottomLine from '../../components/bottom-line/bottom-line';
import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"

	export default{
		data() {
			return {
				visible: false,
				contentinfo: ""
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad(params) {
			console.log(params)
			this.getAboutUS()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			}
		},
		onShow() {
			this.getAboutUS()
		},
		methods:{
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAboutUS() {
				api({
					url: `bs/ys/zc/info?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
				    if (res.code === 200) {
						this.contentinfo = res.data.data
						// console.log(this.contentinfo, '关于我们')
						this.visible = true
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.content{
		padding: 0;
	}
</style>