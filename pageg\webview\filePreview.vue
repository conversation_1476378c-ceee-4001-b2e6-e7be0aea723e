<template>
	<view class="page">
		<view class="pdfview">
			<web-view :src="url"></web-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				PDFurl: '',
			};
		},
		onLoad(params){
			console.log(params, '路径参数')
			if(params.url) {
			  this.url = `../../hybrid/pdf.html?url=${params.url}`
			
			}
			// let {url} = params
		},
		onBackPress(){}
	}
</script>

<style lang="scss" scoped>
	.page{
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
		.pdfview{
			width: 100%;
			height: 500rpx;
		}
	}
</style>
