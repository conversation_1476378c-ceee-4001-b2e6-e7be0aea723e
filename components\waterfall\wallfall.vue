<template>
	<!-- 瀑布流组件 -->
	<view class="waterfall">
		<view class="loadingLayout" v-if="!RecommendList.length && !noData">
			 <uni-load-more status="'loading'"></uni-load-more>
		</view>
		<view class="waterfallCon" v-if="RecommendList.length > 0">
			<view class="waterfall_hot">
				<image :src="hotmag" mode=""></image>
			</view>
			<view class="waterfall_item" v-for="(item,index) in RecommendList" :key="index">
				<view class="waterdetail" @click="Prodetail(item)" :style="{ marginTop: index % 2 !== 0 ? '-398rpx' : '0' }">
					<view class="imageView">
						<image :src="item.recommendImageUrlSquare" mode="aspectFill" @load="onoff='1'" v-show="onoff=='1'"></image>
						<image src="https://file.36sm.cn/xtjyjt/images/common/default.png" mode="aspectFill" v-show="onoff=='0'"></image>
					</view>
					<view class="good_detail">
						<view class="detail_title">
							{{item.productTitle}}
						</view>
						<view class="price">
							<view class="coinname">
								 {{item.tradeCoinName}}
							</view>
							<view class="value">
								{{item.price}}
							</view>
						</view>
						<view class="saledetail">
							<view class="title">
								{{item.recommendReason}}
							</view>
							<view class="value">
								{{item.salesVolume}}
							</view>
							<image src="https://file.36sm.cn/xtjyjt/images/common/firehot.png" style="width: 18rpx;height: 18rpx;" mode="aspectFill"></image>
							
						</view>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	const app = getApp();
	import {
			onLoad,
			onShow,
			onReachBottom
		} from "@dcloudio/uni-app"
	export default {
		data() {
			return {
				onoff: 0,
			    theme_view: app.globalData.get_theme_value_view(),
				// RecommendList: [],
				leftdata: [],
				rightdata: [],
				noData: false,
				classifyList: [],
				sliderList: [
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/13/19162898ab664f72aabdb97b172fb1bb.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/12/2aaaccaab3db405292c8de5f0536423d.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/11/f338e7b4f8b740ee8a0eb79532edd6df.png",
					"https://tuanku.oss-cn-hangzhou.aliyuncs.com/2023/07/11/53fb4099bce24386822b13284f6fe9f0.png"
				],
				hotmag: 'https://file.36sm.cn/mttttxxs/2025/02/06/75c8fc752f234e0cb445266f2f8ee121.png'
			}
		},
		components: {
		},
		mounted() {
		},
		props: {
			RecommendList: {
				type: Array
			},
			CanClick: {
				type:Boolean,
				default: true
			},
			ishome: {
				type:Boolean,
				default: false
			},
			platformType: {
				type: String || Number,
				default: '0'
			}
		},
		computed: {
		},
		onReachBottom() {
			console.log('触底了')
		},
		methods: {
			Prodetail(item) {
				const token = uni.getStorageSync("token")
				if(!token) {
					uni.showToast({
						title: "请先登录!",
						icon: 'none'
					})
					// setTimeout(() => {
						uni.navigateTo({
							url: '/pageo/login/login',
						})
					// }, 1000);
					return
				}
				if(this.ishome) {
					// 首页跳转操作
					this.$emit('homeprodetail', item);
					return
				}
				// console.log(item, '商品信息')
				uni.navigateTo({
					url: `/pageo/product_details/product_details?acid=${item.acId}&platformType=${this.platformType}`
				})
			}
		},
		watch: {
			// RecommendList: {
			// 	immediate: true,
			// 	handler(newVal) {
			// 		// 当 RecommendList 变化时的处理逻辑
			// 		console.log(newVal, '推荐列表更新');
			// 		if(newVal.length > 0) {
			// 			newVal.forEach((item, index) => {
			// 				if (index % 2 === 0) {
			// 					this.leftdata.push(item); // 偶数索引
			// 				} else {
			// 					this.rightdata.push(item); // 奇数索引
			// 				}
			// 			});
			// 		}
			// 	}
			// },
		},
		created() {
			// if(this.RecommendList.length > 0) {
			// 	this.RecommendList.forEach((item, index) => {
			// 		if (index % 2 === 0) {
			// 			this.leftdata.push(item); // 偶数索引
			// 		} else {
			// 			this.rightdata.push(item); // 奇数索引
			// 		}
			// 	});
			// }
		}
	}
</script>

<style lang="scss" scoped>
	.waterfall{
		.waterfallCon {
			width: 98%;
			margin: 0 auto;
			margin-top: 10rpx;
			display: flex;
			flex-wrap: wrap;
			// align-items: flex-start;
			justify-content: space-between;
			.waterfall_hot{
				width: 49%;
				height: 120rpx;
				image{
					border-top-left-radius: 10rpx;
					border-top-right-radius: 10rpx;
					width: 100%;
					height: 120rpx;
				}
			}
			.waterfall_item{
				width: 49%;
				// .waterdetailhot{
				// 	height: 150rpx;
				// 	width: 100%;
				// 	background-color: #fff;
				// 	border-radius: 10rpx;
				// 	margin: 5rpx 0 10rpx 0;
				// 	.imageView{
				// 		border-radius: 10rpx;
				// 		image{
				// 			border-top-left-radius: 10rpx;
				// 			border-top-right-radius: 10rpx;
				// 			width: 100%;
				// 			height: 150rpx;
				// 		}
				// 	}
				// }
				.waterdetail{
					min-height: 360rpx;
					width: 100%;
					background-color: #fff;
					border-radius: 10rpx;
					margin: 5rpx 0 10rpx 0;
					// height: 520rpx;
					// background-color: #fff;
					// border-radius: 10rpx;
					// margin: 5rpx 0 10rpx 0;
					.imageView{
						border-radius: 10rpx;
						image{
							border-top-left-radius: 10rpx;
							border-top-right-radius: 10rpx;
							width: 100%;
							height: 360rpx;
						}
					}
					.good_detail{
						padding: 10rpx;
						.detail_title{
							 display: -webkit-box;
							  -webkit-box-orient: vertical;
							  -webkit-line-clamp: 2; /* 设置行数为2 */
							  overflow: hidden; /* 隐藏超出部分 */
							  text-overflow: ellipsis; /* 用省略号表示多余文本 */
							  font-family: PingFang SC, PingFang SC;
							  font-weight: 500;
							  font-size: 24rpx;
							  color: #333333;
							  line-height: 32rpx;
							  text-align: left;
							  font-style: normal;
							  text-transform: none;
						}
						.price{
							margin-top: 10rpx;
							height: 30rpx;
							line-height: 30rpx;
							color:#FF4000;
							font-size: 22rpx;
							display: flex;
							align-items: center;
							// justify-content: center;
							font-family: PingFang SC-Regular;
							font-weight: 400;
							text-align: center;
							font-style: normal;
							text-transform: none;
							.value{
								color:#FF4000;
								margin-left: 4rpx;
								font-size: 28rpx;
								font-weight: 600;
								font-family: DIN, DIN;
								font-weight: 600;
								text-align: center;
								font-style: normal;
								text-transform: none;
							}
						}
						.saledetail{
							height: 35rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							line-height: 35rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
							color:#a9a9a9;
							display: flex;
							align-items: center;
							font-size: 20rpx;
							letter-spacing: 3rpx;
							.value{
								margin:0 8rpx;
							}
							
						}
					}
				}
			}
		}

	}
</style>