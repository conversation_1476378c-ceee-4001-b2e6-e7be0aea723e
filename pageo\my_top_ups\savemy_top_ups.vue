<template>
	<view class="page">
		<newNavBar :title="$t('accountUser.cz')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
				<u-form-item :label="$t('accountUser.hkje')" prop="amount" borderBottom labelWidth="300rpx">
					<u-input v-model="formData.amount" border="none" :placeholder="$t('accountUser.hkje')" type="digit" @change="inputVal">
						<u--text :text="coinName" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
					<u-form-item :label="$t('accountUser.hkdz')" borderBottom labelWidth="300rpx">
						<view style="margin: ;">
							<u--image :showLoading="true" :src="czqrImg" width="200rpx" height="200rpx" @tap="onTapimage(czqrImg)"></u--image>
						</view>
					</u-form-item>
				
				<u-form-item :label="$t('accountUser.hkpz')" borderBottom labelWidth="300rpx" prop="transactionUrl">
					<view style="width: 100rpx;height: 100rpx;" v-if="formData.transactionUrl == ''">
						<image style="width: 100rpx;height: 100rpx;"@tap="upload_img"
						 :src="common_static_url"></image>
					</view>
					<image v-if="formData.transactionUrl !== ''" style="width: 200rpx;height: 200rpx;"@tap="upload_img"
					 :src="formData.transactionUrl"></image>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.hkbz')" borderBottom labelWidth="300rpx" prop="remarks">
					<u--textarea v-model="formData.remarks" :placeholder="$t('accountUser.hkbz')" autoHeight border="none" clearable
						disableDefaultPadding></u--textarea>
				</u-form-item>
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	// var common_static_url = app.globalData.get_static_url('common');

	
	export default {
		data() {
			return {
				pickerShow: false,
				type:0,
				common_static_url: 'https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png',
				loading:false,
				// 金额单位
				coinName: "",
				// 充值地址二维码
				czqrImg: "",
				formData: {
					// 充值金额
					amount: "",
					// 提交汇款凭证
					transactionUrl: "",
					// 提交备注
					remarks: ""
				},
				rules: {
					amount: {
						required: true,
						// message: '请输入汇款金额',
						message: this.$t('accountUser.hkje'),
						trigger: ['blur']
					},
					transactionUrl: {
						required: true,
						// message: '请上传汇款凭证',
						message: this.$t('accountUser.hkpz'),
						trigger: ['blur', 'change']
					},
					remarks: {
						required: true,
						// message: '汇款备注',
						message: this.$t('accountUser.hkbz'),
						trigger: ['blur', 'change']
					}
				}
			}
		},
		onLoad(data) {
			
		},
		onReady() {
		},
		created() {
			this.getCZqrcode()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			// 获取会员充值码信息
			getCZqrcode() {
				api({
				    url: `mb/cz/get/code/info?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '二维码图片')
						this.czqrImg = res.data.data.codeImg
						this.coinName = res.data.data.coinName
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},
			onTapimage(src) {
				console.log(src, 'src')
				 uni.previewImage({
				        urls: [src],
						current: src
				      });

			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			upload_img() {
				console.log('选择汇款凭证')
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						// console.log(res, 'res')
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							// url: 'https://manger.tuanku.vip/khrWeb/oss/hteditor/upload',
							// url: 'http://************/khrWeb/oss/hteditor/upload',
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								this.formData.transactionUrl = Data.data.data.fileUrl
								console.log(this.formData.transactionUrl, '凭证链接')
								if (Data.code == 200) {
									uni.showToast({
										// title: "上传成功",
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						this.formData.amount = Number(this.formData.amount).toFixed(2)
						console.log(this.formData, '提交参数')
						this.loading = true
						setTimeout(() => {
							this.loading = false
						},2000)
						// let that = this
						api({
							url: `mb/cz/save`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: this.formData
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									// title: '提交成功',
									title: this.$t('message.czcg'),
									icon:'none'
								})
								setTimeout(() => {
									this.$back()
								}, 500)
							}
						})
						
					}
				})
			},
			inputVal(val) {
				console.log(val);
				  
				  // 移除所有非数字和非小数点的字符
				  let temp = val.replace(/[^0-9.]+/g, '');
				  
				  // 如果有多个小数点，只保留第一个小数点
				  const decimalIndex = temp.indexOf('.');
				  if (decimalIndex !== -1) {
				    temp = temp.slice(0, decimalIndex + 1) + temp.slice(decimalIndex + 1).replace(/\./g, '');
				  }
				
				  // 限制小数点最多两位
				  const decimalPart = temp.split('.')[1];
				  if (decimalPart && decimalPart.length > 2) {
				    temp = temp.slice(0, temp.indexOf('.') + 3); // 保留两位小数
				  }
				
				  // 更新输入框值
				  this.$nextTick(() => {
				    this.formData.amount = temp;
				  });
			},
		}
	}
</script>

<style lang="scss" scoped>

</style>