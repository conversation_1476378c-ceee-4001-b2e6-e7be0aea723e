<template>
	<view class="waterfall">
		<view class="loadingLayout" v-if="!RecommendList.length && !noData">
			 <uni-load-more status="'loading'"></uni-load-more>
		</view>
		<view class="waterfallCon" v-if="RecommendList.length > 0">
			<view class="waterfallitem" v-for="(item,index) in RecommendList" :key="index" @tap="goToDemand(item)">
				<view class="infostatus" :style="{ backgroundColor: item.ywStatus == 3 ? 'red' : (item.ywStatus == 2 ? '#31d337' : '#2A3A88FF') }">
					<text>{{item.ywStatusName}}</text>
				</view>
				<view class="toptitle">
					<text>{{item.companyName}}({{item.estimateNub}})</text>
				</view>
				<!-- <view class="titletop">
					<view class="title">
						<text>{{item.companyName}}({{item.departmentId}})</text>
					</view>
					<view class="rightnum">
						<image src="https://file.36sm.cn/xtjyjt/images/common/gtz.png" mode=""></image>
					</view>
				</view> -->
				<view class="centercontent">
					<view class="toptitle">
						<u--text :lines="2" :text="item.buildingInfo"
						  size="26rpx" color="#666666FF"></u--text>
					</view>
					<view class="centerProcess">
						<view class="xqdj content">
							<view class="title">
								<text>需求提交</text>
							</view>
							<view class="name">
								<text>{{item.submitterName}}</text>
							</view>
						</view>
						<view class="ywdj content">
							<view class="title">
								<text>业务对接</text>
							</view>
							<view class="name">
								<text>{{item.liaisonName}}</text>
							</view>
						</view>
						<view class="xmzx content">
							<view class="title">
								<text>项目执行</text>
							</view>
							<view class="name">
								<text>{{item.executorName}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="bottmcontent" v-if="item.ywStatus == 3">
					<text>{{item.remarks}}</text>
				</view>
				<view class="bottom">
					<text>{{item.orderTime.slice(0, 10)}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
			onLoad,
			onShow,
			onReachBottom
		} from "@dcloudio/uni-app"
		
	export default {
		data() {
			return {
				noData: false,
				bottomList: [
				        '团前会议',
				        '资料打印',
				        '开始出团',
				        '客户签到',
				        '出团结束',
				        '客户评价',
				        '物料归还',
				        '提交结算',
				        '项目复盘'
				    ],
					deflautWidth: 20
			}
		},
		components: {
		},
		mounted() {
		},
		props: {
			RecommendList: {
				type: Array
			},
			CanClick: {
				type:Boolean,
				default: true
			},
			ishome: {
				type:Boolean,
				default: false
			},
			platformType: {
				type: String || Number,
				default: '0'
			}
		},
		methods: {
			goToDemand(item) {
				this.$emit('Demanddetail', item)
				// console.log(item, item.estimateId)
				// uni.navigateTo({
				// 	url:`/pageu/demand/index?id=${item.estimateId}`
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.waterfall{
		.waterfallCon{
			.waterfallitem{
				background-color: #fff;
				border-radius: 16rpx;
				margin-bottom: 15rpx;
				padding: 15rpx 25rpx;
				position: relative;
				.infostatus{
					position: absolute;
					padding: 5rpx 10rpx;
					height: 48rpx;
					line-height: 48rpx;
					text-align: center;
					background: #2A3A88FF;
					border-radius: 0rpx 16rpx 0rpx 16rpx;
					color: #fff;
					right: 0;
					top: 0;
					text{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #FFFFFF;
						font-style: normal;
						text-transform: none;
					}
					
				}
				.titletop{
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 10rpx;
					.title{
						width: 366rpx;
						height: 32rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 28rpx;
						color: #333333;
						line-height: 32rpx;
						font-style: normal;
						text-transform: none;
					}
					.rightnum {
						image{
							width: 104rpx;
							height: 36rpx;
						}
					}
				}
				
				.centercontent{
					.toptitle{
						width: 654rpx;
						max-height: 80rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 26rpx;
						color: #666666;
						line-height: 40rpx;
						font-style: normal;
						text-transform: none;
						margin: 10rpx 0;
					}
					.centerProcess{
						display: flex;
						justify-content: space-between;
						.content{
							width: 218rpx;
							height: 108rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							.title{
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 26rpx;
								color: #666666FF;
								line-height: 30rpx;
								font-style: normal;
								text-transform: none;
								margin-bottom: 15rpx;
							}
							.name{
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333FF;
								line-height: 33rpx;
								text-align: center;
								font-style: normal;
								text-transform: none;
							}
						}
						.xqdj{
							width: 33.3%;
							background-color: #F4F5FFFF;
						}
						.ywdj{
							width: 33.3%;
							background-color: #FF763633;
						}
						.xmzx{
							width: 33.3%;
							background-color: #FFEB5533;
						}
					}
					
				}
				.bottom{
					display: flex;
					justify-content: flex-end;
					align-items: center;
					text{
						margin-top: 10rpx;
						// margin: 20rpx 0 5rpx 0;
						height: 32rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #2A3A88;
						line-height: 32rpx;
						font-style: normal;
						text-transform: none;
						letter-spacing: 2rpx;
					}
				}
				.bottmcontent{
					margin-top: 15rpx;
					color: #E12524;
					  font-size: 22rpx;
					  flex-wrap: wrap;
					  word-wrap: break-word;
					  overflow-wrap: break-word;
					  white-space: normal;
				}
			}
		}
	}
</style>