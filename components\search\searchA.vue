<template>
	<view :class="theme_view" style="width: 96%;">
        <view class="search-content pr">
            <view style="display: flex;align-items: center; width:500rpx;margin-left: 20rpx;">
				<image src="https://file.36sm.cn/xtjyjt/images/common/search.png" style="width: 34rpx;height: 34rpx;" @click="search_icon_event"></image>
            	<input
            	    type="text"
            	    confirm-type="search"
            	    :class="'round wh-auto dis-block '+propClass"
            	    :placeholder="propPlaceholder"
            	    :placeholder-class="propPlaceholderClass"
            	    :value="propDefaultValue"
            	    @input="search_input_value_event"
            	    @confirm="search_submit_confirm_event"
            	    @focus="search_input_focus_event"
            	    @blur="search_input_blur_event"
            	    :style="'color:' + propTextColor + ';background:' + propBgColor + ';' + ((propBrColor || null) != null ? 'border:1px solid ' + propBrColor + ';' : '')"/>
				 <view class="search-icon dis-inline-block pa camera" >
				 </view>
			</view>
            <button v-if="propIsBtn" class="search-btn pa" size="mini" type="default" @tap="search_submit_confirm_event">{{$t('message.ssbtn')}}</button>
        </view>
    </view>
</template>
<script>
    const app = getApp();
    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                input_value: '',
				
            };
        },
        components: {},
        props: {
            propUrl: {
                type: String,
                default: '/pages/goods-search/goods-search',
            },
            propFormName: {
                type: String,
                default: 'keywords',
            },
            propPlaceholder: {
                type: String,
                default: '其实搜索很简单 ^_^!',
            },
            propDefaultValue: {
                type: String,
                default: '',
            },
            propPlaceholderClass: {
                type: String,
                default: 'cr-grey-c',
            },
            propClass: {
                type: String,
                default: '',
            },
            propTextColor: {
                type: String,
                default: '#666',
            },
            propBgColor: {
                type: String,
                default: '#fff',
            },
            propBrColor: {
                type: String,
                default: '',
            },
            propIsRequired: {
                type: Boolean,
                default: true,
            },
            propIsOnEvent: {
                type: Boolean,
                default: false,
            },
            propIsOnFocusEvent: {
                type: Boolean,
                default: false,
            },
            propIsOnBlurEvent: {
                type: Boolean,
                default: false,
            },
            propIsOnInputEvent: {
                type: Boolean,
                default: false,
            },
            propIcon: {
                type: String,
                default: 'icon-index-search',
            },
            propIconColor: {
                type: String,
                default: '#ccc',
            },
            propIsIconOnEvent: {
                type: Boolean,
                default: false,
            },
            propIsBtn: {
                type: Boolean,
               default: true
            },
        },
        // 属性值改变监听
        watch: {
            // 默认值
            propDefaultValue(value, old_value) {
                this.setData({
                    input_value: value,
                });
            }
        },
        // 页面被展示
        created: function () {
            this.setData({
                input_value: this.propDefaultValue
            });
        },
        methods: {
            search_input_value_event(e) {
                this.setData({
                    input_value: e.detail.value,
                });
                // 是否回调事件
                if (this.propIsOnInputEvent) {
                    this.$emit('oninput', e.detail.value);
                }
            },

            // 搜索失去焦点事件
            search_input_blur_event(e) {
                this.setData({
                    input_value: e.detail.value,
                });
                // 是否回调事件
                if (this.propIsOnBlurEvent) {
                    this.$emit('onblur', e.detail.value);
                }
            },

            // 搜索获取焦点事件
            search_input_focus_event(e) {
                this.setData({
                    input_value: e.detail.value,
                });
                // 是否回调事件
                if (this.propIsOnFocusEvent) {
                    this.$emit('onfocus', e.detail.value);
                }
            },

            // 搜索确认事件
            search_submit_confirm_event(e) {
                // 是否验证必须要传值
                if (this.propIsRequired && this.input_value === '') {
                    app.globalData.showToast('请输入搜索关键字');
                    return false;
                }

                // 是否回调事件
                if (this.propIsOnEvent) {
                    this.$emit('onsearch', this.input_value);
                } else {
                    // 进入搜索页面
                    uni.navigateTo({
                        url: this.propUrl + '?' + this.propFormName + '=' + this.input_value,
                    });
                }
            },

            // icon事件
            search_icon_event() {
                // 是否回调事件
                if (this.propIsIconOnEvent) {
                    this.$emit('onicon', {});
                }
            },
        },
    };
</script>
<style>
	.search-content{
		/* border: 1rpx solid #333333; */
		border-radius: 40rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #fff;
	}
    .search-content .search-icon {
        z-index: 1;
        /* left: 0rpx; */
        top: 0;
        padding: 14rpx 0 0 0;
        line-height: 28rpx;
        height: 42rpx;
    }

    .search-content input {
        padding: 0 32rpx;
        box-sizing: border-box;
		height: 80rpx;
		line-height: 80rpx;
		border: none !important;
		font-size: 28rpx !important;
		font-family: PingFang SC, PingFang SC;
		font-weight: blod;
		font-style: normal;
		text-transform: none;
    }
	

    .search-content .search-btn {
        width: 115rpx;
        height: 67rpx;
        line-height: 67rpx;
        font-size: 28rpx;
        border-radius: 30rpx;
        padding: 0;
        color: #fff;
        right: 1rpx;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
		background-color: #2A3A88 ;
		font-family: PingFang SC, PingFang SC;
		font-weight: 300;
		letter-spacing: 2rpx;
		font-style: normal;
		text-transform: none;
    }
	.search-content .camera {
		z-index: 2;
		left: 520rpx;
		height: 68rpx;
		line-height: 68rpx;
	}
</style>
