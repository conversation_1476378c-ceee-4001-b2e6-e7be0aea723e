<template>
	<view class="page">
		<view class="cart" v-if="visible">
			<view class="orderList" v-if="ShopCart.length > 0">
				<view class="orderitem" v-for="(item,index) in ShopCart" :key="item.shopCode">
					<view class="orderdetail">
						<view class="Storeinfo">
							<view class="storeName">
								<view class="checkbox">
									<checkbox-group @change="selectedShop(item, index)">
										<label>
												<checkbox class="selected" color="#FF4000" :checked="item.ischecked" style="transform:scale(0.7)" shape="circle"/>
										</label>
									</checkbox-group>
								</view>
								<view style="display: flex;align-items: center;" @tap="goshopdetail(item)">
									<view class="iconIcon">
										<!-- 天猫 -->
										<image src="https://file.36sm.cn/xtjyjt/images/common/altouxiang.png" mode=""></image>
									</view>
									<view class="name" style="width: 420rpx;">
										<u--text :lines="1" :text="item.shopName" bold></u--text>
									</view>
								</view>
							</view>
							<view class="more" @tap="goshopdetail(item)">
								<text>{{$t('search.gd')}}</text>
								<image src="https://file.36sm.cn/xtjyjt/images/common/account/g.png" mode="" style="width: 14rpx; height: 14rpx;"></image>
							</view>
						</view>
						
						<view class="mxList" v-if="item.mxList.length > 0">
								<view class="Mxitem" v-for="(items,i) in item.mxList" :key="i">
								<view class="mexdetail">
									<view class="checkbox">
										<checkbox-group @change="selectedProduct(item, i)">
											<label>
												<checkbox class="selected" color="#FF4000" :checked="items.ischecked" style="transform:scale(0.7)"/>
											</label>
										</checkbox-group>
									</view>
									<view class="Proimg" @tap="goDetil(items)">
										<image :src="items.productSkuImg" @load="onoff='1'" v-show="onoff=='1'" style="width: 155rpx; height: 170rpx;"></image>
										<image src="https://file.36sm.cn/xtjyjt/images/common/default.png" v-show="onoff=='0'" mode="" style="width: 155rpx; height: 170rpx;"></image>
									</view>
									<view class="right" @tap="goDetil(items)">
										<view class="righttop">
											<view class="proname">
												<u--text :lines="1" :text="items.productTitle" bold size="26rpx"></u--text>
											</view>
										</view>
										<view class="skudetail">
											<view class="sku">
												<u--text :lines="1" :text="items.productSkuName" size="22rpx" color="#8b8b8b"></u--text>
												<image src="https://file.36sm.cn/xtjyjt/images/common/down.png" mode="" style="width: 20rpx; height: 20rpx;"></image>
											</view>
											<view class="num">
												<!-- 共 {{items.sellQuantity}} 件 -->
											</view>
										</view>
										
										<view class="skuprice">
											<view class="price">
												<text class="USD">{{items.tradeCoinName}} {{items.sellProductPriceTrade}} / </text>
												<text class="CNY">{{items.baseCoinName}} {{items.sellProductPriceBase}}</text>
											</view>
											<!-- <view class="changenum">
												<image src="https://file.36sm.cn/xtjyjt/images/common/downnum.png" mode="" @tap="changeCrease(items, 'decrease')"></image>
												<view class="num">
												    {{items.sellQuantity}}
												</view>
												<image src="https://file.36sm.cn/xtjyjt/images/common/upnum.png" mode="" @tap="changeCrease(items, 'increase')"></image>
											</view> -->
										</view>
									</view>
								</view>
							</view>
						</view>
					
			            <!-- <view class="totalPrice">
			            	实付款: ￥{{item.payAmountTrade}}
			            </view> -->
					</view>
				</view>
			</view>
			<view v-if="ShopCart.length == 0">
				<view class="noCartdata">
					<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode="" style="width: 150rpx;height: 150rpx;"></image>
					<text class="title">{{$t('message.scskd')}}</text>
				</view>
			</view>
			
			<!-- 结尾 -->
			<!-- <component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
			<component-bottom-line propMsg="没有数据了" v-else></component-bottom-line> -->
			
			<!-- <component-copyright></component-copyright> -->
			<!-- </z-paging> -->
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
			
		<view style="height: 220rpx;"></view>
		<view class="cartbottom" v-if="visible">
			<view class="cartleft">
				<view class="checkbox">
					<checkbox-group @change="selected()">
						<label>
							<checkbox class="selected" color="#FF4000" :checked="ischecked" style="transform:scale(0.7)" shape="circle" :disabled="!visible"/>
						</label>
					</checkbox-group>
				</view>
				<text>{{$t('search.qx')}}</text>
			</view>
			<view class="cartright" v-if="isEdit">
				<view class="noprice" v-show="totalNum == 0">
					<text>{{$t('search.hj')}}:</text>
					<text class="totalnum">￥0</text>
				</view>
				<view class="pricetotal" v-show="totalNum != 0">
					<view class="totaltop">
						<text class="title">{{$t('search.YX')}}: {{totalNum}}, {{$t('search.zj')}}:</text>
						<text class="price">${{totalPricebase}}</text>
					</view>
					<view class="totalbottom">
						<text class="title">{{$t('search.bhgjf')}}</text>
						<text class="price">￥{{totalPricetrade}}</text>
					</view>
				</view>
				<view class="btn">
					<u-button :text="$t('search.xd')" class="addOrder" color="#FF4000" @tap="cartsellgood" :disabled="isloading" :loading="isloading" :loadingText="$t('search.jiaz')"></u-button>
				</view>
			</view>
			<view class="cartright" v-if="!isEdit">
				<view class="btn">
					<u-button :text="$t('search.sc')" class="addOrder" color="#FF4000" @tap="delsellgood" :disabled="isloading" :loading="isloading" :loadingText="$t('search.jiaz')"></u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
  // 状态栏高度
  var bar_height = parseInt(app.globalData.get_system_info('statusBarHeight', 0, true));
  // #ifdef MP-TOUTIAO
  bar_height = 0;
  // #endif
  
  import api from '@/common/request/index.js'
  
	export default {
		data() {
			return {
				// 购物车列表
				ShopCart: [],
				buyNum: 1,
				stockNum: 10,
				// 图片预加载标识
				onoff: 0,
				visible: false,
				loadingStatus: true,
				// 购物车商品选中状态
				ischecked: false,
				// 所选中购物车商品数量
				checkNum: 0,
				isloading: false,
				// 当前是否是编辑的状态
				isEdit: false,
				MxIdList: [],
				changeNumloading: false,
				// location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				changeLoading: false,
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			};
		},

		components: {
			componentCopyright,
			componentBottomLine,
			componentBottomLoading
		},
		onShow() {
			uni.setNavigationBarTitle({
			    title: this.$t('message.scsc')
			});
			this.ischecked = false
			this.visible = false
			this.getShopCart()
		},
		onLoad() {
			// this.getShopCart()
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
		},
		onReachBottom() {
			console.log('触底刷新')
			this.getShopCart()
			setTimeout(() => {
				this.$refs.paging.complete(false);
			}, 1200)
		},
		computed: {
			// location() {
			// 	return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			// },
			// 购物车所选中的商品总数量
			totalNum() {
				let totalNum = 0
				this.ShopCart.forEach(shop => {
					if (Array.isArray(shop.mxList)) {
						shop.mxList.forEach(item => {
						    if (item.ischecked) {
						      totalNum += item.sellQuantity;
						    }
						});
					}
				})
				return totalNum
			},
			// 购物车所选商品trade总金额
			totalPricetrade() {
				let totalprice = 0
				this.ShopCart.forEach(shop => {
					if (Array.isArray(shop.mxList)) {
						shop.mxList.forEach(item => {
						    if (item.ischecked) {
						      totalprice += item.sellProductPriceTrade*item.sellQuantity;
						    }
						});
					}
				})
				return totalprice.toFixed(2)
			},
			// 购物车所选商品base总金额
			totalPricebase() {
				let totalprice = 0
				this.ShopCart.forEach(shop => {
					if (Array.isArray(shop.mxList)) {
						shop.mxList.forEach(item => {
						    if (item.ischecked) {
						      totalprice += item.sellProductPriceBase*item.sellQuantity;
						    }
						});
					}
				})
				return totalprice.toFixed(2)
			}
		},
		methods: {
			goDetil(item) {
				console.log(item, '商品详情', item.productCode)
				uni.navigateTo({
					url: `/pageo/product_details/product_details?acid=${item.productCode}`
				})
			},
			goshopdetail(item) {
				// console.log(item, 'item')
				console.log('店铺详情', item)
				// return
				uni.navigateTo({
					url: `/pageu/shopdetail/shopdetail?sid=${item.shopCode}&shopName=${item.shopName}`
				})
				
				// url: `/pageu/shopdetail/shopdetail?sid=${item.sid}&shopName=${item.nick}`
			},
			changeIsedit() {
				// this.isEdit = !this.isEdit
			},
			changeCrease(item, iswhat) {
				
				console.log(item, iswhat)
				return

				if(this.changeLoading) {
					return
				}
				this.changeLoading = true
				var changenum = 0
				console.log(item, iswhat)
				if(iswhat == 'decrease') {
					console.log('减少', item)
					changenum = item.sellQuantity - 1
				} else {
					console.log('增加', item)
					changenum = item.sellQuantity + 1
				}
				console.log(changenum, 'changenum')
				
				if(this.changeNumloading) {
					return
				}
				this.changeNumloading = true
				const params= {
					cartMxId: item.cartMxId,
					sellQuantity: changenum
				}
				api({
					    url: `car/upd/goods/nub?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post',
						data: params
					}).then(res => {
						if(res.code == 200) {
							console.log(res, 'res')
							this.changeLoading = false
							uni.showToast({
								// title: '操作成功',
								title: this.$t('message.czcg'),
								icon: 'none'
							})
							this.getShopCart()
							this.isEdit = false
							this.changeNumloading = false
						} else {
							uni.showToast({
								// title: '状态码异常',
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: '网络超时,请重试',
							icon: 'none'
						})
					})
				
			},
			queryList() {
				console.log('下拉刷新')
				this.getShopCart()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			delsellgood() {
				if(this.totalNum == 0) {
					// 还未选中商品
					uni.showToast({
						// title: '您还没有选择商品哦!',
						title: this.$t('message.nhmyxzspo'),
						icon: 'none'
					})
					return
				}
				
				if(this.isloading) {
					return
				}
				// 删除所选商品
				this.isloading = true
				const ShopCart = JSON.parse(JSON.stringify(this.ShopCart))
				let MxIdList = []
				ShopCart.forEach(shop => {
				  shop.mxList.forEach(item => {
				    if (item.ischecked) {
				      // 如果子商品的 ischecked 为 true，推送其 cartMxId 到 cartMxIdList 中
				      MxIdList.push(item.cartMxId);
				    }
				  });
				});
				console.log(MxIdList, '所选择要删除的商品')
				
			
				const dataList = {
					cartMxIdList: MxIdList
				}
				api({
					    url: `collect/del/goods/nub?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post',
						data: dataList
					}).then(res => {
						if(res.code == 200) {
							uni.showToast({
								// title: '操作成功',
								title: this.$t('message.czcg'),
								icon: 'none'
							})
							this.getShopCart()
							this.ischecked = false
							setTimeout(() => {
								this.isloading = false
							}, 1500)
						} else {
							uni.showToast({
								// title: '状态码异常',
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							title: '网络超时,请重试',
							icon: 'none'
						})
					})
				
			},
			cartsellgood() {
				return
				if(this.totalNum == 0) {
					uni.showToast({
						// title: '您还没有选择商品哦!',
						title: this.$t('message.nhmyxzspo'),
						icon: 'none'
					})
					return
				}
				
				// 购物车下单
				this.isloading = true
				setTimeout(() => {
					this.isloading = false
				}, 1500)
				
				const ShopCart = JSON.parse(JSON.stringify(this.ShopCart))
				//  let cartMxIdList = ShopCart.filter(shop => {
				//     // 过滤父商品，保留至少有一个选中子商品的父商品
				//     shop.mxList = shop.mxList.filter(item => item.ischecked); // 只保留子商品中 ischecked 为 true 的项
				
				//     // 如果子商品中有选中项，则保留父商品
				//     return shop.mxList.length > 0;
				//   });
				//   console.log(cartMxIdList, '所选中下单的商品')
				let MxIdList = []
				this.MxIdList = []
				ShopCart.forEach(shop => {
				  shop.mxList.forEach(item => {
				    if (item.ischecked) {
				      // 如果子商品的 ischecked 为 true，推送其 cartMxId 到 cartMxIdList 中
				      MxIdList.push(item.cartMxId);
					  this.MxIdList.push(item.cartMxId)
				    }
				  });
				});
				uni.navigateTo({
					url: `/pages/digital-avatar/confirmOrder?MxIdList=${JSON.stringify(this.MxIdList)}`
				})
				
				return
				const dataList = {
					cartMxIdList: MxIdList
				}
				api({
					    url: `car/sell/goods/info?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post',
						data: dataList
					}).then(res => {
						if(res.code == 200) {
							// console.log(res, 'res')
							console.log(this.MxIdList, 'MxIdListMxIdList')
							// 进入生成订单页面
							uni.navigateTo({
								url: `/pages/digital-avatar/confirmOrder?MxIdList=${JSON.stringify(this.MxIdList)}`
							})
						} else {
							uni.showToast({
								// title: '状态码异常',
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							// title: '异常',
							title: res.msg,
							icon: 'none'
						})
					})
			},
			changeBuyNum() {
			},
			// 处理父级(商店)选中状态
			selectedShop(item) {
				item.ischecked = !item.ischecked;
				// 同步更新所有子元素的选中状态
				item.mxList.forEach((product) => {
				      product.ischecked = item.ischecked;
				});
				console.log(item, 'item')
			},
			// 处理子集选中框
			selectedProduct(item, index) {
				console.log(item, 'item', index)
				// 切换子元素的选中状态
				item.mxList[index].ischecked = !item.mxList[index].ischecked;
				// 如果所有子元素都选中了 那么父级也要被选中
				item.ischecked = item.mxList.every(product => product.ischecked);
				console.log(item, '选中的父元素')
			},
			selected() {
				console.log(this.ischecked, 'ischecked')
				this.ischecked = !this.ischecked
				this.ShopCart.forEach(shop => {
				      shop.ischecked = this.ischecked;
				      // 如果该商品有子商品列表（mxList），将所有子商品的 ischecked 属性设为 true
				      if (Array.isArray(shop.mxList)) {
				        shop.mxList.forEach(item => {
				          item.ischecked = this.ischecked;  // 子商品也设为选中
				        });
				      }
				    });
			},
			// 获取购物车列表数据
			getShopCart() {
				api({
						url: `collect/get/car/list?location=${this.location}`,
						header: {
							"jarepair-platform": uni.getStorageSync("token")
						},
						method: 'post'
					}).then(res => {
						if(res.code == 200) {
						    console.log(res.data.data, '收藏')
							// 给子元素和父元素都添加ischecked 用来鉴别是否选中
							if(res.data.data.length > 0) {
								res.data.data.forEach((item) => {
									item.ischecked = false
									if(item.mxList.length > 0) {
										item.mxList.forEach((shop) => {
											shop.ischecked = false
										})
									}
								})
							}
							  this.ShopCart = [...res.data.data]
							  console.log(this.ShopCart, 'ShopCart')
							  this.visible = true
						} else {
							uni.showToast({
								// title: '状态码异常',
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch(err => {
						uni.showToast({
							// title: '异常',
							title: '网络超时,请重试',
							icon: 'none'
						})
					})
				},
		}
	};
</script>
<style>
	.page {
		/* background-color: #f4f4f4; */
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style scoped lang="scss">
	.topHeader{
		width: 100%;
		height: 80rpx;
		background-color: #FFFFFF;
		line-height: 100rpx;
		position: fixed;
		z-index: 999;
		.editchange{
			display: flex;
			justify-content: space-between;
			background-color: #f4f4f4;
			.carttitle{
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
				margin-left: 30rpx;
			}
			.editcart{
				padding-right: 20rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 30rpx;
				color: #000;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}
	
	 .cart{
		 // margin-top: 100rpx;
		 width: 100%;
		 .orderList{
			 // margin-bottom: 30rpx;
			 .orderitem{
			 	width: 96%;
			 	background-color: #fff;
			 	border-radius: 15rpx;
			 	margin: 0 auto;
			 	margin-bottom: 15rpx;
			 	.orderdetail{
			 		padding-top: 10rpx;
			 		.Storeinfo{
			 			height: 60rpx;
						line-height: 60rpx;
			 			padding: 0 25rpx 0 18rpx;
			 			display: flex;
			 			align-items: center;
			 			justify-content: space-between;
			 			.storeName{
			 				display: flex;
			 				align-items: center;
							.checkbox{
								/deep/ .uni-checkbox-input {
								    width: 36rpx;
								    height: 36rpx;
								    border-radius: 50%;
								    border: 1px solid #ccc;
								}
							}
			 				.iconIcon{
								margin: 10rpx 10rpx 0 10rpx;
								image{
			 					   width: 32rpx;
			 					   height: 32rpx;
								   border-radius: 50%;
								}
			 					// color: #fff;
			 					// font-size: 20rpx;
			 					// text-align: center;
			 					// background-color: red;
			 					// border-radius: 15rpx;
			 					// margin-right: 8rpx;
			 				}
			 				.name{
			 					color: #000;
								font-weight: 600;
			 					margin-right: 12rpx;
			 				}
			 				image{
			 					width: 15rpx;
			 					height: 15rpx;
			 				}
			 			}
			 					.more{
			 						display: flex;
			 						align-items: center;
			 						text{
			 							margin-right: 12rpx;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 24rpx;
										color: #5a5a5a;
										font-style: normal;
										text-transform: none;
			 						}
			 					}
			 			.orderstatus{
			 				color: #ccc;
			 				font-size: 28rpx;
			 				font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
			 				letter-spacing: 2rpx;
			 				font-weight: 300;
			 			}
			 		}
			 		.mxList{
			 			.Mxitem{
			 				padding: -10rpx 18rpx;
			 				.mexdetail{
			 					display: flex;
			 					justify-content: space-between;
								align-items: center;
								margin-bottom: 10rpx;
								.checkbox{
									margin-left: 20rpx;
									display: flex;
									align-items: center;
									/deep/ .uni-checkbox-input {
									    width: 36rpx !important;
									    height: 36rpx !important;
									    border-radius: 50% !important;
									    border: 1px solid #ccc !important;
									}
								}
								.Proimg{
									height: 100%;
									image{
										border-radius: 10rpx;
									}
								}
			 					.right{
									padding: 30rpx 0;
			 						width: 65%;
									display: flex;
									flex-direction: column;
									justify-content: space-between;
			 						.righttop{
			 							font-weight: 500;
			 							color: #000;
			 							display: flex;
			 							justify-content: space-between;
			 							margin-bottom: 10rpx;
										.proname{
											width: 420rpx;
										}
			 						}
			 						.skudetail{
			 							color: #ccc;
			 							display: flex;
			 							justify-content: space-between;
										align-items: center;
										letter-spacing: 1rpx;
										max-width: 400rpx;
										.sku{
											min-width: 160rpx;
											display: flex;
											align-items: center;
											padding:8rpx 10rpx;
											border-radius: 5rpx;
											background-color: #F4F4F4;
											text{
												font-family: PingFang SC, PingFang SC;
												font-weight: 500;
												font-size: 20rpx;
												color: #777777;
												font-style: normal;
												text-transform: none;
												margin-right: 20rpx;
											}
										}
			 							
			 						}
									
									.skuprice{
										margin-top: 40rpx;
										display: flex;
										align-items: center;
										justify-content: space-between;
										.price{
											font-family: DIN, DIN;
											color: #FF4000;
											font-style: normal;
											text-transform: none;
											.USD{
												font-weight: 500;
												font-size: 26rpx;
											}
											.CNY{
												margin-left: 5rpx;
												min-width: 60rpx;
												height: 20rpx;
												padding: 3rpx 6rpx;
												background-color: #FFE8E0;
												font-size: 18rpx;
												border-radius: 15rpx;
											}
										}
										.changenum{
											width: 180rpx;
											padding: 0 10rpx;
											display: flex;
											align-items: center;
											justify-content: space-between;
											image{
												width: 30rpx;
												height: 30rpx;
											}
											.num{
												font-size: 24rpx;
												width: 72rpx;
												height: 40rpx;
												line-height: 40rpx;
												text-align: center;
												background: #F4F4F4;
												border-radius: 12rpx;
											}
										}
									}
			 					}
			 				}
			 			}
			 		}
			 		
			 		.totalPrice{
			 			padding: 0 25rpx;
			 			color: #000;
			 			font-weight: 500;
			 			display: flex;
			 			flex-direction: row-reverse;
			 			margin-bottom: 2rpx;
			 		}
			 	}
			 }
		 }
		 
		 .noCartdata{
			 display: flex;
			 flex-direction: column;
			 align-items: center;
			 justify-content: center;
			 height: 1000rpx;
			 .title{
				 margin-top: 40rpx;
				 color: #ccc;
				 letter-spacing: 1rpx;
			 }
		 }
	 }
	 .cartbottom{
	 		 position: fixed;
	 		 bottom: 0rpx;
	 		 left: 0;
	 		 width: 100%;
	 		 height: 120rpx;
	 		 line-height: 110rpx;
	 		 display: flex;
	 		 align-items: center;
			 justify-content: space-between;
	 		 z-index: 999;
	 		 background-color: #fff;
			 box-shadow: 0 -5rpx 5rpx rgba(0, 0, 0, 0.1);
			 .cartleft{
				 margin-left: 20rpx;
				 display: flex;
				 align-items: center;
				 .checkbox{
				 	/deep/ .uni-checkbox-input {
				 	    width: 36rpx;
				 	    height: 36rpx;
				 	    border-radius: 50%;
				 	    border: 1px solid #ccc;
				 	}
				 }
			 }
			 
			 .cartright{
				 display: flex;
				 align-items: center;
				 margin-right: 20rpx;
				 .noprice{
					 color: #bdbdbd;
					 .totalnum{
						 margin-left: 20rpx;
						 font-weight: 600;
					 }
				 }
				 .pricetotal{
					 height: 100%;
					 min-width: 200rpx;
					 .totaltop{
						 height: 48rpx;
						 line-height: 48rpx;
						 display: flex;
						 justify-content: space-between;
						 .title{
							 font-family: PingFang SC, PingFang SC;
							 // font-weight: 600;
							 font-size: 24rpx;
							 color: #979797;
							 font-style: normal;
							 text-transform: none;
						 }
						 .price{
							 font-weight: 600;
							 font-size: 30rpx;
							 color: #FF4000;
							 font-style: normal;
							 text-transform: none;
							 margin-left: 5rpx;
						 }
					 }
					 .totalbottom{
						 height: 48rpx;
						 line-height: 48rpx;
						 display: flex;
						 justify-content: space-between;
						 .title{
							 font-family: PingFang SC, PingFang SC;
							 font-size: 18rpx;
							 color: #979797;
							 font-style: normal;
							 text-transform: none;
						 }
						 .price{
							 font-family: DIN, DIN;
							 // font-weight: 500;
							 font-size: 22rpx;
							 color: #FF4000;
							 font-style: normal;
							 text-transform: none;
						 }
					 }
				 }
				 .btn{
					 margin-left: 20rpx;
					 width: 180rpx;
				 }
			 }
	 }
</style>
