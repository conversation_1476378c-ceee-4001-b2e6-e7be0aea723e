<template>
	<view class="page">
		<newNavBar :title="jjmmName" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm" >
				<u-form-item :label="$t('accountUser.sjh')" borderBottom labelWidth="300rpx" prop="phone">
					<u-input v-model="formData.phone" border="none" :placeholder="$t('accountUser.sjh')"
					 type="text" @input="validatePhoneNumber" maxlength="13">
						<u--text :text="$t('accountUser.sjh')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.yzm')" borderBottom labelWidth="300rpx" prop="code">
					<u--input v-model="formData.code" type="number" border="none" :placeholder="$t('accountUser.yzm')" clearable></u--input>
					<template slot="right">
						<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
						<u-button @tap="getCode" :text="btntext" type="warning" size="mini" style="background: linear-gradient(to right, #2A3A88FF, #3c9cff) !important;"></u-button>
					</template>
				</u-form-item>
				
				
				<u-form-item :label="$t('accountUser.zjmm')" borderBottom labelWidth="300rpx" prop="password">
					<u-input v-model="formData.password" border="none" :placeholder="$t('message.qsrzjmmlw')" type="password" maxlength="6">
						<u--text :text="$t('accountUser.zjmm')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import md5 from 'js-md5';
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				pickerShow: false,
				type:0,
				loading:false,
				formData: {
					// 手机号
					phone: "",
					code: "",
					memberId: "",
					password: "",
					areaCode: 86
					
				},
				jjmmName: "",
				status: "",
				memberId: "",
				// btntext: "获取验证码",
				btntext: this.$t('message.hqyzm'),
				rules: {
					phone: {
						required: true,
						// message: '请输入手机号',
						message: this.$t('message.qsrsjh'),
						trigger: ['blur']
					},
					code: {
						required: true,
						// message: '请输入验证码',
						message: this.$t('message.qsryzm'),
						trigger: ['blur']
					},
					password: {
						required: true,
						// message: '请输入资金密码',
						message: this.$t('message.qsrzjmm'),
						trigger: ['blur']
					}
				}
			}
		},
		onLoad(params) {
			console.log(params, '路由参数')
			this.jjmmName = params.jjmmName
			this.jjmmstatus = params.jjmmstatus
			this.memberId = params.memberId
		},
		onReady() {
		},
		created() {
		},
		computed: {
			// currentWallet() {
			// 	return this.walletTypelist.find(item => item.coinId === this.formData.coinId);
			// }
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			getCode() {
				this.$refs.uForm.validateField('phone', (res) => {
					if (!res.length) {
						if (this.$refs.uCode.canGetCode) {
							uni.showLoading({
								// title: '正在获取验证码',
								title: this.$t('message.zzhqyzm')
								
							})
							let data = {
								phone: this.formData.phone,
								areaCode: this.formData.areaCode
							}
							api({
							    url: `mb/login/jjmm/sendCode?location=${this.location}`,
							    header: {
							        "jarepair-platform": uni.getStorageSync("token")
							    },
							    method: 'POST',
								data: data
							}).then(res => {
								if (res.code == 200) {
									// console.log(res)
									this.$refs.uCode.start();
									uni.showToast({
										// title: '验证码发送成功',
										title: this.$t('message.yzmfscg'),
										icon: 'none'
									})
								} else {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}
							}).catch(err => {
								uni.showToast({
									title: '请求异常',
									icon: "none"
								})
							})
							
						} else {
							uni.$u.toast(this.$t('message.djsjszfs'));
						}
					}
				})
			
			},
			codeChange(text) {
				this.btntext = text;
			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						// console.log(this.formData, '提交参数')
						const data = {
							code: this.formData.code,
							memberId: this.memberId,
							password: md5(this.memberId + md5(this.formData.password)),
							phone: this.formData.phone
						}
						console.log(data, '提交参数')
						this.loading = true
						setTimeout(() => {
							this.loading = false
						}, 2000)
						api({
							url: `mb/login/set/jjmm/info?location=${this.location}`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: data
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								this.formData.jjMm = ''
								uni.showToast({
									// title: '操作成功',
									title: this.$t('message.czcg'),
									icon:'none'
								})
								setTimeout(() => {
									this.$back()
								}, 500)
							}
						})
						
					}
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.totalAmount = temp
				})
			},
			inputValCardNo(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.cardNo = temp
				})
			},
			validateInteger(event) {
				let value = this.formData.amount;
				if (!/^\d*$/.test(value)) {
					this.formData.amount = value.replace(/[^\d]/g, '');
				}
			},
			validatePhoneNumber() {
			  }
		}
	}
</script>

<style lang="scss" scoped>

</style>