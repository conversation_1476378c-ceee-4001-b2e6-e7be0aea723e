<template>
	<view class="page">
		<newNavBar :title="$t('accountUser.zjzr')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm" v-if="currentWallet">
				<u-form-item :label="'账号' + currentWallet.coinName"  borderBottom labelWidth="200rpx">
					<u-input v-model="currentWallet.balance" border="none"  type="text" disabled>
						<u--text :text="'账号' + currentWallet.coinName+ ':'" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				<u-form-item :label="$t('accountUser.dfsjh')" borderBottom labelWidth="500rpx" prop="otherPhone">
					<u-input v-model="formData.otherPhone" border="none" :placeholder="$t('accountUser.dfsjh')"
					 type="text" @input="validatePhoneNumber" maxlength="13">
						<u--text :text="$t('accountUser.sjh')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				
				<u-form-item :label="$t('accountUser.sl')" borderBottom labelWidth="200rpx" prop="amount">
					<u-input v-model="formData.amount" border="none" :placeholder="$t('accountUser.sl')" type="text" @input="validateInteger" maxlength="10">
						<u--text :text="$t('accountUser.sl')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.zjmm')" borderBottom labelWidth="300rpx" prop="jjMm">
					<u-input v-model="formData.jjMm" border="none" :placeholder="$t('message.zjmm')" type="password" maxlength="6">
						<u--text :text="$t('accountUser.zjmm')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				<u-form-item :label="$t('accountUser.jyts')" borderBottom labelWidth="300rpx" >
					<text style="color: #ccc;">{{currentWallet.zrxy}}</text>
				</u-form-item>
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import md5 from 'js-md5';
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				pickerShow: false,
				type:0,
				loading:false,
				formData: {
					// 整数
					amount: "",
					// 资金类型
					coinId: "",
					// 对方手机号
					otherPhone: "",
					// 资金密码
					jjMm: ""
					
				},
				walletTypelist: [],
				my_money: {
					balance: "",
					coinName: ""
				},
				rules: {
					otherPhone: {
						required: true,
						// message: '请输入对方手机号',
						message: this.$t('message.qsrdfsjh'),
						trigger: ['blur']
					},
					amount: {
						required: true,
						// message: '请输入数量',
						message: this.$t('message.qsrsl'),
						trigger: ['blur']
					},
					jjMm: {
						required: true,
						// message: '请输入资金密码',
						message: this.$t('message.qsrzjmm'),
						trigger: ['blur']
					}
				}
			}
		},
		onLoad(params) {
			console.log(params, 'params')
			this.formData.coinId = params.coinId
		},
		onReady() {
		},
		created() {
			this.getwalletType()
		},
		computed: {
			currentWallet() {
				return this.walletTypelist.find(item => item.coinId === this.formData.coinId);
			}
		},
		methods: {
			// 资金转让信息
			getwalletType() {
				api({
				    url: 'mb/wallet/zj/get/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '转让的类型')
						this.walletTypelist = [...res.data.data]
						console.log(this.walletTypelist, '资金转让类型')
					} else {
						uni.showToast({
							title: '请求异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '请求异常',
						icon: "none"
					})
				})
			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						if(this.currentWallet.balance - this.formData.amount < 0 || this.currentWallet.balance == 0 || this.formData.amount == 0) {
							uni.showToast({
								// title: '转让余额异常!',
								title: this.$t('message.ztmyc'),
								icon:'none'
							})
							return
						}
						// this.formData.jjMm = md5(this.formData.jjMm)
						let data = {
							jjMm: md5(this.formData.jjMm),
							amount: this.formData.amount,
							coinId: this.formData.coinId,
							otherPhone: this.formData.otherPhone
						}
						
						console.log(data, '提交参数')

						this.loading = true
						setTimeout(() => {
							this.loading = false
						}, 2000)
						api({
							url: `mb/wallet/zj/save`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: data
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								this.formData.jjMm = ''
								uni.showToast({
									// title: '转让成功',
									title: this.$t('message.zrcg'),
									icon:'none'
								})
								setTimeout(() => {
									this.$back()
								}, 500)
							}
						})
						
					}
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.totalAmount = temp
				})
			},
			inputValCardNo(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.cardNo = temp
				})
			},
			validateInteger(event) {
				let value = this.formData.amount;
				if (!/^\d*$/.test(value)) {
					this.formData.amount = value.replace(/[^\d]/g, '');
				}
			},
			validatePhoneNumber() {
			   // console.log(this.formData.otherPhone, 'otherPhone')
			  }
		}
	}
</script>

<style lang="scss" scoped>

</style>