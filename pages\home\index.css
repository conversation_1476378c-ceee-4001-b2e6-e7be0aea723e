/**
 * 顶部内容
 */
.home-top-nav-content {
    background-repeat: no-repeat;
    background-size: 100% auto;
    min-height: calc(var(--status-bar-height) + 130rpx);
    /* #ifdef H5 */
    min-height: calc(var(--status-bar-height) + 160rpx);
    /* #endif */
    padding-top: calc(var(--status-bar-height) + 5px);
}

.home-top-nav-logo {
    text-align: left;
    height: 56rpx;
}

.home-top-nav-logo-image {
    width: 56rpx;
    height: 100% !important;
}

.home-top-nav-logo-title {
    font-weight: bold;
    font-size: 42rpx;
    /* #ifdef H5 || MP-TOUTIAO || APP */
    font-size: 24px;
    /* #endif */
}

/**
 * 右侧图标导航
 */
/* #ifdef H5 || MP-TOUTIAO || APP */
.nav-top-right-icon {
    top: -34px;
    right: 28rpx;
    z-index: 12;
    line-height: 28px;
    /* #ifdef MP-TOUTIAO */
    top: 2px;
    /* #endif */
}

.nav-top-right-icon .item:not(:last-child) {
    margin-right: 20rpx;
}

.nav-top-right-icon .badge-icon {
    top: -10px;
    right: 2px;
}

/* 轮播图顶部的文字 */
.banner_top {
	background-color: aqua;
	width: 100%;
	height: 38rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 4rpx 0 4rpx 30rpx;
}
.banner_middle {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.banner_top_img {
	width: 36rpx;
	height: 36rpx;
}
.banner_top_title {
	font-size: 34rpx;
	font-weight: 500;
}

/**
 * 搜索
 */
.search-fixed-seat {
    padding-top: 82rpx;
    /* #ifdef MP-TOUTIAO */
    padding-top: 60rpx;
    /* #endif */
}

.search-content-fixed {
    position: fixed !important;
    top: 0;
    z-index: 11;
    width: 100%;
}

.search-content-input {
    width: calc(100% - 320rpx);
    /* #ifdef MP-ALIPAY */
    padding-left: 60rpx;
    /* #endif */
}

.search-content-fixed-content {
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    padding-bottom: 24rpx;
    /* #ifdef H5 || APP */
    padding-top: 15rpx !important;
    padding-bottom: 15rpx !important;
    /* #endif */
}

/* #ifdef H5 || MP-TOUTIAO || APP */
.search-content-fixed .nav-top-right-icon {
    top: 11px !important;
}

/* #endif */

/**
 * 推荐文章
 */
.article-list .new-icon {
    width: 130rpx !important;
    height: 42rpx !important;
}

.article-list .right-content {
    width: calc(100% - 150rpx);
}

.article-list .right-content swiper {
    height: 40rpx;
}

.notice {
    margin: 20rpx 0 14rpx 0;
}

/*
 * 限时秒杀 - 插件
 */

.plugins-seckill-data {
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: top;
    background-color: #fff;

}

.plugins-seckill-data .icon {
    max-width: 140rpx;
    max-height: 34rpx;
}

.plugins-seckill-data .goods-list .swiper {
    height: 310rpx !important;
}

.plugins-seckill-data .goods-list .item .goods-img {
    width: 100%;
    height: 210rpx;
}

.plugins-seckill-data .goods-list .goods-base .icon {
    right: 20rpx;
    bottom: 13rpx;
}

/**
 * 购买记录 - 插件
 */
.plugins-salerecords swiper {
    height: 506rpx;
}

.plugins-salerecords image {
    width: 40rpx !important;
    height: 40rpx !important;
    border-radius: 50%;
}

.plugins-salerecords .item-content {
    width: 24%;
}

.plugins-salerecords .item-content:nth-child(1) {
    width: 36%;
}

.plugins-salerecords .item-content:nth-child(2) {
    width: 40%;
}

/**
 * 标签 - 秒杀单独修改样式
 */
.plugins-seckill-data .plugins-label-bottom-left,
.plugins-seckill-data .plugins-label-bottom-center,
.plugins-seckill-data .plugins-label-bottom-right {
    bottom: calc(100% - 240rpx);
}

/**
 * 首页中间广告- 插件
 */
.plugins-homemiddleadv .item {
    width: calc(50% - 10rpx);
}

.plugins-homemiddleadv .item:nth-of-type(2n + 1) {
    float: left;
}

.plugins-homemiddleadv .item:nth-of-type(2n) {
    float: right;
}

/**
 * 弹屏广告 - 插件
 */
.plugins-popupscreen {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 20;
    background-color: rgb(0 0 0 / 0.3);
}

.plugins-popupscreen .close {
    right: 10%;
    top: 0;
    z-index: 1;
    width: 46rpx;
    height: 46rpx;
    line-height: 46rpx;
    background-color: rgb(4 4 4 / 0.3);
    border: solid 1px #a9a9a9;
}

.plugins-popupscreen .content {
    margin-top: calc(50vh - 200rpx) !important;
}

.plugins-popupscreen .content image {
    width: 600rpx;
}