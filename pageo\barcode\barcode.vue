<template>
	<view>
		<view v-if="visible">
			<image class="bgPic"
				src="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/02/17/3cf5a3291e3f4f348ab310868fb27058.png"
				mode=""></image>
			<view class="outer">
				<image class="top" src="https://file.36sm.cn/xtjyjt/images/common/barcode/third.png" mode=""></image>
				<view class="mine">
					<view class="code">
						<!-- #ifdef MP-WEIXIN -->
						<view class="qrcode" style="text-align: center;">
							<uv-qrcode class="codeImage" :value="qrcodeImg" size="200"></uv-qrcode>
						</view>
						<!-- #endif -->

						<!-- #ifndef MP-WEIXIN -->
						<view class="qrcode" style="">
							<uqrcode ref="uqrcode" canvas-id="qrcode" :value="qrcodeImg" size="100"></uqrcode>
						</view>
						<!-- #endif -->

						<text>{{$t('accountUser.sysyqhy')}}</text>
						<button @tap="clonecode">{{$t('accountUser.yqhy')}}</button>
					</view>
					<view class="mine-text">
						<text>{{$t('accountUser.wdtjm')}}</text>
						<view class="codeText" @tap="clonecode">
							<text style="font-size: 20rpx;">{{code_invite}}</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/barcode/five.png"
								style="width: 33rpx; height: 33rpx;" mode="">
							</image>
						</view>
					</view>
					<view class="invite">
						<text @tap="goinivate">{{$t('accountUser.wdyqlb')}}</text>
						<view class="line">

						</view>
					</view>

				</view>
			</view>
		</view>
		<showLoading v-else></showLoading>
	</view>
</template>

<script>
	import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js';
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				onoff: 0,
				visible: false,
				invite_code: "",
				qrcodeImg: "",
				code_invite: '',
				options: {
					size: 400,

				}
			}
		},
		onShow() {
			this.init()
			const title = this.$t('accountUser.wdewm'); // 获取国际化文本
			console.log(title, 'title')
			uni.setNavigationBarTitle({
				title: title
			});
		},
		onReady() {},
		onLoad() {},
		methods: {
			async init() {
				await this.getMemberInv()
				await this.identify_person()
			},
			identify_person() {
				api({
					url: 'mb/member/invite/code',
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data, '邀请推荐码码')
						this.invite_code = res.data.data
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},

			// 获取会员的邀请码图片和链接
			getMemberInv() {
				api({
					url: `mb/member/invite/code/info`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						// console.log(res.data.data, 'resresres')
						this.qrcodeImg = res.data.data.codeImg
						this.code_invite = res.data.data.coinName
						console.log(this.qrcodeImg, 'qrcodeImg')
					} else {
						uni.showToast({
							title: '异常',
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},
			clonecode() {
				// 复制链接
				console.log(this.qrcodeImg, '当前链接 复制')
				// #ifdef H5
				let oInput = document.createElement('input');
				oInput.value = this.qrcodeImg;
				document.body.appendChild(oInput);
				oInput.select(); // 选择对象;
				document.execCommand("Copy"); // 执行浏览器复制命令
				// 复制成功提示;
				uni.showToast({
					title: this.$t('accountUser.fzcg'),
					icon: 'none',
					duration: 2000
				});
				oInput.remove()
				// #endif

				// #ifndef H5
				uni.showToast({
					title: this.$t('message.gndtj'),
					icon: 'none',
				});
				// #endif
			},
			goinivate() {
				uni.navigateTo({
					url: '/pageo/invitation/invitation',

				});
			}
		},
		onLoad() {
			setTimeout(() => {
				this.$data.visible = true
			}, 1200)
		}
	}
</script>



<style scoped lang="scss">
	// /deep/ .uqrcode-canvas {
	// 	width: 100% !important;
	// 	height: 100% !important;
	// }
	// /deep/ .uqrcode {
	// 	width: 272rpx !important;
	// 	height: 272rpx !important;
	// }
	// /deep/ .uqrcode-canvas-wrapper {
	// 	width: 272rpx !important;
	// 	height: 272rpx !important;
	// }

	.bgPic {
		position: absolute;
		width: 100vh;
		height: 100vh;
	}

	.outer {
		width: 100vw;
		height: 100vh;
		text-align: center;

		.top {
			height: 51rpx;
			margin-top: 112rpx;
		}

		.mine {
			margin: auto;
			transform: translateY(-30rpx);
			width: 589rpx;
			height: 1143rpx;
			background: url("https://file.36sm.cn/xtjyjt/images/common/barcode/first.png") no-repeat;
			background-size: cover;
			background-position: center;

			.mine-text {
				padding-top: 43rpx;

				text {
					font-size: 29rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 400;
					color: #3D3D3D;
					line-height: 42rpx;
					letter-spacing: 1px;
					-webkit-background-clip: text;
				}

				.codeText {
					width: 322rpx;
					height: 98rpx;
					background: #EBEBEB;
					margin: auto;
					margin-top: 25rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						height: 98rpx;
						font-size: 40rpx;
						font-family: Source Han Sans, Source Han Sans;
						font-weight: 500;
						color: #3D3D3D;
						line-height: 98rpx;
						letter-spacing: 1px;
						-webkit-background-clip: text;
						margin-right: 20rpx;
					}
				}
			}

			.invite {
				width: 456rpx;
				margin: auto;
				margin-top: 25rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				padding-bottom: 85rpx;
				// border-bottom: 1rpx solid #E2E2E2;

				text {
					min-width: 187rpx;
					height: 42rpx;
					font-size: 29rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #0C71FD;
					line-height: 42rpx;
					letter-spacing: 1px;
					-webkit-background-clip: text;
				}

				.line {
					width: 187rpx;
					height: 1rpx;
					background-color: #0C71FD;
				}
			}


			.code {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				padding: 58rpx 0;
				border-bottom: 1rpx solid #E2E2E2;

				text {
					margin-top: 29rpx;
					width: 317rpx;
					height: 72rpx;
					font-size: 25rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #3D3D3D;
					line-height: 36rpx;
					letter-spacing: 1px;
					-webkit-background-clip: text;
				}

				button {
					color: #FFFFFF;
					font-size: 25rpx;
					width: 330rpx;
					height: 65rpx;
					font-weight: 500;
					letter-spacing: 1rpx;
					font-family: Source Han Sans, Source Han Sans;
					margin-top: 58rpx;
					background: linear-gradient(270deg, #2294FE 0%, #324DFD 100%);
					box-shadow: 0rpx 2rpx 5rpx 0rpx rgba(0, 0, 0, 0.1);
				}
			}
		}
	}
</style>