<template>
	<view class="box">
		<view class="boxulvalue" @click="elementItself">
			<scroll-view scroll-y="true"  ref="scrollContainer" class="uni-select__selector-scroll"  :style="{height: inputHeight?'auto':'80rpx'}">
				<view class="boxli"  v-for="(item,index) in ullist" :key="index" >
					<view class="boxli-ls">{{item}}</view>
					<view  class="shanchu" @click.stop="removeli(item,index)" v-if="!disabled">
						<uni-icons type="clear" color="#c1c1c1"  size="20"></uni-icons>
					</view>
				</view>
				<input placeholder="" :cursor="1" :focus="firstFocus" :disabled="disabled" v-model="modelvalues" @input="getinput" @blur="getblur" @focus.stop="getfocus" @confirm="getconfirm" />
			</scroll-view>
		</view>
		<uni-icons type="down" class="huaban1fuben2" :class="{'rotate-180':isShow}"  color="#333333"  size="16"></uni-icons>
		<view class="boxul" :style="{top:inputHeight?'calc(100% + 10rpx)':'90rpx'}" v-show="isShow">
			<scroll-view scroll-y="true" class="uni-select__selector-scroll"  :style="[{height: scrollHeight}]">
				<view class="li" @click.stop="clickLi(item,index)" :class="item.active?'active':''"
					v-for="(item,index) in filterMixinDatacomResData" :key="index">
					{{item.text}}
					<uni-icons type="checkmarkempty" class="icon-tick" color="#1a90fd" v-show="item.active" size="20"></uni-icons>
				</view>
				<view class="li" v-if="!filterMixinDatacomResData.length" style="text-align: center;" >暂无数据</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'selectLt',
		props: {
			scrollHeight: {
				type: String,
				default: '400rpx'
			},
			partlist: {
				type: Array,
				default: function() {
					return [];
				}
			},
			value: {
				type: [String, Number],
				default: ''
			},
			disabled: {
				type: Boolean,
				default: false
			},
			inputHeight: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {
				modelvalues: '',
				ullist: [],
				isShow: false,
				firstFocus:false,
				isben:false,
				// scrolltop:40
			}
		},
		computed: {
			filterMixinDatacomResData() {
				if (this.partlist && this.modelvalues) {
					return this.partlist.filter(obj => obj.text.includes(this.modelvalues))
				} else {
					return this.partlist
				}
			},
		},
		watch: {
			value() {
				console.log(this.value)
				this.initDefVal()
			},
			firstFocus(){
				setTimeout(()=>{
					this.isShow= this.firstFocus
				},50)
			}
		},
		created(){
			this.initDefVal()
		},
		methods: {
			initDefVal() {
				console.log(this.value)
				this.ullist = this.value?this.value.split(","):[],
				setTimeout(()=>{
				    for (var i = 0; i < this.filterMixinDatacomResData.length; i++) {
				    	for (var j = 0; j < this.ullist.length; j++) {
				    		if (this.filterMixinDatacomResData[i].text === this.ullist[j]) {
				    			this.filterMixinDatacomResData[i].active = true
				    		}
				    	}
				    }
				},10)
			},
			getinput(e){
				// console.log(e.detail.value)
			},
			getfocus(e) {
				// this.firstFocus = true;
			},
			elementItself(){
				this.firstFocus = false;
				setTimeout(()=>{
				    this.firstFocus = true;
				},10)
			},
			getblur(e) {
				setTimeout(()=>{
					if(this.isben){
						this.isben=false
					}else{
						// console.log("this.modelvalues",this.modelvalues)
						// 失去焦点后如果this.modelvalues有值说明是直接输入的并且不是回车失去的焦点需要单独赋值
						if(this.modelvalues !== ''){
							if (!this.ullist.includes(this.modelvalues)) {
								let found = this.filterMixinDatacomResData.some(person => person.text === this.modelvalues);
								if (found) {
									for (var i = 0; i < this.filterMixinDatacomResData.length; i++) {
										if (this.filterMixinDatacomResData[i].text === this.modelvalues) {
											this.filterMixinDatacomResData[i].active = true
										}
									}
								}
								this.ullist.push(this.modelvalues)
							}
						}
						this.firstFocus=false
					}
					this.modelvalues = ''    
				},200)
			},
			// 点击下拉列表赋值
			clickLi(item, index) {  
				this.isben=true
				if (item.active) {
					for (let i = 0; i < this.ullist.length; i++) {
						if (this.ullist[i] === item.text) {
							this.ullist.splice(i, 1);
						}
					}
				} else {
					this.ullist.push(item.text)
				}
				this.filterMixinDatacomResData[index].active = !this.filterMixinDatacomResData[index].active
				this.modelvalues = ''
				this.firstFocus = false;
				setTimeout(()=>{
				    this.firstFocus = true;
				},80)
				this.emit()
			},
			// 输入回车赋值
			getconfirm(e) {
				this.isShow = false
				if (e.detail.value) {
					if (!this.ullist.includes(e.detail.value)) {
						let found = this.filterMixinDatacomResData.some(person => person.text === e.detail.value);
						if (found) {
							for (var i = 0; i < this.filterMixinDatacomResData.length; i++) {
								if (this.filterMixinDatacomResData[i].text === e.detail.value) {
									this.filterMixinDatacomResData[i].active = true
								}
							}
						}
						this.ullist.push(e.detail.value)
					}
					this.modelvalues = ''
					this.firstFocus = false;
					this.emit()
				}
			},
			emit() {
				const val = this.ullist.join(',')
					this.$emit('change', val)
					this.$emit('input', val)
				
			},
			removeli(item, index) {
				this.ullist.splice(index, 1);
				this.emit()
				for (let i = 0; i < this.filterMixinDatacomResData.length; i++) {
					if (this.filterMixinDatacomResData[i].text === item) {
						this.filterMixinDatacomResData[i].active = false
					}
				}
				if(this.firstFocus ==true){
					this.isben=true
					this.firstFocus = false;
					setTimeout(()=>{
					    this.firstFocus = true;
					},10)
				}
			},
			turnOffTheMaskLayer() {
				this.isShow = false
			},
		}
	};
</script>
<style scoped lang="scss">
	.box {
		display: flex;
		height: 100%;
		padding-right: 40rpx;
		position: relative;
		font-size: 28rpx;
		flex-wrap: wrap;
		border: 2rpx solid #b2b2b2;
		border-radius: 16rpx;
		flex: 1;
		width: calc(100% - 80rpx);

		.boxulvalue {
			display: flex;
			flex: 1;
			flex-wrap: wrap;
			// overflow-y: scroll;
			// height: 80rpx;
			min-height: 80rpx;
            /deep/ .uni-scroll-view-content{
				display: flex;
				flex: 1;
				flex-wrap: wrap;
			}
			.boxli {
				height: 60rpx;
				/* 高度可以根据需要设置 */
				margin: 10rpx;
				/* 项之间的间距 */
				background-color: #f0f2f5;
				padding: 0 16rpx;
				border-radius: 12rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;

				.shanchu {
					margin-left: 8rpx;
				}
			}

			uni-input {
				flex: 1;
				width: 100%;
				height: 80rpx;
				padding-left: 20rpx;
				max-width: 100%;
			}
		}
		.huaban1fuben2 {
			position: absolute;
			font-size: 28rpx;
			top: calc(50% - 14rpx);
			right: 12rpx;
		}

		.rotate-180 {
			transform: rotate(180deg);
		}

		.boxul {
			position: absolute;
			left: 0;
			// top: 90rpx;
			top:calc(100% + 10rpx);
			width: 100%;
			z-index: 300;
			background: #FFFFFF;
			box-shadow: 0px 6rpx 44rpx 2rpx rgba(0, 0, 0, 0.06);
			border-radius: 20rpx;
			padding: 20rpx;
			box-sizing: border-box;

			.li {
				color: #333;
				position: relative;
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 20rpx;
				font-size: 32rpx;

				.icon-tick {
					position: absolute;
					right: 12rpx;
					color: #1a90fd;
				}
			}

			.active {
				color: #1a90fd;
				border-radius: 8rpx;
				background-color: #f0f2f5;
			}
		}
	}
</style>