<template>
	<view>
		<view class="top-status-bar" id="topStatusBar" :style="[{height: statusBarHeight + 'px'},statusStyle]">
			<view class="slot-view" :style="[{paddingTop: slotMarginTop},statusStyle]">
				<view class="flex justify-between align-center status-content u-px-20" :style="[{width : slotWidth,height:titleHeight}]">
					<view class="u-m-r-20" style="width: 50rpx;">
						<u-icon v-if="iconLeft !=='none'" :name="iconLeft" :size="iconSize" :color="iconColor" @click="clickLeftIcon"></u-icon>
					</view>
					<view class="flex flex-center u-m-r-20">
						<u--image v-if="imageSrc" :showLoading="true" :src="imageSrc" :width="imageWidth" :height="imageHeight"></u--image>
						<view>
							<u--text v-if="title" :text="title" :size="textSize" :color="textColor" :lines="1" style="font-family: <PERSON>, <PERSON>;letter-spacing: 1px;"></u--text>
						</view>
						<view>
							<u--text v-if="title1" :text="title1" :size="textSize" :color="textColor" :lines="1" style="font-family: Source Han Sans, Source Han Sans;letter-spacing: 1px;"></u--text>
						</view>
					</view>
					<view class="u-m-l-20" style="width: 50rpx;">
						<u-icon v-if="iconRight" :name="iconRight" :size="iconSize" :color="iconColor" @click="clickRightIcon"></u-icon>
					</view>
				</view>
				<slot></slot>
			</view>
			<view class="TopTabs" v-if="showTab" style="background-color: #fff;">
				<view class="tab_item" @click="IndexTab(item)" v-for="(item,index) in FiledList" :key="index" :class="orderTypeStatus == item.orderStatus ? 'tabActive' : ''" >
					<text >{{item.name}}</text>
					<view :class="orderTypeStatus == item.orderStatus ? 'line' : ''"></view>
				</view>
			</view>
		</view>
		<!-- <view :style="[{height: statusBarHeight + 'px'},statusStyle]">
		</view> -->
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	export default {
		name: "top-status-bar",
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			},
			isrefresh:{
				type:Boolean,
				default: false
			},
			width:{
				type: String,
				default: ''
			},
			iconLeft: {
				type: String,
				default: 'arrow-left'
			},
			iconRight: {
				type: String,
				default: ''
			},
			imageSrc:{
				type: String,
				default: ''
			},
			imageWidth:{
				type: String | Number,
				default: '55rpx'
			},
			imageHeight:{
				type: String | Number,
				default: '55rpx'
			},
			title:{
				type: String | Number,
				default: ''
			},
			title1:{
				type: String | Number,
				default: ''
			},
			iconSize:{
				type: String | Number,
				default: '50rpx'
			},
			textSize:{
				type: String | Number,
				default: '36rpx'
			},
			textColor:{
				type: String,
				default: '#3D3D3D'
			},
			iconColor:{
				type: String,
				default: '#3D3D3D'
			},
			// 是否展示tabBar
			showTab: {
				type:Boolean,
				default: false
			},
			FiledList: {
				type: Array
			},
			orderStatus: {
				type: String | Number,
				default: ''
			}
		},
		data() {
			return {
				slotObserver: null,
				slotHeight: 0,
				systemInfo:uni.getStorageSync('cache_shop_system_info_key'),
				orderTypeStatus: this.orderStatus
			}
		},
		computed: {
			...mapState({
				topStatusBarData: state => state.init.topStatusBarData
			}),
			statusBarHeight() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.statusBarHeight
				// }
				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight',h)
				return h
			},
			slotMarginTop() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.slotMarginTop
				// }
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.slotWidth
				// }
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				// #endif
				return sw
			},
			titleHeight(){
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.titleHeight
				// }
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			}
		},
		mounted() {
			// if (this.topStatusBarData.isLoad && !this.isrefresh) {
			// 	return
			// }
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
		},
		destroyed() {
			// if (this.topStatusBarData.isLoad) {
			// 	return
			// }
			// 取消节点监听
			this.slotObserver?.disconnect()
			let data = {
					statusBarHeight: this.statusBarHeight,
					slotWidth: this.slotWidth,
					slotMarginTop: this.slotMarginTop,
					titleHeight:this.titleHeight,
					isLoad: true
				}
			this.setGlobalData(data)
		},
		methods: {
			...mapMutations(['setTopStatusBarData']),
			setGlobalData(data) {
				this.setTopStatusBarData(data)
			},
			clickLeftIcon(){
				this.$emit('clickLeftIcon')
			},
			clickRightIcon(){
				this.$emit('clickRightIcon')
			},
			IndexTab(item) {
				this.orderTypeStatus = item.orderStatus
				this.$emit('changeOrderStatus', item.orderStatus)
			},
		}
	}
</script>

<style lang="scss" scoped>
	view{
		box-sizing: border-box;
	}
	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		// #fff3e9
		background-color: #fff;
		z-index: 999;
	}
	.TopTabs{
		margin-top: -5rpx;
		background-color: #fff;
		width: 100%;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		.tab_item{
			font-family: PingFang SC, PingFang SC;
			font-weight: blod;
			font-size: 29rpx;
			color: #333333;
			font-style: normal;
			text-transform: none;
			.line{
				// background: linear-gradient(to right, #2A3A88FF, #3c9cff);
				background:#3c9cff;
				width:  90%;
				height: 10rpx;
				border-radius: 20rpx;
				margin: 0 auto;
				margin-top: -10rpx;
				z-index: -99;
			}
		}
		.tabActive{
			font-family: PingFang SC, PingFang SC;
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
			height: 46rpx;
			line-height: 46rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
	}
	
	
</style>
