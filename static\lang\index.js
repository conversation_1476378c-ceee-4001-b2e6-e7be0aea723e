const locales = {
	zh: require('./chinese.js'),
	en: require('./English.js'),
	kh: require('./Khmer.js')
};
 
let currentLocale = '';
 
if (!uni.getStorageSync('locale')) {
	const systemLanguage = uni.getSystemInfoSync().language; // 首次进入使用系统默认语言，也可以根据自己项目需求改写此处的逻辑
	currentLocale = systemLanguage.startsWith('zh') ? 'zh' : 'kh';
} else {
	currentLocale = uni.getStorageSync('locale'); // 用户切换过语言，使用用户上次切换的语言
}
 
const i18n = {
	t(key) {
		return locales[currentLocale][key] || key;
	},
	setLocale(locale) {
		if (locales[locale]) {
			currentLocale = locale;
			// 切换语言后存储在本地，下次进入页面使用用户切换过的语言
			uni.setStorageSync('locale', currentLocale);
		} else {
			console.warn(`Locale ${locale} not found`);
		}
	},
};
export default i18n;