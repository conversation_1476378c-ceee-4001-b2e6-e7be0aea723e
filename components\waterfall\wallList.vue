<template>
	<view class="waterfall">
		<view class="loadingLayout" v-if="!RecommendList.length && !noData">
			 <uni-load-more status="'loading'"></uni-load-more>
		</view>
		<view class="waterfallCon" v-if="RecommendList.length > 0">
			<view class="waterfallitem" v-for="(item,index) in RecommendList" :key="index" @tap="Estimate(item, index)">
				<!-- background: #2A3A88FF; -->
				<view class="infostatus" :style="{ backgroundColor: item.spStatus == 3 ? 'red' : (item.spStatus == 2 ? '#31d337' : '#2A3A88FF') }">
					<text>{{item.spStatusName || item.rwStatusName}}</text>
				</view>
				<view class="toptitle">
					<text>{{item.companyName}}({{item.estimateBh}})</text>
				</view>
				<view class="centercontent">
					<view class="left">
						<image :src="item.infoImageUrl" mode="" v-if="item.infoImageUrl"></image>
						<image :src="itemimg" mode="" v-else></image>
					</view>
					<view class="right">
						<view class="topname">
							<u--text :lines="2" :text="item.buildingInfo"
							  size="26rpx" color="#000"></u--text>
						</view>
						<view style="display: flex;align-items: center;">
							<view class="centertype" v-for="(ryitem, index) in item.ryList" :key="index">
								<!-- <text style="margin-right: 20rpx;">{{ryitem}}</text> -->
								<text style="margin-right: 20rpx;">{{ryitem}}</text>
							</view>
						</view>
						<view class="bottom">
							<view class="blue">
								<image src="https://file.36sm.cn/xtjyjt/images/common/naoz.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
								<text>{{item.bqList[0]}}</text>
							</view>
							<view class="red">
								<image src="https://file.36sm.cn/xtjyjt/images/common/rli.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
								<text>{{item.bqList[1]}}</text>
							</view>
							<view class="blue">
								<text>{{item.bqList[3]}}</text>
							</view>
							<view class="red">
								<text>{{item.bqList[2]}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="bottmcontent" v-if="item.spStatus == 3">
					<text>{{item.spRemarks}}</text>
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
			onLoad,
			onShow,
			onReachBottom
		} from "@dcloudio/uni-app"
		
	export default {
		data() {
			return {
				noData: false,
				bottomList: [
					'团前会议',
					'资料打印',
					'开始出团',
					'客户签到',
					'出团结束',
					'客户评价',
					'物料归还',
					'提交结算',
					'项目复盘'
				],
				itemimg: 'https://file.36sm.cn/mttttxxs/2024/12/31/ce545e481f4546be859b5b38f9eeea65.png',
				deflautWidth: 20
			}
		},
		components: {
		},
		mounted() {
		},
		props: {
			RecommendList: {
				type: Array
			},
			CanClick: {
				type:Boolean,
				default: true
			},
			ishome: {
				type:Boolean,
				default: false
			},
			platformType: {
				type: String || Number,
				default: '0'
			}
		},
		methods: {
			calculateOpacity(index) {
			    // 计算每个元素的透明度，越靠右越透明
				const totalItems = this.bottomList.length;
				const fadeFactor = 0.5; // 设置每个项透明度变化的最大值
				const opacity = 1 - (index / totalItems) * fadeFactor;
				return opacity;
			},
			Estimate(item, index) {
				// this.$emit('Estimatedetail', {
				// 	index: index,
				// 	...item
				// })
				this.$emit('Estimatedetail', item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.waterfall{
		.waterfallCon{
			.waterfallitem{
				background-color: #fff;
				border-radius: 16rpx;
				margin-bottom: 15rpx;
				padding: 15rpx 30rpx;
				position: relative;
				.infostatus{
					position: absolute;
					// width: 100rpx;
					padding: 5rpx 10rpx;
					height: 48rpx;
					line-height: 48rpx;
					text-align: center;
					border-radius: 0rpx 16rpx 0rpx 16rpx;
					color: #fff;
					right: 0;
					top: 0;
					text{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #FFFFFF;
						font-style: normal;
						text-transform: none;
					}
					
				}
				.toptitle{}
				.centercontent{
					margin: 20rpx 0 10rpx 0;
					display: flex;
					align-items: center;
					
					.left{
						width: 256rpx;
						height: 180rpx;
						image{
							width: 100%;
							height: 100%;
						}
					}
					.right{
						margin-left: 30rpx;
						height: 180rpx;
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.topname{}
						.centertype{
							text{
								width: 72rpx;
								height: 32rpx;
								background: rgba(51,51,51,0.1);
								border-radius: 8rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 20rpx;
								color: #999999;
								line-height: 32rpx;
								text-align: center;
								font-style: normal;
								text-transform: none;
								padding: 5rpx;
							}
						}
						.bottom{
							display: flex;
							justify-content: space-between;
							align-items: center;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 22rpx;
							font-style: normal;
							text-transform: none;
							.blue{
								display: flex;
								align-items: center;
								background: #EAEBF3;
								border-radius: 8rpx;
								padding: 5rpx 10rpx;
								color: #2A3A88;
								image{
									margin-right: 5rpx;
								}
							}
							.red{
								display: flex;
								align-items: center;
								background: rgba(225, 37, 36, 0.2);
								border-radius: 8rpx;
								padding: 5rpx 10rpx;
								color: #E12524;
								image{
									margin-right: 5rpx;
								}
							}
						}
					}
				}
				.bottmcontent{
					margin-top: 15rpx;
					color: #E12524;
					  font-size: 22rpx;
					  flex-wrap: wrap;
					  word-wrap: break-word;
					  overflow-wrap: break-word;
					  white-space: normal;
				}
				.progress{
					margin: 10rpx 0;
				}
			}
		}
	}
</style>