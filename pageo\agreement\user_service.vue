<!-- <template>
	<view class="page">
		<view class="title">
			用户服务协议
		</view>
		<view class="content">
			&nbsp;  您在申请注册流程中点击同意本协议之前，应当认真阅读本协议。请您务必审慎阅读、充分理解各条款内容，特别是免除或者限制责任的条款、法律适用和争议解决条款。当您按照注册页面提示填写信息、阅读并同意本协议且完成全部注册程序后，即表示您已充分阅读、理解并接受本协议的全部内容，成为见安修脚商城平台的用户。阅读本协议的过程中，如果您不同意本协议或其中任何条款约定，您应立即停止注册程序。见安修脚商城平台会在必要时修改本协议及/或各类规则，并将在见安修脚商城平台相应页面提前公告。具体而言，见安修脚商城将通过适当的方式在平台上对本协议的修改事宜进行公告或通知，此类通知于公布或发送之日即视为已送达您。 见安修脚商城承诺，对于本协议及/或各类规则的修改，见安修脚商城平台应当于变更后的文本生效之日前至少七日进行公告或通知。如您不同意相关变更，应当立即停止访问见安修脚商城平台或使用见安修脚商城平台的产品/服务。若您在本协议及/或各类规则变更生效之日后继续使用见安修脚商城平台的产品/服务的，即表示您接受修订后的文本您完全理解并同意，本服务涉及到互联网及移动通讯等服务可能会受到各个环节不稳定因素的影响。因此任何因不可抗力、计算机病毒或黑客攻击、系统不稳定、用户所在位置、用户关机、GSM网络、互联网络、通信线路等其他见安修脚商城平台无法预测或控制的原因，造成服务中断、取消或终止的风险，由此给您带来的损失，见安修脚商城不承担赔偿责任。 见安修脚商城平台需要定期或不定期地对提供网络服务的平台或相关的设备进行检修或者维护，如因此类情况而造成网络服务 (包括收费网络服务) 在合理时间内的中断，见安修脚商城无需为此承担任何责任，但见安修脚商城平台应尽可能事先进行通知。 您完全理解并同意，除本服务协议另有规定外，鉴于网络服务的特殊性，见安修脚商城平台有可能变更、中断或终止部分或全部的网络服务。除了您已经通过见安修脚商城平台订购或申请的产品/服务并已完成支付并已获得确认订购或申请的情况之外，见安修脚商城无需为变更、中断或终止服务承担任何责任，但见安修脚商城应尽可能事先进行通知，并尽可能给您预留时间以便外理相关权益，用户行为规范，您在使用见安修脚商城平台产品/服务时，必须遵守中华人民共和国相关法律法规的规定，您承诺将不会利用见安修脚商城平台的产品/服务进行任何违法或不正当的活动.
		</view>
	</view>
</template>

<script>
</script>

<style scoped lang="scss">
	.page{
		.title{
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
		}
		.content{
			padding: 0 10rpx;
			letter-spacing: 3rpx;
			font-family: 'Courier New', Courier, monospace;
			white-space: pre-line; 
			  word-wrap: break-word; 
			  word-break: break-all;
			  line-height: 3;
		}
	}
</style> -->


<template>
	<view>
		<!-- <newNavBar :title="$t('search.xxxq')" width="100vw" @clickLeftIcon="goBack"></newNavBar> -->
		<newNavBar title="服务协议" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<view v-if="visible">
			<view class="content">
				<u-parse :content="contentinfo"></u-parse>
			</view>
			<!-- <view class="messageInfo">
				<view class="messagedetail">
					<view class="usename">
						<text>{{messageInfo.msName}}</text>
					</view>
					<view class="Mescenter">
						<view class="labelTitleList" v-for="(item, i) in messageInfo.labelTitleList" :key="i">
							<view class="item">
								{{item}}
							</view>
						</view>
						
						<view class="time">
							{{messageInfo.createTime}}
						</view>
					</view>
					
					<view class="content">
						<u-parse :content="messageInfo.basicInfo"></u-parse>
					</view>
				</view>
			</view> -->
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
import store from '@/store'
import api from '@/common/request/index.js'
import componentBottomLine from '../../components/bottom-line/bottom-line';
import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"

	export default{
		data() {
			return {
				visible: false,
				contentinfo: ""
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad(params) {
			console.log(params)
			this.getAboutUS()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			}
		},
		onShow() {
			this.getAboutUS()
		},
		methods:{
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAboutUS() {
				api({
					url: `bs/zc/fw/info?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
				    if (res.code === 200) {
						this.contentinfo = res.data.data
						// console.log(this.contentinfo, '关于我们')
						this.visible = true
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.content{
		padding: 0;
	}
</style>