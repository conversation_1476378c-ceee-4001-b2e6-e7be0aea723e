<template>
	<view class="page">
		<newNavBar :title="$t('search.zf')" width="100vw" @clickLeftIcon="to_order(null , -1)"></newNavBar>
		<view v-if="visible">
			<view class="topOrder">
				<image src="https://file.36sm.cn/xtjyjt/images/common/duig.png" mode="" style="width: 60rpx;height: 60rpx;"></image>
				<!-- <text>账单已生成，请支付</text> -->
				<text>{{$t('search.zdysc')}}</text>
			</view>
			<view class="centerOrder">
				<view class="Ordertitle">
					<!-- <text>费用明细</text> -->
					<text>{{$t('search.fymx')}}</text>
				</view>
				<view class="totalproprice">
					<view class="spzj">
						<view class="title">
							<!-- 商品总价 -->
							<text>{{$t('search.spzj')}}</text>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.spZjTrade}}
							</view>
							<view class="base">
								({{ConfirmOInfo.baseCoinName}}  {{ConfirmOInfo.spZjBase}})
							</view>
						</view>
					</view>
					<view class="cghj">
						<view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.cghj'))">
							<!-- <text>采购合计</text> -->
							<text>{{$t('search.cghj')}}</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.cgHjfTrade}}
							</view>
							<view class="base">
								({{ConfirmOInfo.baseCoinName}}  {{ConfirmOInfo.cgHjfBase}})
							</view>
						</view>
					</view>
					<view class="line"></view>
					<view class="dgfwf">
						<view class="title">
							<!-- 代购服务费 -->
							<text>{{$t('search.dgfwf')}}</text>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.dgfTrade}}
							</view>
						</view>
					</view>
					<view class="yhf">
						<view class="title" style="display: flex;align-items: center;" @tap="showinfo($t('search.yhf'))">
							<!-- <text>验货费</text> -->
							<text>{{$t('search.yhf')}}</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.yhfTrade}}
							</view>
						</view>
					</view>
					
					<view class="jsbz">
						<view class="title">
							<!-- 计算币种 -->
							<text>{{$t('search.jsbz')}}</text>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.tradeCoinName}}
							</view>
						</view>
					</view>
					<view class="huil">
						<view class="title">
							<!-- 汇率 -->
							<text>{{$t('search.hl')}}</text>
						</view>
						<view class="price">
							<view class="usd">
								{{ConfirmOInfo.exchangeTate}}
							</view>
						</view>
					</view>
					<view class="jgzj">
						<!-- <text>总计</text> -->
						<text>{{$t('search.zj')}}</text>
						<view class="value">
							{{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.syfyTrade}}
						</view>
					</view>
				</view>
			</view>
			
			<view class="kjzfbottom">
				<view class="leftinfo">
					<view class="imgicon">
						<image src="https://file.36sm.cn/xtjyjt/images/common/qianbao.png" mode="" style="width: 65rpx;height: 65rpx;"></image>
					</view>
					<view class="rightdetail">
						<!-- <text>快捷支付</text> -->
						<text>{{$t('search.kjzf')}}</text>
						<!-- zhye -->
						<text class="balance">{{$t('search.zhye')}}: {{my_money.my_balance}}</text>
						<text class="dzfprice">{{$t('search.dzf')}}: {{ConfirmOInfo.tradeCoinName}}  {{ConfirmOInfo.syfyTrade}}</text>
					</view>
				</view>
				<view class="rightcheckbox">
					<checkbox-group @change="changeSelect()">
						<label>
							<checkbox class="selected" color="#fe0035" :checked="ischecked" style="transform:scale(0.7)" shape="circle"/>
						</label>
					</checkbox-group>
				</view>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>、
		
		
		<view class="buybtn">
			<u-button :text="btntext" color="#fe0035" :loading="isloading" :loadingText="$t('search.zfz')" @tap="submitbuy" :disabled="!ischecked"></u-button>
		</view>
		
		
		<!-- 充值弹窗 -->
		<uni-popup ref="rechargePopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							<!-- 充值 -->
							<text>{{$t('accountUser.cz')}}</text>
						</view>
						<view class="topClose" @click="closerechargePopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="line"></view>
					<view class="content">
						<view class="firststep">
							<view class="steptitle">
								<view class="step">1</view>
								<view class="stepname">
									<!-- 请汇款至以下收款账户 -->
									<text>{{$t('search.qhkz')}}</text>
									</view>
							</view>
							<view class="step1price">
								<view>
									<!-- 充值金额: -->
									<text>{{$t('accountUser.czje')}}</text>
								</view>
								<view class="pricevalue">
									<text>{{ConfirmOInfo.tradeCoinName}} {{ConfirmOInfo.syfyTrade}}</text>
								</view>
							</view>
							<view class="stepimg">
								<u--image :showLoading="true" :src="czqrImg" width="200rpx" height="220rpx" @tap="onTapimage(czqrImg)"></u--image>
								<view class="right">
									<view class="topC">
										<view class="title">
											<!-- 1、保存二维码至本地相册 -->
											<text>{{$t('search.bcewmz')}}</text>
											</view>
										<view class="title">
											<!-- 2、扫一扫转账 -->
											<text>{{$t('search.syszz')}}</text>
										</view>
									</view>
									<view class="bottombtn">
										<text>
											<!-- 保存二维码 -->
											<text>{{$t('search.bcewm')}}</text>
										</text>
									</view>
								</view>
							</view>
						</view>
						<view class="secondstep">
							<view class="steptitle">
								<text class="step">2</text>
								<text class="stepname">
									<!-- 提交汇款凭证 -->
									<text>{{$t('search.tjhkpz')}}</text>
									</text>
							</view>
							<view class="step2content">
								<view class="uploadicon" v-if="transactionUrl == ''">
									<image style="width: 100rpx;height: 100rpx;"@tap="upload_img"
									 src="https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png"></image>
								</view>
								<image v-if="transactionUrl !== ''" style="width: 150rpx;height: 150rpx;"@tap="upload_img"
								 :src="transactionUrl"></image>
							</view>
						</view>
					</view>
					
					<view class="czBtn">
						<u-button :text="$t('accountUser.cz')" color="#fe0035" :loading="czloading" :disabled="czloading" :loadingText="$t('accountUser.czz')" @tap="submitcz"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		<uni-popup ref="PasswordPopup" type="bottom" :is-mask-click="false">
			<view class="InfoPopupPass" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title">
							<!-- 支付密码 -->
							{{$t('search.zfmm')}}
						</view>
						<view class="topClose" @click="closePasswordPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="line"></view>
					<view class="content">
						<u-code-input v-model="password" :maxlength="6" dot></u-code-input>
					</view>
					<view class="zfBtn">
						<u-button :text="$t('accountUser.fk')" color="#fe0035" :loading="czloading" :disabled="czloading" :loadingText="$t('accountUser.fkz')" @tap="submitzf"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<uni-popup ref="Popupinfo" type="center" :is-mask-click="false">
		   	<view class="Popupinfo" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<text>{{infotitle}}</text>
		   			</view>
					<view class="content">
						<u-parse :content="infodetail"></u-parse>
					</view>
					<view class="bottombtn">
						<u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.wzdl')"  @tap="closeinfo"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
	</view>
</template>

<script>
	const app = getApp();
	import componentCopyright from '../../components/copyright/copyright';
	import api from '@/common/request/index.js'
	
	export default {
		data() {
			return {
				visible: false,
				ConfirmOInfo: {},
				ischecked: false,
				my_money: {
					// 余额
					my_balance: 0,
					balancename: '',
					// 积分
					my_intergal: 0,
					intergalname: '',
					// 贡献值
					my_contribute: 0,
					contributename: '',
					// 业绩
					my_achievement: 0,
					achievementname: ''
				},
				isloading: false,
				btntext: this.$t('search.ljzf'),
				czqrImg: "",
				coinName: "",
				// 汇款凭证图片
				transactionUrl: '',
				czloading: false,
				password: '',
				infotitle: "",
				infodetail: ""
			}
		},
		components: {
			componentCopyright
		},
		onLoad(params) {
			console.log(params, 'params')
			this.ConfirmOInfo = JSON.parse(decodeURIComponent(params.ConfirmOInfo))
			console.log(this.ConfirmOInfo, 'ConfirmOInfo')
		},
		onShow() {
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
			this.get_money()
			this.getCZqrcode()
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			to_order(item=null,index=-1) {
				console.log('返回订单列表')
				// let data = item
				// if(data === null){
				// 	data = {
				// 		name: '全部',
				// 		orderStatus: 100
				// 	}
				// }
				// this.$goTo('/pageu/user-order/user-order',{item:JSON.stringify(data),index:index + 1})
				
				const data = {
					name: '全部',
					orderStatus: 100
				}
				this.$goTo('/pageu/user-order/user-order',{item:JSON.stringify(data),index:index + 1})
				
			},
			showinfo(title) {
				if(title == this.$t('search.cghj')) {
					this.infodetail = this.ConfirmOInfo.cgHjfRemarks
				}
				if(title == this.$t('search.yhf')) {
					this.infodetail = this.ConfirmOInfo.yhfRemarks
				}
				this.infotitle = title
				this.$refs.Popupinfo.open()
			},
			closeinfo() {
				this.$refs.Popupinfo.close()
			},
			onTapimage(src) {
				console.log(src, 'src')
				 uni.previewImage({
				        urls: [src],
						current: src
				      });
			
			},
			changeSelect() {
				this.ischecked = !this.ischecked
			},
			// 获取当前账户余额
			get_money() {
				api({
				    url: 'mb/wallet/get/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						this.$data.visible = true
						let data = res.data.data
						console.log(data, '钱包信息')
						data.map(item => {
							if (item.coinId == 2) {
								this.my_money.my_intergal = item.balance
								this.my_money.intergalname = item.coinName
							} else if (item.coinId == 1) {
								this.my_money.my_balance = item.balance
								this.my_money.balancename = item.coinName
							} else if (item.coinId == 3) {
								this.my_money.my_contribute = item.balance
								this.my_money.contributename = item.coinName
							} else if (item.coinId == 4) {
								this.my_money.my_achievement = item.balance
								this.my_money.achievementname = item.coinName
							}
						})
						console.log(this.my_money, '钱包信息')
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: 'none'
					})
				})
			},
			upload_img() {
				console.log('选择汇款凭证')
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						// console.log(res, 'res')
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							// url: 'http://************/khrWeb/oss/hteditor/upload',
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								this.transactionUrl = Data.data.data.fileUrl
								console.log(this.transactionUrl, '凭证图片')
								if (Data.code == 200) {
									uni.showToast({
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			moveHandle() {
				// 禁止popup弹窗 遮罩层下的页面滚动
			},
			closerechargePopup() {
				this.$refs.rechargePopup.close()
			},
			closePasswordPopup() {
				this.password = ''
				this.$refs.PasswordPopup.close()
			},
			submitbuy() {
				if(!this.ischecked) {
					return
				}
				// this.isloading = true
				// setTimeout(() => {
				// 	this.isloading = false
				// }, 1500)
				// 对比账户余额与 所需费用综合
				if(this.my_money.my_balance - this.ConfirmOInfo.syfyTrade >= 0) {
					// 输入资金密码
					this.$refs.PasswordPopup.open()
				} else {
					// 余额不足 充值弹窗
					this.$refs.rechargePopup.open()
				}
			},
			submitzf() {
				if(this.czloading) {
					return
				}
				if(this.password == '') {
					uni.showToast({
						title: this.$t('message.qsrzjmm'),
						icon: 'none'
					})
					return
				}
				
				console.log(this.ConfirmOInfo, 'ConfirmOInfo', this.ConfirmOInfo.orderInfoId)
				let orderBbList = []
				orderBbList.push(this.ConfirmOInfo.orderInfoId)
				const params = {
					orderBbsIdList: orderBbList,
					zjMm: this.password,
					zjTs: this.ConfirmOInfo.syfyTrade
				}
				console.log(params, '提交参数')
				this.czloading = true
				setTimeout(() => {
					this.czloading = false
				}, 2500)

				api({
					url: `car/lj/zf/order/play/info?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res=>{
					if(res.code == 200){
						uni.showToast({
							title: this.$t('message.zfcg'),
							icon: 'none'
						})
						this.password = ''
						this.$refs.PasswordPopup.close()
						// 支付成功跳转到 订单列表
						// uni.redirectTo({
						// 	url: '/pages/branded-space/index'
						// })
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/branded-space/index' // 目标页面路径
							})
						}, 500)
					}
				})
				
			},
			submitcz() {
				
				if(this.transactionUrl == '') {
					uni.showToast({
						// title: '请上传充值凭证',
						title: this.$t('message.qscczpz'),
						icon: 'none'
					})
					return
				}
				
				if(this.czloading) {
					return
				}
				
				this.czloading = true
				setTimeout(() => {
					this.czloading = false
				}, 2500)
				
				const formData = {
					amount:  Number(this.ConfirmOInfo.syfyTrade).toFixed(2),
					transactionUrl: this.transactionUrl,
					remarks: '充值'
				}
				api({
					url: `mb/cz/save`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						this.$refs.rechargePopup.close()
						uni.showToast({
							// title: '充值成功，待审核',
							title: this.$t('message.czcgdsh'),
							icon:'none'
						})
					}
				})
			},
			// 获取会员充值码信息
			getCZqrcode() {
				api({
				    url: `mb/cz/get/code/info?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '二维码图片')
						this.czqrImg = res.data.data.codeImg
						this.coinName = res.data.data.coinName
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},
		}
	}
	
</script>

<style scoped lang="scss">
	.topOrder{
		height: 200rpx;
		width: 100%;
		background-color: #fff;
		margin: 20rpx 0;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		text{
			margin-top: 10rpx;
			color: #181919;
		}
	}
	.centerOrder{
		width: 100%;
		min-height: 200rpx;
		background-color: #fff;
		margin-bottom: 20rpx;
		padding: 20rpx 0;
		
		.line{
			width: 100%;
			height: 2rpx;
			background-color: #EEEEEE;
		}
		.Ordertitle{
			padding: 0 20rpx;
			color: #000;
			font-size: 30rpx;
			font-weight: 500;
		}
		.totalproprice{
			padding: 0 20rpx;
			.title{
				color: #989898;
				min-width: 200rpx;
				font-size: 28rpx;
			}
			.price{
				.usd{
					color: #000;
					font-weight: 500;
					font-size: 24rpx;
				}
				.base{
					color: #989898;
					font-size: 22rpx;
				}
			}
			.spzj{
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 70rpx;
				margin-top: 15rpx;
				.price{
					display: flex;
				}
			}
			.cghj{
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 70rpx;
				margin-bottom: 15rpx;
				.price{
					display: flex;
				}
			}
			.dgfwf{
				margin-top: 15rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 70rpx;
			}
			.yhf{
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 70rpx;
			}
			.jsbz{
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 70rpx;
			}
			 .huil{
				 display: flex;
				 align-items: center;
				 justify-content: space-between;
				 height: 70rpx;
			 }
			 .jgzj{
				 height: 90rpx;
				 display: flex;
				 align-items: center;
				 justify-content: flex-end;
				 .value{
					 margin-left: 10rpx;
					 font-weight: 600;
					 color: #fe0035;
				 }
			 }
		}
	}
	
	.kjzfbottom{
		width: 100%;
		background-color: #fff;
		height: 150rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		// padding: 0 20rpx;
		.leftinfo{
			margin-left: 20rpx;
			display: flex;
			align-items: center;
			height: 100%;;
			.rightdetail{
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-left: 20rpx;
				font-size: 30rpx;
				.balance{
					color: #989898;
					font-size: 24rpx;
					margin: 8rpx 0;
				}
				.dzfprice{
					color: #fe0035;
					font-size: 24rpx;
				}
				
			}
		}
		.rightcheckbox{
		}
	}
	
	.buybtn{
		width: 90%;
		position: fixed;
		bottom: 50rpx;
		left: 50%;
		transform: translateX(-50%);
	}
	.InfoPopup{
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		min-height: 1100rpx;
		max-height: 1400rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			// padding-top: 30rpx;
			.topCon{
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 100rpx;
				.title{
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 32rpx;
					color: #000000;
					line-height: 38rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
				.topClose{
					image{
						width: 40rpx;
						height: 40rpx;
						}
					}
			}
			
		   .line{
				width: 100%;
				height: 2rpx;
				background-color: #EEEEEE;
			}
			
			.content{
				.firststep{
					.steptitle{
						height: 80rpx;
						line-height: 80rpx;
						display: flex;
						align-items: center;
						.step{
							height: 35rpx;
							width: 35rpx;
							line-height: 35rpx;
							text-align: center;
							color: #fff;
							background-color: #000;
							border-radius: 50%;
						}
						.stepname{
							margin-left: 10rpx;
							color: #000;
							font-size: 30rpx;
							font-weight: 500;
						}
					}
					.step1price{
						padding: 0 45rpx;
						display: flex;
						height: 60rpx;
						align-items: center;
						color: #000;
						.pricevalue{
							margin-left: 10rpx;
							color: #fe0035;
						}
					}
					.stepimg{
						height: 300rpx;
						width: 80%;
						margin: 10rpx 0 0 45rpx;
						background-color: #f3f3f3;
						border-radius: 15rpx;
						display: flex;
						align-items: center;
						.right{
							margin-left: 30rpx;
							height: 220rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.topC{
								color: #9c9c9c;
								font-size: 28rpx;
							}
							.bottombtn{
								width: 280rpx;
								height: 60rpx;
								line-height: 60rpx;
								text-align: center;
								border-radius: 30rpx;
								border: 1rpx solid #fe0035;
								color: #fe0035;
							}
						}
					}
				}
				.secondstep{
					.steptitle{
						height: 80rpx;
						line-height: 80rpx;
						display: flex;
						align-items: center;
						.step{
							height: 35rpx;
							width: 35rpx;
							line-height: 35rpx;
							text-align: center;
							color: #fff;
							background-color: #000;
							border-radius: 50%;
						}
						.stepname{
							margin-left: 10rpx;
							color: #000;
							font-size: 30rpx;
							font-weight: 500;
						}
					}
					
					.step2content{
						margin-left: 45rpx;
						.uploadicon{
							border: 1rpx dashed #e4e4e4;
							width: 150rpx;
							height: 150rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
				}
			}
			.czBtn{
				margin: 0 auto;
				margin-top: 50rpx;
				width: 90%;
				// left: 50%;
				// transform: translateX(-50%);
			}
		 }
	}
	
	.InfoPopupPass{
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		min-height: 500rpx;
		max-height: 1400rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				    display: flex;
					justify-content: space-between;
					align-items: center;
					height: 100rpx;
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 32rpx;
						color: #000000;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
					.topClose{
						image{
							width: 40rpx;
							height: 40rpx;
							}
						}
				}
						
			.line{
				    width: 100%;
					height: 2rpx;
					background-color: #EEEEEE;
				}
			.content{
				width: 100%;
				margin-top: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				}
			.zfBtn{
				margin: 0 auto;
				margin-top: 50rpx;
				width: 50%;
			}
		}
	}
	
	
	.Popupinfo{
		width: 650rpx;
		min-height: 400rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				height: 100rpx;
				line-height: 100rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
			.content{
				min-height: 150rpx;
			}
			
			.bottombtn{
				display: flex;
				margin: 30rpx 0;
				
			}
		}
	}
	
</style>
