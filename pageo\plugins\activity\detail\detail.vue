<template>
    <view :class="theme_view">
        <view v-if="(data || null) != null">
            <view class="padding-horizontal-main padding-top-main">
                <!-- 基础信息 -->
                <view class="base-container tc pr padding-main border-radius-main bg-main oh spacing-mb" :style="'background-color:' + data.color + ' !important;background-image:url(' + (data.banner || data.cover) + ')'">
                    <view class="text cr-white pa bs-bb text-size wh-auto ht-auto">{{ data.describe }}</view>
                </view>

                <!-- 关键字 -->
                <view v-if="data.keywords_arr.length > 0" class="word-content scroll-view-horizontal margin-bottom-sm">
                    <scroll-view scroll-x>
                        <block v-for="(kv, ki) in data.keywords_arr" :key="ki">
                            <navigator :url="'/pages/goods-search/goods-search?keywords=' + kv" hover-class="none" class="word-icon dis-inline-block bg-main-light text-size-xs cr-main round padding-top-xs padding-bottom-xs padding-left padding-right">{{ kv }}</navigator>
                        </block>
                    </scroll-view>
                </view>

                <!-- 推荐商品 -->
                <view v-if="(data.goods_list || null) != null && data.goods_list.length > 0">
                    <view class="spacing-nav-title flex-row align-c jc-sb text-size-xs">
                        <view class="title-left">
                            <text class="text-wrapper title-left-border">活动商品</text>
                            <text class="vice-name margin-left-lg cr-grey">{{ data.vice_title }}</text>
                        </view>
                        <navigator url="'/pages/plugins/activity/index/index" hover-class="none" class="arrow-right padding-right cr-grey">更多活动</navigator>
                    </view>
                    <component-goods-list :propData="{ style_type: 1, goods_list: data.goods_list }" :propCurrencySymbol="currency_symbol"></component-goods-list>
                </view>
                <view v-else>
                    <!-- 提示信息 -->
                    <component-no-data propStatus="0" propMsg="没有相关商品"></component-no-data>
                </view>
            </view>

            <!-- 结尾 -->
            <component-bottom-line :propStatus="data_bottom_line_status"></component-bottom-line>
        </view>
        <view v-else>
            <!-- 提示信息 -->
            <component-no-data :propStatus="data_list_loding_status" :propMsg="data_list_loding_msg"></component-no-data>
        </view>
    </view>
</template>
<script>
    const app = getApp();
    import componentNoData from '../../../../components/no-data/no-data';
    import componentBottomLine from '../../../../components/bottom-line/bottom-line';
    import componentGoodsList from '../../../../components/goods-list/goods-list';

    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                data_bottom_line_status: false,
                data_list_loding_status: 1,
                data_list_loding_msg: '',
                currency_symbol: app.globalData.data.currency_symbol,
                params: null,
                user: null,
                data_base: null,
                data: null,
                // 自定义分享信息
                share_info: {},
            };
        },

        components: {
            componentNoData,
            componentBottomLine,
            componentGoodsList,
        },
        props: {},

        onLoad(params) {
            //params['id'] = 1;
            this.setData({
                params: params,
            });
        },

        onShow() {
            // 初始化配置
            this.init_config();

            // 获取数据
            this.get_data();
        },

        // 下拉刷新
        onPullDownRefresh() {
            this.get_data();
        },

        methods: {
            // 初始化配置
            init_config(status) {
                if ((status || false) == true) {
                    this.setData({
                        currency_symbol: app.globalData.get_config('currency_symbol'),
                    });
                } else {
                    app.globalData.is_config(this, 'init_config');
                }
            },

            // 获取数据
            get_data() {
				let _this = this;
				setTimeout(function() {
					uni.stopPullDownRefresh();
					var data = {
						"base": {
							"application_name": "活动",
							"home_data_list_number": "",
							"goods_detail_icon": "",
							"seo_title": "",
							"seo_keywords": "",
							"seo_desc": "",
							"is_actas_price_original": "0",
							"is_home_auto_play": "1",
							"system_type": "default"
						},
						"data": {
							"id": "3",
							"activity_category_id": "2",
							"title": "618年中活动",
							"vice_title": "个性推荐",
							"color": "#33CCFF",
							"describe": "厂家直销，价格优惠",
							"cover": "https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/plugins_activity\/2023\/11\/09\/1699505222324959.png",
							"banner": "https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/plugins_activity\/2023\/08\/15\/1692101047167860.png",
							"keywords": "华为,苹果,戴尔",
							"goods_count": "11",
							"access_count": "126",
							"is_enable": "1",
							"home_data_location": "0",
							"style_type": "1",
							"is_home": "1",
							"is_goods_detail": "0",
							"time_start": "",
							"time_end": "",
							"seo_title": "",
							"seo_keywords": "",
							"seo_desc": "",
							"sort": "0",
							"add_time": "2022-05-15 14:31:08",
							"upd_time": "2023-11-09 12:47:11",
							"url": "\/pages\/plugins\/activity\/detail\/detail?id=3",
							"activity_category_name": "最新",
							"goods_list": [
							  {
								"activity_id": "3",
								"goods_id": "6",
								"discount_rate": "0.00",
								"dec_price": "0.00",
								"is_recommend": "1",
								"id": "6",
								"store_id": "0",
								"store_user_id": "0",
								"store_goods_id": "0",
								"store_goods_type": "0",
								"store_goods_deliver_type": "0",
								"store_plugins_value": "",
								"store_plugins_author": "",
								"store_is_check_steal": "1",
								"realstore_id": "0",
								"realstore_goods_id": "0",
								"shop_id": "1",
								"shop_user_id": "1",
								"shop_category_id": "0",
								"shop_settle_price": "0.00",
								"shop_settle_rate": "0.00",
								"brand_id": "10",
								"site_type": "-1",
								"title": "vivo iQOO Neo8 12GB+256GB 冲浪 第一代骁龙8+ 自研芯片V1+ 120W超快闪充 144Hz高刷 5G游戏电竞性能手机",
								"title_color": "",
								"simple_desc": "vivo iQOO Neo8 12GB+256GB 冲浪 第一代骁龙8+ 自研芯片V1+ 120W超快闪充 144Hz高刷 5G游戏电竞性能手机",
								"model": "vivo iQOO Neo",
								"place_origin": "19",
								"inventory": "876",
								"inventory_unit": "台",
								"images": "https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691824869950654.png",
								"original_price": "2700.00",
								"min_original_price": "2700.00",
								"max_original_price": "2700.00",
								"price": "1750.00",
								"min_price": "1750.00",
								"max_price": "1750.00",
								"give_integral": "65",
								"buy_min_number": "1",
								"buy_max_number": "0",
								"is_deduction_inventory": "1",
								"is_shelves": "1",
								"content_web": "<p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826204910905.png\" title=\"1691826204910905.png\" alt=\"截屏2023-08-12 15.34.50.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826211530116.png\" title=\"1691826211530116.png\" alt=\"截屏2023-08-12 15.35.03.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826216620275.png\" title=\"1691826216620275.png\" alt=\"截屏2023-08-12 15.35.17.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826222270713.png\" title=\"1691826222270713.png\" alt=\"截屏2023-08-12 15.35.29.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826226253803.png\" title=\"1691826226253803.png\" alt=\"截屏2023-08-12 15.35.42.png\"\/><\/p><p><br\/><\/p><p><br\/><\/p>",
								"photo_count": "1",
								"sales_count": "146",
								"access_count": "10966",
								"video": "",
								"is_exist_many_spec": "0",
								"spec_base": "",
								"plugins_membershiplevelvip_price_extends": "",
								"seo_title": "",
								"seo_keywords": "",
								"seo_desc": "",
								"is_delete_time": "0",
								"add_time": "2019-01-14 16:05:35",
								"upd_time": "2023-08-12 17:28:53",
								"price_container": {
									"price": "1750.00",
									"min_price": "1750.00",
									"max_price": "1750.00",
									"original_price": "2700.00",
									"min_original_price": "2700.00",
									"max_original_price": "2700.00"
								},
								"goods_url": "\/pages\/goods-detail\/goods-detail?id=6",
								"images_old": "\/static\/upload\/images\/goods\/2023\/08\/12\/1691824869950654.png",
								"video_old": "",
								"place_origin_name": "广东省",
								"brand_name": "ViVo",
								"specifications": [],
								"user_cart_count": 0,
								"show_field_original_price_text": "原价",
								"show_field_price_text": "售价",
								"plugins_view_panel_data": [],
								"plugins_view_icon_data": [],
								"is_error": 0,
								"error_msg": ""
							}, 
							  {
								"activity_id": "3",
								"goods_id": "5",
								"discount_rate": "0.00",
								"dec_price": "0.00",
								"is_recommend": "1",
								"id": "5",
								"store_id": "0",
								"store_user_id": "0",
								"store_goods_id": "0",
								"store_goods_type": "0",
								"store_goods_deliver_type": "0",
								"store_plugins_value": "",
								"store_plugins_author": "",
								"store_is_check_steal": "1",
								"realstore_id": "0",
								"realstore_goods_id": "0",
								"shop_id": "1",
								"shop_user_id": "1",
								"shop_category_id": "0",
								"shop_settle_price": "0.00",
								"shop_settle_rate": "0.00",
								"brand_id": "8",
								"site_type": "-1",
								"title": "HUAWEI Mate 50 Pro 曲面旗舰 超光变XMAGE影像 北斗卫星消息 256GB 冰霜银 华为鸿蒙手机",
								"title_color": "",
								"simple_desc": "HUAWEI Mate 50 Pro 曲面旗舰 超光变XMAGE影像 北斗卫星消息 256GB 冰霜银 华为鸿蒙手机",
								"model": "",
								"place_origin": "19",
								"inventory": "873",
								"inventory_unit": "台",
								"images": "https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691826762414807.png",
								"original_price": "3400.00",
								"min_original_price": "3400.00",
								"max_original_price": "3400.00",
								"price": "2500.00",
								"min_price": "2500.00",
								"max_price": "2500.00",
								"give_integral": "56",
								"buy_min_number": "1",
								"buy_max_number": "0",
								"is_deduction_inventory": "1",
								"is_shelves": "1",
								"content_web": "<p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827475272976.png\" title=\"1691827475272976.png\" alt=\"截屏2023-08-12 15.53.51.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827486979998.png\" title=\"1691827486979998.png\" alt=\"截屏2023-08-12 15.54.01.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827499314526.png\" title=\"1691827499314526.png\" alt=\"截屏2023-08-12 15.54.14.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827507900924.png\" title=\"1691827507900924.png\" alt=\"截屏2023-08-12 15.54.26.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827515845673.png\" title=\"1691827515845673.png\" alt=\"截屏2023-08-12 15.55.14.png\"\/><\/p><p><image src=\"https:\/\/d1cdn.shopxo.vip\/static\/upload\/images\/goods\/2023\/08\/12\/1691827555731903.png\" title=\"1691827555731903.png\" alt=\"截屏2023-08-12 15.57.47.png\"\/><\/p><p><br\/><\/p>",
								"photo_count": "1",
								"sales_count": "181",
								"access_count": "13073",
								"video": "",
								"is_exist_many_spec": "0",
								"spec_base": "",
								"plugins_membershiplevelvip_price_extends": "",
								"seo_title": "",
								"seo_keywords": "",
								"seo_desc": "",
								"is_delete_time": "0",
								"add_time": "2019-01-14 15:59:58",
								"upd_time": "2023-08-12 16:09:52",
								"price_container": {
									"price": "2500.00",
									"min_price": "2500.00",
									"max_price": "2500.00",
									"original_price": "3400.00",
									"min_original_price": "3400.00",
									"max_original_price": "3400.00"
								},
								"goods_url": "\/pages\/goods-detail\/goods-detail?id=5",
								"images_old": "\/static\/upload\/images\/goods\/2023\/08\/12\/1691826762414807.png",
								"video_old": "",
								"place_origin_name": "广东省",
								"brand_name": "华为",
								"specifications": [],
								"user_cart_count": 0,
								"show_field_original_price_text": "原价",
								"show_field_price_text": "售价",
								"plugins_view_panel_data": [],
								"plugins_view_icon_data": [],
								"is_error": 0,
								"error_msg": ""
							}],
							"keywords_arr": ["华为", "苹果", "戴尔"],
							"add_time_date_cn": "05月15日 · 2022年",
							"add_time_date": "2022-05-15"
						},
						};
					_this.setData({
					    data_base: data.base || null,
					    data: data.data || null,
					    data_list_loding_msg: '',
					    data_list_loding_status: 0,
					    data_bottom_line_status: (data.data || null) != null && (data.data.goods_list || null) != null && data.data.goods_list.length > 0,
					});
					
					if ((_this.data || null) != null) {
					    // 基础自定义分享
					    _this.setData({
					        share_info: {
					            title: _this.data.seo_title || _this.data.title,
					            desc: _this.data.seo_desc || _this.data.describe,
					            path: '/pages/plugins/activity/detail/detail',
					            query: 'id=' + this.data.id,
					            img: this.data.cover,
					        },
					    });
					
					    // 标题
					    if ((_this.data.title || null) != null) {
					        uni.setNavigationBarTitle({
					            title: _this.data.title,
					        });
					    }
					}
				}, 1200);
				return;
                uni.request({
                    url: app.globalData.get_request_url('detail', 'index', 'activity'),
                    method: 'POST',
                    data: {
                        id: this.params.id || 0,
                    },
                    dataType: 'json',
                    success: (res) => {
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            var data = res.data.data;
                            this.setData({
                                data_base: data.base || null,
                                data: data.data || null,
                                data_list_loding_msg: '',
                                data_list_loding_status: 0,
                                data_bottom_line_status: (data.data || null) != null && (data.data.goods_list || null) != null && data.data.goods_list.length > 0,
                            });

                            if ((this.data || null) != null) {
                                // 基础自定义分享
                                this.setData({
                                    share_info: {
                                        title: this.data.seo_title || this.data.title,
                                        desc: this.data.seo_desc || this.data.describe,
                                        path: '/pages/plugins/activity/detail/detail',
                                        query: 'id=' + this.data.id,
                                        img: this.data.cover,
                                    },
                                });

                                // 标题
                                if ((this.data.title || null) != null) {
                                    uni.setNavigationBarTitle({
                                        title: this.data.title,
                                    });
                                }
                            }
                        } else {
                            this.setData({
                                data_bottom_line_status: false,
                                data_list_loding_status: 2,
                                data_list_loding_msg: res.data.msg,
                            });
                        }

                        // 分享菜单处理
                        app.globalData.page_share_handle(this.share_info);
                    },
                    fail: () => {
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_bottom_line_status: false,
                            data_list_loding_status: 2,
                            data_list_loding_msg: '网络开小差了哦~',
                        });
                        app.globalData.showToast('网络开小差了哦~');
                    },
                });
            },
        },
    };
</script>
<style>
    @import './detail.css';
</style>
