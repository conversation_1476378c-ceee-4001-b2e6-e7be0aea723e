<template>
	<view :class="theme_view" v-if="visible">
		<view class="page-bottom-fixed">
			<!-- 主体内容 -->
			<!-- <block v-if="data_list_loding_status == 3"> -->
		<block>
				<!-- <form @submit="form_submit" class="form-container"> -->
				<form class="form-container">
					<view class="padding-main page-bottom-fixed">
						<view class="bg-white border-radius-main oh">
							<view class="form-gorup oh flex-row jc-sb align-c">
								<view>
									<!-- 头像 -->
									{{$t('accountUser.TX')}}
									</view>
								<view class="flex-row align-c">
									<view class="bg-white br-0 lh-0 padding-horizontal-sm" hover-class="none"
										open-type="chooseAvatar" @tap="choose_avatar_event">
										<image :src="user_data.logoUrl" mode="widthFix"
											class="circle br user-avatar flex-1 flex-width"></image>
									</view>
									<iconfont name="icon-qiandao-jiantou2" size="34rpx" color="#ccc"></iconfont>
								</view>
							</view>

							<view class="form-gorup oh flex-row jc-sb align-c">
								<!-- <view class="form-gorup-title">用户名</view> -->
								<view class="form-gorup-title">{{$t('accountUser.yhm')}}</view>
								<view class="flex-row align-c flex-1 flex-width">
									<input maxlength="16" v-model="user_data.nickName"
										placeholder-class="cr-grey-9 tr" class="cr-base tr margin-right-sm"
										:placeholder="$t('accountUser.yhm')" />
								</view>
							</view>

							<!-- <view class="form-gorup oh flex-row jc-sb align-c">
								<view class="form-gorup-title">昵称<text class="form-group-tips-must">*</text></view>
								<view class="flex-row align-c flex-1 flex-width">
									<input :type="application_client_type == 'weixin' ? 'nickname' : 'text'"
										name="nickname" :value="user_data.nickName || ''" maxlength="16"
										placeholder-class="cr-grey-9 tr" class="cr-base tr margin-right-sm"
										placeholder="昵称1~16个字符" />
								</view>
							</view>
							<view class="form-gorup oh jc-sb"
								style="display: flex;justify-content: space-between;width: 90%;">
								<view class="form-gorup-title">我的二维码</view>
								<image src="https://file.36sm.cn/xtjyjt/images/common/barcode/code.png"
									style="width: 30rpx; height: 30rpx;" mode=""></image>
							</view> -->
						</view>

						<view class="bottom" style="margin-top: 30rpx;">
							<!-- <view class="form-gorup oh flex-row jc-sb align-c">
								<view class="form-gorup-title">性别</view>
								<view class="flex-row jc-e align-c flex-1 flex-width">
									<picker @change="select_change_event" :value="user_data.gender || ''"
										:range="gender_list" range-key="name" name="gender" data-field="gender"
										class="margin-right-sm wh-auto tr">
										<view class="uni-input cr-base picker">
											{{ gender_list[user_data.gender].name || '' }}
										</view>
									</picker>
									<iconfont name="icon-qiandao-jiantou2" size="34rpx" color="#ccc"></iconfont>
								</view>
							</view> -->
						<!-- 	<view class="form-gorup oh flex-row jc-sb align-c">
								<view class="form-gorup-title">出生日期</view>
								<view class="flex-1 flex-width flex-row jc-e align-c">
									<picker class="margin-right-sm wh-auto tr" name="birthday" mode="date"
										:value="user_data.birthday || ''" data-field="birthday"
										@change="select_change_event">
										<view
											:class="'picker ' + ((user_data.birthday || null) == null ? 'cr-grey' : '')">
											{{ user_data.birthday || '请选择生日' }}
										</view>
									</picker>
									<iconfont name="icon-qiandao-jiantou2" size="34rpx" color="#ccc"></iconfont>
								</view>
							</view>

							<view class="form-gorup oh flex-row jc-sb align-c">
								<view class="form-gorup-title">服务专员</view>
								<view class="flex-row align-c flex-1 flex-width">
									<input type="text" name="address" :value="user_data.address || ''" maxlength="30"
										placeholder-class="cr-grey-9 tr" class="cr-base tr margin-right-sm"
										placeholder="XXX" />
								</view>
							</view -->


						</view>

						<view class="bottom-fixed">
							<view class="bottom-line-exclude">
								<button class="cr-white round text-size" type="default" style="background: #FF4000; box-shadow: 0rpx 2rpx 5rpx 0rpx rgba(0,0,0,0.1);" form-type="submit" hover-class="none"
									:disabled="form_submit_disabled_status" @tap="MemberUpdate">
									<!-- 保存 -->
									{{$t('accountUser.bc')}}
									</button>
							</view>
						</view>
					</view>
				</form>
			</block>

			<!-- 错误提示 -->
			<component-no-data :propStatus="data_list_loding_status"
				:propMsg="data_list_loding_msg"></component-no-data>
		</view>
	</view>
	<showLoading v-else></showLoading>
</template>
<script>
	const app = getApp();
	import componentNoData from '../../components/no-data/no-data';
    import { getUserInfo, UpdateUserInfo } from "@/common/request/api/user.js"
	import { mapState,mapActions } from 'vuex'
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				theme_view: app.globalData.get_theme_value_view(),
				application_client_type: app.globalData.application_client_type(),
				data_list_loding_status: 1,
				data_list_loding_msg: '',
				form_submit_disabled_status: false,
				default_avatar: app.globalData.data.default_user_head_src,
				user_data: {},
				gender_list: [],
				visible: false
			};
		},

		components: {
			componentNoData,
		},
		computed:{
			...mapState({
				userInfo: state => state.user.userInfo
			}),
		},
		onLoad() {
			setTimeout(() => {
				this.$data.visible = true
			}, 1200)
		},

		onShow() {
			// 数据加载
			this.init();
		},

		methods: {
			// 获取数据
			init() {
				var user = this.userInfo;
				console.log(user, '个人信息')
				if (user != false) {
					this.get_data()
				}
				// var user = app.globalData.get_user_info(this, 'init');
				// console.log(user, 'user')
				// if (user != false) {
				// 	// 用户未绑定手机则转到登录页面
				// 	if (app.globalData.user_is_need_login(user)) {
				// 		uni.redirectTo({
				// 			url: '/pageo/login/login?event_callback=init',
				// 		});
				// 		this.setData({
				// 			data_list_loding_status: 0,
				// 			data_list_loding_msg: '请先绑定手机',
				// 		});
				// 		return false;
				// 	} else {
				// 		this.get_data();
				// 	}
				// } else {
				// 	this.setData({
				// 		data_list_loding_status: 0,
				// 		data_list_loding_msg: '请先登录',
				// 	});
				// }
			},

			// 获取数据
			get_data() {
				getUserInfo().then(res => {
					if(res.code == 200) {
						let data = res.data.data
						console.log(data, '用户信息')
						this.setData({
							data_list_loding_status: 3,
							user_data: data
						})
					} else {
						uni.showToast({
							title: err.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: 'none'
					})
				})
			},

			// url事件
			url_event(e) {
				app.globalData.url_event(e);
			},

			// 生日、性别选择事件
			select_change_event(e) {
				var temp = this.user_data;
				temp[e.currentTarget.dataset.field] = e.detail.value;
				this.setData({
					user_data: temp
				});
			},

			// 头像事件
			choose_avatar_event(e) {
				var self = this;
				uni.chooseImage({
					count: 1,
					// sourceType: ['album', 'camera'],
					// sizeType: ['original', 'compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								self.user_data.logoUrl = Data.data.data.fileUrl
								if (Data.code == 200) {
									uni.showToast({
										// title: "上传成功",
										title: this.$t('message.sccg'),
										icon: 'success'
									})
								}
							}
						})
					},
				})
				// if (this.application_client_type == 'weixin') {
				// 	self.upload_handle(e.detail.avatarUrl);
				// } else {
				// 	uni.chooseImage({
				// 		count: 1,
				// 		success(res) {
				// 			if (res.tempFilePaths.length > 0) {
				// 				self.upload_handle(res.tempFilePaths[0]);
				// 			}
				// 		},
				// 	});
				// }
			},

			// 上传处理
			upload_handle(image) {
				var self = this;
				uni.uploadFile({
					url: app.globalData.get_request_url('useravatarupload', 'personal'),
					filePath: image,
					name: 'file',
					formData: {},
					success: function(res) {
						if (res.statusCode == 200) {
							var data = typeof res.data == 'object' ? res.data : JSON.parse(res.data);
							if (data.code == 0) {
								var temp = self.user_data;
								temp['avatar'] = data.data;
								self.setData({
									user_data: temp
								});
							} else {
								app.globalData.showToast(data.msg);
							}
						}
					},
				});
			},
			MemberUpdate() {
				const data ={
					// logoUrl: this.user_data.avatar,
					logoUrl: this.user_data.logoUrl,
					memberId: this.userInfo.memberId,
					nickName: this.user_data.nickName,
				}
				console.log(data, '提交的参数')
				api({
					url: 'mb/member/update',
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: data
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							// title: '保存成功',
							title: this.$t('message.bccg'),
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
				// UpdateUserInfo(data).then(res => {
				// 	if(res.code == 200) {
				// 		let data = res.data.data
				// 		console.log(data, '返回结果')
				// 	} else {
				// 		uni.showToast({
				// 			title: err.msg,
				// 			icon: 'none'
				// 		})
				// 	}
				// }).catch(err => {
				// 	uni.showToast({
				// 		title: err.msg,
				// 		icon: 'none'
				// 	})
				// })
			},
			// 数据提交
			form_submit(e) {
				console.log(e, 'eee')
				return
				// 表单数据
				var form_data = e.detail.value;
				// 头像
				form_data['avatar'] = this.user_data.avatar || '';
				// 生日
				form_data['birthday'] = this.user_data.birthday || '';
				// 性别
				form_data['gender'] = this.user_data.gender || 0;

				// 数据保存
				this.setData({
					form_submit_disabled_status: true,
				});
				uni.showLoading({
					// title: '处理中...',
					title: this.$t('accountUser.jzz'),
					
				});
				uni.request({
					url: app.globalData.get_request_url('save', 'personal'),
					method: 'POST',
					data: form_data,
					dataType: 'json',
					success: (res) => {
						uni.hideLoading();
						this.setData({
							form_submit_disabled_status: false,
						});
						if (res.data.code == 0) {
							uni.setStorageSync(app.globalData.data.cache_user_info_key, res.data.data);
							app.globalData.showToast(res.data.msg, 'success');
							setTimeout(function() {
								uni.navigateBack();
							}, 1200);
						} else {
							if (app.globalData.is_login_check(res.data)) {
								app.globalData.showToast(res.data.msg);
							} else {
								// app.globalData.showToast('提交失败，请重试！');
								app.globalData.showToast(this.$t('message.tjsb'));
							}
						}
					},
					fail: () => {
						uni.hideLoading();
						this.setData({
							form_submit_disabled_status: false,
						});
						// app.globalData.showToast('网络开小差了哦~');
						app.globalData.showToast(this.$t('message.wlkxc'));
					},
				});
			},
		},
	};
</script>
<style>
	@import './personal.css';
</style>