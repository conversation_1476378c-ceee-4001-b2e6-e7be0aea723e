import {
	getUserInfo
} from '@/common/request/api/user.js'
const state ={
	oneUserPage : true,
	isLogin: false,
	userInfo: {},
	token: null,
	ephemeral:null,
}
const getters ={
	
}
const mutations={
	setOneUserPage(state, data) {
		state.oneUserPage = data
	},
	setToken(state, data) {
		state.token = data
	},
	setLogin(state, data) {
		state.isLogin = data
	},
	setUserInfo(state, data) {
		state.userInfo = data
	},
	setEphemeral(state, data) {
		state.ephemeral = data
	},
}
const actions = {
	// 获取用户信息
	getUserInfo({
		state,
		commit,
		dispatch
	}) {
		return new Promise((resolve, reject) => {
			getUserInfo().then(res => {
				if (res.code == 200) {
					commit('setUserInfo', res.data.data);
					uni.setStorageSync("userInfo", res.data.data)
				}
				resolve(res)
			}).catch(e => {
				reject(e)
			})
		})
	},
	// 退出登录状态
	outLogin({
		commit
	}) {
		// uni.setStorageSync('isLogin', false);
		commit('setToken', null)
		commit('setUserInfo', {});
		commit('setLogin', false);
		commit('setEphemeral', null);
	},
}
export default {
	state,
	mutations,
	actions,
	getters
}