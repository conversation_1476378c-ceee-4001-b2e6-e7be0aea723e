<template>
	<!-- 教练详情 -->
	<view class="page">
		<scroll-view class="scroll-container" :scroll-into-view="currentSection" scroll-y="true"
			style="height: 100vh;scroll-behavior: smooth;">
			<view class="info">
				<view class="info_header" style="display: flex;">
					<view class="info_header_image">
						<image :src="DetailData.coverImageUrl" mode="aspectFill"></image>
					</view>
					<view class="name_wrapper">
						<view class="name_content">
							<text class="name">{{ DetailData.coachName }}</text>
							<text>|</text>
							<text class="level">Lv {{ DetailData.cooperateLevel }}</text>
						</view>
						<view class="points">
							<text>{{ DetailData.starLevel }}分</text>
						</view>
					</view>
				</view>

				<view class="introduction">
					<text class="introduction_text">{{ DetailData.coachRemarks }}</text>
					</text>
				</view>
			</view>
			<view class="goods">
				<text class="goods_title">擅长调理</text>
				<view class="goods_banner_list">
					<view class="substance">
						<view class="" v-for="(item,index) in DetailData.contentRichList" :key="index">
							<view class="substanceTitle" v-if="item.type == 'title'">
								<image src="https://file.36sm.cn/xtjyjt/images/common/one.png" mode=""></image>
								<view class="">
									{{item.content}}
								</view>
							</view>
							<view class="substanceHtml" v-if="item.type == 'text'">
								<view class="html" v-html="item.content"></view>
							</view>
							<view class="photo" v-if="item.type == 'pic'">
								<image v-for="img in item.contentList" :src="img" mode="" :key="img"
									@tap="preview(img)"></image>
							</view>
							<view class="photo" v-if="item.type == 'video'">
								<video class="video" v-for="video in item.contentList" :key="video" :src="video"
									:danmu-list="danmuList" enable-danmu danmu-btn controls></video>
							</view>
						</view>
					</view>
					<!-- <view v-for="(url,index) in bannerData" :key="index" class="banner_item">
						<image :src="url" mode="aspectFill"></image>
					</view> -->
				</view>
				<view class="goods_banner_list" v-if="!DetailData.contentRichList.length">
					<view class="substanceTitle">
						<text>暂无技师介绍</text>
					</view>
				</view>
			</view>

		</scroll-view>
		<cc-gifLoading v-if="!visible"
			gifSrc="https://testksjjkl.oss-cn-hangzhou.aliyuncs.com/txjy/2025/02/12/142c4f60aee441ab884da7052337a6a7.gif"></cc-gifLoading>
		<view class="bottomCon">
			<view class="topTab">
				<view class="bottom_left">
					<text class="title"></text>
					<text class="timevalue"></text>
				</view>
				<view class="bottom_right" @click="goSymptoms">
					<text>选择症状</text>
				</view>
			</view>
			<view style="height: 68rpx;">
			</view>
		</view>

	</view>
</template>
<script>
	import api from '@/common/request/index.js'
	import {
		getCurrentInstance
	} from "vue";
	export default {
		data() {
			return {
				navBarHeight: 0,
				coachId: '',
				DetailData: [],
				contentRichList: [],
				scrollTop: 0, // 当前的滚动位置
				showTab: true, // 是否显示Tab页
				lastScrollTime: 0, // 上一次滚动的时间  
				currentSection: '', // 当前需要滚动到的section的id
				visible: false,
				image1: '',
				image2: '',
				imageUrl: '',
				timeoutId: null,
				danmuList: [],
				lastKnownSection: null,
				QRimg: '',
				downloading: false,
				bannerData: ["https://file.36sm.cn/jaxinfo/2025/02/28/acfd50ff413a4bcba0b7f8a40ba08879.png",
					"https://file.36sm.cn/jaxinfo/2025/02/28/acfd50ff413a4bcba0b7f8a40ba08879.png",
					"https://file.36sm.cn/jaxinfo/2025/02/28/acfd50ff413a4bcba0b7f8a40ba08879.png"
				]
			}
		},
		onPageScroll(e) {
			console.log(e, 'e')
		},
		onLoad(options) {
			this.coachId = options.coachId
			console.log(this.coachId, 'this.coachId');
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度 
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});

			//调用数据
			this.getDetailData()
		},
		computed: {
			formattedYear() {
				const date = new Date(this.DetailData.workParticular);
				return date.getFullYear(); // 提取年份
			}
		},
		onShareAppMessage() {
			return {
				title: '教练详情',
				path: `/pageg/coash_resources/coashDetails?coachId=${this.coachId}`,
				content: '教练详情'
			}
		},
		methods: {
			moveHandle() {},
			getQRimg() {
				const params = {
					path: `/pageg/coash_resources/coashDetails?coachId=${this.coachId}`,
				}
				api({
					url: `dt/details/path/info/code`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, '二维码资源');
						this.QRimg = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			preview(imageSrc) {
				console.log(imageSrc, '图片路径');

				uni.previewImage({
					urls: [imageSrc],
					current: imageSrc
				});
			},
			goback() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			onScroll(e) {
				return
				let that = this
				if (that.timeoutId) {
					clearTimeout(that.timeoutId);
				}

				that.timeoutId = setTimeout(() => {
					const scrollTop = e.detail.scrollTop;
					that.scrollTop = scrollTop;

					// 判断是否超过300px并切换状态
					if (scrollTop > 250) {
						if (that.showTab) {
							that.showTab = false; // 只在状态改变时切换
						}
					} else {
						if (!that.showTab) {
							that.showTab = true; // 只在状态改变时切换
						}
					}
				}, 100)
			},

			//获取详情数据  
			getDetailData() {
				api({
					url: `tx/coach/id/info/${this.coachId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
				}).then(res => {
					if (res.code == 200) {
						console.log(res);
						this.DetailData = res.data.data
						this.contentRichList = res.data.data.contentRichList
						// this.addGoodsBanner(res.data.data.contentRichList)
						setTimeout(() => {
							this.$data.visible = true
						}, 1200)

					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},

			//选择症状  回到上一页
			goSymptoms() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
				// uni.navigateTo({
				// 	url: `/pageg/all_engineers/index?selectedDate=${this.selectedDD.selectedDate}&selectedHour=${this.selectedDD.selectedHour}&selectedMinute=${this.selectedDD.selectedMinute}&placeName=${this.shopDetail.placeName}&placeId=${this.shopDetail.placeId}`
				// })
			},
			// 添加擅长调理banner
			addGoodsBanner(resList) {
				let bannerData = []
				resList.forEach(item => {
					if (item.type === 'pic') {
						bannerData.push(item.contentList[0])
					}
				})
				this.bannerData = bannerData
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		width: 100%;
		height: 100%;
		// min-height: 100vh;
		background-color: #f5f5f5;

		.scroll-container {
			.info {
				height: 400rpx;
				border-radius: 20rpx;
				background: #FFFFFF;
				padding: 30rpx;
				margin: 80rpx 20rpx 20rpx 20rpx;
				box-sizing: border-box;

				s .info_header {
					height: 110rpx;
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

				}

				.introduction {
					max-height: 290rpx;
					padding: 30rpx 0 0 0;

					.introduction_text {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						line-height: 40rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 5;
						-webkit-box-orient: vertical;
					}

				}

				.info_header_image {
					width: 200rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 100rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 20rpx;

					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 100rpx;
						position: relative;
						top: -50rpx;
					}
				}

				.name_wrapper {
					.name_content {
						display: flex;
						gap: 10rpx;
						align-items: center;

						.name {
							font-family: PingFang SC, PingFang SC;
							font-weight: 700;
							font-size: 48rpx;
							color: #333333;
							font-style: normal;
							text-transform: none;
						}
					}

					.points {
						color: #a1a1a1;
						font-size: 24rpx;
					}
				}
			}

			.goods {
				min-height: 400rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 30rpx;
				margin: 0rpx 20rpx 20rpx 20rpx;
				box-sizing: border-box;

				.goods_title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 700;
					font-size: 48rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
				}

				.goods_banner_list {
					// display: flex;
					// justify-content: flex-start;
					// align-items: center;
					// overflow-x: scroll;
					// padding: 25rpx 0;
					// gap: 20rpx;

					// .banner_item {
					// 	width: 300rpx;
					// 	height: 250rpx;
					// 	background: #FFFFFF;
					// 	border-radius: 20rpx;

					// 	image {
					// 		width: 300rpx;
					// 		height: 250rpx;
					// 		border-radius: 20rpx;
					// 	}
					// }
					.substance {
					}

					.substanceTitle {
						display: flex;
						align-items: center;
						padding: 10rpx 0;
						box-sizing: border-box;

						image {
							width: 28rpx;
							height: 30rpx;
						}

						view {
							font-size: 32rpx;
							font-weight: bold;
							margin: 0 0 0 20rpx;
						}

					}

					.substanceHtml {
						.html {
							margin-bottom: 20rpx;
						}

					}

					.photo {
						display: flex;
						flex-wrap: wrap;
						align-items: center;

						image {
							width: 315rpx;
							height: 200rpx;
							margin: 11rpx;
						}

						.video {
							width: 700rpx;
							height: 300rpx;
							margin: 0 auto;
							position: relative;

							.videoimg {
								width: 100%;
								height: 100%;
								border-radius: 10rpx;
							}

							.iconbf {
								position: absolute;
								width: 100rpx;
								height: 100rpx;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
							}
						}
					}
				}
			}
		}


		.bottomCon {
			position: fixed;
			width: 100vw;
			height: 188rpx;
			background: #FFFFFF;
			bottom: 0;

			.topTab {
				height: 120rpx;
				line-height: 120rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.bottom_left {
					margin-left: 20rpx;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						margin-right: 5rpx;
					}

					.timevalue {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 32rpx;
						color: #FF6900;
						line-height: 60rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.bottom_right {
					margin-right: 20rpx;
					width: 240rpx;
					height: 80rpx;
					line-height: 80rpx;
					background: #FF6900;
					border-radius: 94rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #FFFFFF;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}
		}
	}
</style>