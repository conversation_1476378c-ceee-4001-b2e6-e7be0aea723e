<template>
	<view class="page">
		<newNavBar :title="$t('accountUser.tx')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
				<view  style="display: flex;justify-content: flex-end;color: #1D91FF; padding-right: 20rpx;" @tap="goSetcard">
					{{$t('accountUser.yhksz')}}
				</view>
				<u-form-item :label="$t('accountUser.yhksz')" prop="bank" borderBottom labelWidth="300rpx">
					<u-input v-model="formData.bank" border="none" :placeholder="$t('accountUser.yhksz')"  type="text">
						<u--text :text="$t('accountUser.yhmc')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				<u-form-item :label="$t('accountUser.zhmc')" borderBottom labelWidth="300rpx">
					<u-input v-model="formData.branch" border="none" :placeholder="$t('accountUser.zhmc')" type="text">
						<u--text :text="$t('accountUser.zhmc')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.yhkh')" borderBottom labelWidth="300rpx">
					<u-input v-model="formData.cardNo" border="none" :placeholder="$t('accountUser.yhkh')" type="text" @change="inputValCardNo">
						<u--text :text="$t('accountUser.yhkh')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.skrmc')" borderBottom labelWidth="300rpx">
					<u-input v-model="formData.memberName" border="none" :placeholder="$t('accountUser.skrmc')" type="text">
						<u--text :text="$t('accountUser.skrmc')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.txdgrzh')" borderBottom labelWidth="400rpx">
					<view class="" style="display: flex;">
						<u-input v-model="formData.totalAmount" maxlength="10" border="none" :placeholder="$t('accountUser.txje')" type="number" @change="inputVal">
							<u--text text="￥" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
						</u-input>
						<!-- <u-input v-model="formData.arrivedAmount" border="none" type="text" disabled> -->
						<u-input v-model="arrivedAmount" border="none" type="text" disabled>
							<u--text :text="$t('accountUser.DZ')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
						</u-input>
					</view>
					<!-- <view style="display: inline-block;">
						最小提现额{{minAmount}}
					</view> -->
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.ktxje')" borderBottom labelWidth="400rpx">
					<u-input v-model="my_money.balance" border="none" :placeholder="$t('accountUser.ye')" type="text" disabled>
						<u--text :text="my_money.coinName" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				<u-form-item :label="$t('accountUser.zjmm')" borderBottom labelWidth="300rpx" prop="jjMm">
					<u-input v-model="formData.jjMm" border="none" :placeholder="$t('message.qsrzjmmlw')" type="password" maxlength="6">
						<u--text :text="$t('accountUser.zjmm')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.txbz')" borderBottom labelWidth="300rpx">
					<u--textarea v-model="formData.remarks" :placeholder="$t('accountUser.txbz')" autoHeight border="none" clearable
						disableDefaultPadding></u--textarea>
				</u-form-item>
				
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	import md5 from 'js-md5';
	// var common_static_url = app.globalData.get_static_url('common');

	
	export default {
		data() {
			return {
				pickerShow: false,
				type:0,
				loading:false,
				// 金额单位
				coinName: "",
				// 提现最小额度
				minAmount: '',
				formData: {
					bank: "",
					// 银行卡号
					cardNo: "",
					// 支行名称
					branch: "",
					// 提现的金额
					totalAmount: "",
					// 实际获得的提现金额
					arrivedAmount: "",
					// 提交备注
					remarks: "",
					// 提现手续费
					fee: "",
					// 资金密码
					jjMm: "",
					memberName: ""
					
				},
				bankList: [],
				bankId: "",
				my_money: {
					balance: "",
					coinName: ""
				},
				rules: {
					bank: {
						required: true,
						// message: '请选择银行名称',
						message: this.$t('message.qxzyh'),
						trigger: ['blur']
					},
					branch: {
						required: true,
						// message: '请选择支行',
						message: this.$t('message.qxzzh'),
						trigger: ['blur', 'change']
					},
					cardNo: {
						required: true,
						// message: '请输入银行卡号',
						message: this.$t('message.qsryhkh'),
						trigger: ['blur', 'change']
					},
					totalAmount: {
						required: true,
						// message: '请输入提现金额',
						message: this.$t('message.qsrtxje'),
						trigger: ['blur', 'change']
					},
					jjMm: {
						required: true,
						// message: '请输入资金密码',
						message: this.$t('message.qsrzjmm'),
						trigger: ['blur', 'change']
					}
				}
			}
		},
		onLoad(data) {
			console.log(data,'路由参数')
			
			 uni.$on('getbankId', (data) => {
			        console.log('我在B页面选择了:', data);
			        this.bankId = data.current;
			        console.log(this.bankId, 'bankId');
			        
			        // 使用箭头函数，this 指向正确的组件实例
			        this.get_money();
			        this.getAddress();
			    });
			// if(data.bankId) {
			// 	this.bankId = data.bankId
			// } else {
			// 	this.bankId = ''
			// }
			
			this.get_money()
			this.getAddress()
		},
		onReady() {
		},
		created() {
			this.getTxfree()
		},
		computed: {
			arrivedAmount() {
				if(this.formData.totalAmount > 0) {
					// return this.formData.totalAmount - (this.formData.fee/100)*this.formData.totalAmount
					return (this.formData.totalAmount*(100-this.formData.fee)/100).toFixed(2)
				} else {
					return 0
				}
			},
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			goSetcard() {
				uni.navigateTo({
					url: '/pageo/bank_cards/bank_cards?type=tx'
				})
			},
			getAddress(){
				api({
					url: `mb/bd/get/list?page=1&size=10`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						const data = res.data.data.list
						console.log(data, '会员银行卡信息')
						this.bankList = [...data]
						if(data.length > 0) {
							if(this.bankId !== '') {
							   const findList = data.find(item => item.bankId == this.bankId)
							   console.log(findList, '匹配的数据')
							   this.formData.bank = findList.bank
							   this.formData.branch = findList.branch
							   this.formData.cardNo = findList.cardNo	
							} else {
							  this.formData.bank = data[0].bank
							  this.formData.branch = data[0].branch
							  this.formData.cardNo = data[0].cardNo	
							}
						}
					}
				})
			},
			// 获取会员充值码信息
			getTxfree() {
				api({
				    url: `mb/tx/get/sxf/info?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, 'res.data')
						this.formData.fee = res.data.data.fee
						this.minAmount = res.data.data.minAmount
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						if(this.formData.totalAmount < this.minAmount) {
							uni.showToast({
								// title: '至少提现'+ this.minAmount,
								title: this.$t('message.zstx')+ this.minAmount,
								icon:'none'
							})
							return
						}
						// 保留2位小数
						this.formData.arrivedAmount = this.arrivedAmount
						this.formData.totalAmount = Number(this.formData.totalAmount).toFixed(2)
						console.log(this.formData, '提交参数')
						if(this.formData.totalAmount - this.my_money.balance > 0) {
							uni.showToast({
								// title: '可提现金额不足',
								title: this.$t('message.ktxjebz'),
								icon:'none'
							})
							return
						}
						
						const data = {
							bank: this.formData.bank,
							cardNo: this.formData.cardNo,
							// 支行名称
							branch: this.formData.branch,
							// 提现的金额
							totalAmount: Number(this.formData.totalAmount),
							// 实际获得的提现金额
							arrivedAmount: Number(this.formData.arrivedAmount),
							// 提交备注
							remarks: this.formData.remarks,
							// 提现手续费
							fee: Number(this.formData.totalAmount) - Number(this.formData.arrivedAmount),
							// 资金密码
							jjMm: md5(this.formData.jjMm),
							memberName: this.formData.memberName
							
						}
						
						console.log(data, '提交参数')
						
						this.loading = true
						setTimeout(() => {
							this.loading = false
						}, 2000)
						api({
							url: `mb/tx/save`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: data
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									// title: '提交成功',
									title: this.$t('message.czcg'),
									icon:'none'
								})
								setTimeout(() => {
									this.$back()
								}, 500)
							}
						})
						
					}
				})
			},
			get_money() {
				api({
				    url: 'mb/wallet/get/list',
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
					if (res.code == 200) {
						let data = res.data.data
						// console.log(data, '钱包信息')
						data.map(item => {
							if(item.coinId == 1) {
								this.my_money.coinName = item.coinName
								this.my_money.balance = item.balance
							}
						// 	if (item.coinId == 2) {
						// 		this.my_money.my_intergal = item.balance
						// 		this.my_money.intergalname = item.coinName
						// 	} else if (item.coinId == 1) {
						// 		// this.setData({
						// 		// 	my_money: {
						// 		// 		my_balance: item.balance
						// 		// 	}
						// 		// })
						// 		this.my_money.my_balance = item.balance
						// 		this.my_money.balancename = item.coinName
						// 	} else if (item.coinId == 3) {
						// 		this.my_money.my_contribute = item.balance
						// 		this.my_money.contributename = item.coinName
						// 	} else if (item.coinId == 4) {
						// 		this.my_money.my_achievement = item.balance
						// 		this.my_money.achievementname = item.coinName
						// 	}
						})
						console.log(this.my_money, '钱包信息')
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: 'none'
					})
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.totalAmount = temp
				})
			},
			inputValCardNo(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.cardNo = temp
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>