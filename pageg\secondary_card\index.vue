<template>
	<view class="page">
		<view class="header" :style="{ 'padding-top': navBarHeight + 'px' }">
			<newNavBar :title="hometitle" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		</view>
		<view class="orderView">
			<z-paging height="1450rpx" ref="paging" :fixed="false" @query="queryList" :auto="false">
				<refresh-loading slot="refresher"></refresh-loading>
				<view class="orderList" v-if="cardList.length > 0">
					<view class="orderitem" v-for="(item,index) in cardList" :key="item.productCode"
						@click="goToCardDe(item)">
						<view class="orderdetail">
							<view class="itemleft">
								<image :src="item.productMainImg" mode="aspectFill"
									style="width: 160rpx;height: 160rpx;border-radius: 16rpx;">
								</image>
							</view>
							<view class="itemright">
								<view class="top">
									<view class="shopname">
										<text>{{item.productTitle}}</text>
									</view>
								</view>
								<view class="center">
									<text>{{item.spJs}}</text>
								</view>

							</view>
						</view>
					</view>
				</view>
				<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line :propStatus="bottom_line_status" propMsg="已经到底了哦!"
					v-if="!loadingStatus && cardList.length > 0"></component-bottom-line>
			</z-paging>
		</view>
	</view>
</template>

<script>
	import newNavBar from '../../components/newNavBar/newNavBar.vue'
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	export default {
		components: {
			newNavBar,
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		data() {
			return {
				navBarHeight: 0,
				// 3 次卡  4储值卡
				platformType: 3,
				params: {
					page: 1,
					size: 10
				},
				cardList: [],
				loadingStatus: true,
				bottom_line_status: false,
				hometitle: '次卡管理'
			}
		},
		onLoad(params) {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			console.log(params, '路径参数')
			if (params.type) {
				if(params.type == 4) {
					this.hometitle = '储值卡管理'
				}
				this.platformType = params.type
				this.getCard()
			}
		},
		methods: {
			queryList(pageNo, pageSize) {
				console.log(pageNo, pageSize, 'pageNo, pageSize')
				this.params.page = pageNo
				this.params.size = pageSize
				this.getCard()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 600)
			},
			goToCardDe(item) {
				uni.navigateTo({
					url: `/pageg/secondary_card/product_details?acid=${item.productCode}&resourceType=0`
				})
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			getCard() {
				// const params = {
				// 	platformType: this.platformType,
				// 	page: this.params.page,
				// 	size: this.params.size
				// }
				api({
					url: `goods/search/get/list/?platformType=${this.platformType}&keyWords=&location=&size=${this.params.size}&page=${this.params.page}`,
					// url: `goods/search/get/list`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get',
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data
						console.log(data, '列表数据')
						if (data.totalCount == 0) {
							this.loadingStatus = false
						}
						if (this.cardList.length == data.totalCount) {
							if (this.params.page == 1) {
								this.cardList = [...data.list]
							}

							setTimeout(() => {
								this.loadingStatus = false
								this.bottom_line_status = true
							}, 500)
						} else {
							if (this.params.page == 1) {
								this.cardList = [...data.list]
								if (this.cardList.length == data.totalCount) {
									this.bottom_line_status = true
								}
							} else {
								this.cardList = [...this.cardList, ...data.list]
							}
						}
						setTimeout(() => {
							this.loadingStatus = false
						}, 500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			}
		}
	}
</script>

</script>
<style lang="scss" scope>
	.page {
		background-color: #FBF5F1;
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}

	.header {
		width: 100%;
		height: 80rpx;
		background-color: #FBF5F1;
		position: fixed;
		z-index: 999;
	}

	.orderView {
		.orderList {
			margin: 0 auto;
			width: 96%;
			background-color: #fff;
			padding: 24rpx 0 4rpx 0;
			border-radius: 24rpx;
			margin-top: 200rpx;

			.orderitem {
				margin: 0 24rpx;
				.orderdetail {
					margin-bottom: 20rpx;
					height: 180rpx;
					border-bottom: 1rpx solid #f8f4eb;
					display: flex;
					align-items: flex-start;
					.itemleft {}
					.itemright {
						margin-left: 24rpx;
						height: 150rpx;
						flex: 1;
						display: flex;
						flex-direction: column;
						// background-color: skyblue;
						// justify-content: space-between;

						.top {
							display: flex;
							justify-content: space-between;

							.shopname {
								font-size: 30rpx;
								color: #333333;
								font-weight: 600;
								// text-align: center;
								font-style: normal;
								text-transform: none;
								width: 340rpx;
								height: 44rpx;
								font-family: PingFang SC, PingFang SC;
								line-height: 44rpx;
								// background-color: skyblue;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}
						}

						.center {
							display: -webkit-box;
							-webkit-box-orient: vertical;
							overflow: hidden;
							-webkit-line-clamp: 3;
							/* 限制显示三行 */
							text-overflow: ellipsis;
							/* 超出部分显示省略号 */
							margin-top: 5rpx;
							align-items: center;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 24rpx;
							color: #666;
							font-style: normal;
							text-transform: none;

						}
					}
				}
			}
		}
	}
</style>