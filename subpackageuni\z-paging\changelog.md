## 2.7.5（2024-01-23）
1.`新增` props：`chat-loading-more-default-as-loading`，支持设置在聊天记录模式中滑动到顶部状态为默认状态时，以加载中的状态展示。  
2.`新增` slots：`chatNoMore`，支持自定义聊天记录模式没有更多数据view。  
3.`修复` 固定在底部view可能出现默认黄色的问题。  
4.`优化` 聊天记录加载更多样式，与普通模式对齐，支持点击加载更多&点击重试，并支持加载更多相关配置。  
5.`优化` 微调下拉刷新和底部加载更多样式。  
6.`优化` 聊天记录模式自动滚动到底部添加延时以避免可能出现的滚动到底部位置不正确的问题。  
7.`优化` 使用新的判断滚动到顶部算法以解决在安卓设备中可能出现的因滚动到顶部时scrollTop不为0导致的下拉刷新被禁用的问题。  
## 2.7.4（2024-01-14）
1.`新增` props:`auto-adjust-position-when-chat`，支持设置使用聊天记录模式中键盘弹出时是否自动调整slot="bottom"高度。  
2.`新增` props:`auto-to-bottom-when-chat`，支持设置使用聊天记录模式中键盘弹出时是否自动滚动到底部。  
3.`新增` props:`show-chat-loading-when-reload`，支持设置使用聊天记录模式中reload时是否显示chatLoading。  
4.`修复` 在聊天记录模式中`scrollIntoViewById`和`scrollIntoViewByNodeTop`无效的问题。  
5.`优化` 聊天记录模式底部安全区域针对键盘开启/关闭兼容处理。  
6.`优化` 更新内置的空数据图&加载失败图，感谢图鸟UI提供的免费可商用的空数据图和加载失败图！  
## 2.7.3（2024-01-10）
1.`新增` 聊天记录模式支持虚拟列表&添加相关demo。  
2.`新增` nvue中list添加`@scrollend`监听。  
3.`优化` 聊天记录模式+vue第一页并且没有更多时不倒置列表。  
4.`优化` 聊天记录模式+nvue中数据不满屏时默认从顶部开始，不进行列表倒置。  
## 2.7.2（2024-01-09）
1.`修复` `vue3+h5`中报错`uni.onKeyboardHeightChange is not a function`的问题。  
2.`优化` 聊天记录模式细节：表情面板在触摸列表时隐藏&添加隐藏动画。  
## 2.7.1（2024-01-08）
1.`新增` `keyboardHeightChange` event，支持监听键盘高度改变。  
2.`新增` 聊天记录模式新增切换表情面板/键盘demo。  
3.`优化` 键盘弹出占位添加动画效果。  
## 2.7.0（2024-01-07）
2024新年快乐！！祝大家在新的一年里工作顺利，事事顺心！  
1.`新增` 全新的聊天记录模式设计！将vue中的聊天记录模式与nvue中对齐，完全解决了聊天记录模式滚动到顶部加载更多在vue中抖动的问题，同时将聊天记录模式键盘自动弹出自动上推页面交由`z-paging`处理，解决了由此引发的各种问题，尤其是在微信小程序中导航栏被键盘顶出屏幕外的问题。如果您使用了`z-paging`的聊天记录模式，强烈建议更新，写法有一定变更，具体请参见demo。  
2.`新增` `swiper-demo`新增`onShow`时候调用reload演示。  
3.`修复` 修复滚动相关方法在微信小程序中首次滚动动画无效的问题。  
4.`修复` props设置单位，单位为px时报错的问题。  
5.`修复` 在某些情况下`z-paging`加载了但是未渲染时，reload无效的问题。  
6.`修复` 底部loading动画未生效的问题。  
