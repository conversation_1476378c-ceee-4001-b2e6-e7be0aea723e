<template>
	<view class="page">
		<view class="header">
			<view class="box-bg">
				<view class="back" @tap="goBack">
					 <image src="https://file.36sm.cn/xtjyjt/images/common/bback.png" style="width: 22rpx;height: 42rpx;"></image>
				</view>
				<view class="inputView">
					<view class="search" @tap="SearchPro" style="margin-right: 20rpx;">
						<image src="https://file.36sm.cn/xtjyjt/images/common/search.png" style="width: 28rpx;height: 28rpx;" ></image>
					</view>
					 <!-- <u--input :placeholder="$t('accountUser.qslgjc')" border="none" clearable  v-model="searchValue" @keyenter="onEnterPress"></u--input> -->
					 <input placeholder="请输入搜索内容" border="none" clearable  v-model="searchValue" @confirm="onEnterPress"></input>
					<!-- <image src="https://file.36sm.cn/xtjyjt/images/common/camera.png" style="width: 44rpx;height: 35rpx;" ></image> -->
				</view>
				<view class="" @click="">
					<text>搜索</text>
				</view>
			</view>
		</view>
		<view class="bottom" v-if="visible">
			<!-- <view class="history" v-if="searchHistory.length > 0">
				<view class="search-oprate">
					<text class="histitle">搜索历史</text>
					<image src="https://file.36sm.cn/xtjyjt/images/common/news/delete.png" class="icons-delete" mode=""></image>
				</view>
				<view class="history-main">
					<view class="history-content" v-for="(history,id) in searchHistory" :key="id" @tap="goSearch(history.contentName)">
						<text>{{history.contentName}}</text>
					</view>
				</view>
			</view> -->
			<view class="hot">
				<view class="hottop">
					<text class="hottitle">{{$t('search.rmss')}}</text>
					<image src="https://file.36sm.cn/xtjyjt/images/common/shuaxin.png" mode="" style="width: 34rpx; height: 30rpx;"></image>
				</view>
				<view class="hot-main">
					<view class="hot-content" v-for="(item,index) in OneList" :key="index">
						<view class="hot-title" @click="goSearch(item)">
							{{item.name}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
	import api from '@/common/request/index.js'
	import {
		getJyInfo,
		getBannerList,
		getOneList,
		getMarket,
		getRecommend
	} from "@/common/request/api/home.js"
	import {
			onLoad,
			onShow,
			onReachBottom,
			onShareAppMessage,
			onShareTimeline
		} from "@dcloudio/uni-app"
	export default {
		data() {
			return {
				visible: false,
				searchValue: "",
				params:{
					size: 10,
					page: 1
				},
				type: 1,
				// searchHistory: uni.getStorageSync("historySearch") || [],
				searchHistory: [],
				hotList: [],
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				OneList: []
			};
		},
		components: {
		},
		async created() {
			// this.getSearchWord()
		  await	this.getListOne()
		},
		methods: {
			onEnterPress() {
				this.SearchPro()
			},
			goBack() {
				// uni.navigateBack()
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			SearchPro() {
				console.log(this.searchValue, '搜索的关键词')
				uni.navigateTo({
					url: `/pageo/search/searchA?keyvalue=${this.searchValue}`
				})
			},
			goSearch(item) {
				this.searchValue = item.name
				switch (item.codeId) {
				        case '1868939964314677248':
				            // 精选产品
				            uni.switchTab({
				                url: '/pages/digital-avatar/index'
				            });
				            break;
				        
				        case '1868940074188664832':
				            // 基地资源
				            uni.navigateTo({
				                url: `/pageg/base_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940183857131520':
				            // 项目资源
				            uni.navigateTo({
				                url: `/pageg/pro_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940296906207232':
				            // 教练资源
				            uni.navigateTo({
				                url: `/pageg/coash_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940386848862208':
				            // 供应商资源
				            uni.navigateTo({
				                url: `/pageg/supplier_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940485985431552':
				            // 道具资源
				            uni.navigateTo({
				                url: `/pageg/item_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940603698573312':
				            // 音乐资源
				            uni.navigateTo({
				                url: `/pageg/music_resources/index?title=${item.name}&codeId=${item.codeId}`
				            });
				            break;
				        
				        case '1868940881747374080':
				            // 足疗资源
				            uni.switchTab({
				                url: '/pages/platformPlan/platformPlan'
				            });
				            break;
				
				        case '1868940717515206656':
				            // 装备商城
				            // 此处可以根据需求填入跳转逻辑
						uni.navigateTo({
							url: '/pageo/order_List/order_List?name=活动商品&marketId=1861656108880404480'
						})
				            break;
				
				        case '1868941014429986816':
				            // 关于我们
				            // 此处可以根据需求填入跳转逻辑
							if(this.pdfurl !== '') {
								// uni.navigateTo({
								// 	url: `/pageg/webview/filePreview?url=${this.pdfurl}`
								// 	// url: `/pageu/pdfFileage/pdfFileage?url=${this.pdfurl}`
								// })
								this.showPDf()
							}
				            break;
				        
				        default:
				            // 其他情况
				            console.log('未知的 codeId:', item.codeId);
				            break;
				    }
				    console.log(item, 'itemitem');
				    console.log(item.codeId, item.name);
			},
			// 获取搜索关键词
			getSearchWord() {
				api({
				    url: `search/word/get/list/${this.type}?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
				    if (res.code === 200) {
						console.log(res.data.data, '搜索')
						this.hotList = [...res.data.data.hotList]
						this.searchHistory = [...res.data.data.historyList]
						this.$data.visible = true
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			},
			// 获取首页一级的商品类目
			getListOne() {
				getOneList().then(res => {
					console.log(res, '获取首页一级的商品类目')
					if (res.code == 200) {
						this.OneList = res.data.data.list;
						console.log(this.OneList, 'OneList')
						this.$data.visible = true
					}else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			
			},
		},
		onLoad(params) {
			console.log(params)
			// if(params.name) {
			// 	this.searchValue = params.name
			// 	this.SearchPro()
			// }
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
		},
		computed: {
			activeText() {
			}
		}
	}
</script>

<style>
	.page {
		/* background-color: #fff; */
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.header {
		padding-top: 70rpx;
		width: 100%;
		height: 110rpx;
		background-color: #FFFFFF;
		line-height: 110rpx;
		.box-bg {
			padding: 0 15rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.back{
				margin-top: 15rpx;
			}
			.inputView{
				width:80%;
				height: 70rpx;
				padding: 5rpx 20rpx;
				line-height: 80rpx;
				border-radius:40rpx;
				background-color: #eeeeee;
				display: flex;
				align-items: center;
				justify-content: space-between;
				input{
					width: 530rpx;
				}
			}
		}
		
	}
	
	.TopFixed{
		// height: 200rpx;
		// width: 100%;
		// position: fixed;
		// top: 0rpx;
		// z-index: 999;
		// background-color: #fff;
	}

	.bottom {
		width: 100%;
		height: max-content;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #FFFFFF;
		// background-color: skyblue;
		border-radius: 18rpx;
		// margin-top: 30rpx;
	}

	.history {
		width: 95%;
		display: flex;
		flex-direction: column;
		padding-top: 20rpx;

		.search-oprate {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #333333;
			font-style: normal;
			text-transform: none;
			
			.histitle{
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #333333;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.icons-delete {
				width: 42rpx;
				height: 42rpx;
			}
		}

		text {
			font-size: 25rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			color: #9E9E9E;
			line-height: 29rpx;
			letter-spacing: 1px;
			-webkit-background-clip: text;
		}

		.history-main {
			width: 100%;
			padding-top: 20rpx;
			display: flex;
			flex-wrap: wrap;

			.history-content {
				margin:0 20rpx 15rpx 0;
				background: #EEEEEE;
				border-radius: 92rpx 92rpx 92rpx 92rpx;

				text {
					width: 100rpx;
					font-size: 22rpx;
					padding: 0 10rpx;
					font-family: Source Han Sans, Source Han Sans;
					font-weight: 500;
					color: #333333;
					height: 52rpx;
					line-height: 52rpx;
					-webkit-background-clip: text;
				}
			}
		}
	}

	.hot {
		width: 95%;
		margin-top: 60rpx;
		display: flex;
		flex-direction: column;
		.hottitle{
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #333333;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}
		text {
			font-size: 25rpx;
			font-family: Source Han Sans, Source Han Sans;
			font-weight: 400;
			color: #9E9E9E;
			line-height: 29rpx;
			letter-spacing: 1px;
			-webkit-background-clip: text;
		}

       .hottop{
		   display: flex;
		   align-items: center;
		   justify-content: space-between;
	   }
		.hot-main {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			margin-top: 40rpx;
			.hot-content {
				width: 45%;
				margin:0 5rpx 15rpx 0;
				height: 55rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #333333;
				line-height: 55rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				
				.hot-title{
					
				}
				
			}

		}
	}
	
</style>
