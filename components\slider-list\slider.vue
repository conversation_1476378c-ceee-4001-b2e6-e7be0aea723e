<template>
	<view :class="theme_view">
		<view v-if="propData.length > 0" class="spacing-mb" style="width: 94%; margin: 0 auto;"
			:class="(propLeft ? 'swiper-left ' : '') + (propRight ? 'swiper-right ' : '')">
			<uni-swiper-dot class="uni-swiper-dot-box" :mode="propMode" :dots-styles="dotsStyles"
				@clickItem="click_item" :info="propData" :current="current">
				<swiper class="banner oh" :class="' banner-' + (propSize || 'default') + ' ' + propRadius"
					:autoplay="propData.length > 0" :duration="duration" :circular="circular" @change="swiper_change" :interval="interval"
					:current="swiperDotIndex">
					<swiper-item v-for="(item, index) in propData" :key="item.bannerId">
						<!-- <image src="https://file.36sm.cn/mkxxs/2024/12/17/1b0ec34c5a054fe3b2fbb6cdb1bd349c.png" mode="" v-show="onoff=='0'"></image>
						<image :src="item.imageUrl" mode="" @load="onoff='1'" v-show="onoff=='1'"></image> -->
						<image :src="item.imageUrl" mode=""></image>
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	export default {
		data() {
			return {
				onoff: 0,
				theme_view: app.globalData.get_theme_value_view(),
				circular: true,
				duration: 1000,
				interval: 3000,
				styleIndex: -1,
				current: 0,
				swiperDotIndex: 0,
				dotsStyles: {},
			};
		},

		components: {},
		props: {
			propData: {
				type: Array,
				default: [],
			},
			propSize: {
				type: String,
				default: 'default',
			},
			propRadius: {
				type: String,
				default: 'border-radius-main',
			},
			// 指示点 class 调整靠左
			propLeft: {
				type: Boolean,
				default: false,
			},
			// 指示点 class 调整靠右
			propRight: {
				type: Boolean,
				default: false,
			},
			// 轮播指示点分类 default/dot/round/nav/indexes
			propMode: {
				type: String,
				default: 'round',
			},
			// 未选择指示点背景色
			propBackgroundColor: {
				type: String,
				default: '#fff',
			},
			// 指示点宽度 在 mode = nav、mode = indexes 时不生效
			propWidth: {
				type: Number,
				default: 6,
			},
			// 指示点距 swiper 底部的高度
			propBottom: {
				type: Number,
				default: 10,
			},
			// 未选择指示点边框样式
			propBorder: {
				type: String,
				default: '0',
			},
			// 指示点前景色，只在 mode = nav ，mode = indexes 时生效
			propColor: {
				type: String,
				default: '#fff',
			},
			// 已选择指示点背景色，在 mode = nav 时不生效
			propSelectedBackgroundColor: {
				type: String,
				// default: '#F55D3C',
				default: '#2A3A88',
			},
			// 已选择指示点边框样式，在 mode = nav 时不生效
			propSelectedBorder: {
				type: String,
				default: '0',
			},
		},
		beforeMount() {
			this.dotsStyles = {
				backgroundColor: this.propBackgroundColor,
				width: this.propWidth,
				bottom: this.propBottom,
				border: this.propBorder,
				color: this.propColor,
				selectedBackgroundColor: this.propSelectedBackgroundColor,
				selectedBorder: this.propSelectedBorder,
			};
		},
		methods: {
			onSuccessImg(item) {
				console.log('成功')
				item.isshow = true
			},
			onErrorImg(item) {
				console.log('失败')
				item.isshow = false
			},
			swiper_change(e) {
				// 原始index
				this.current = e.detail.current;
				// 当前滑index
				// this.currentIndex = tmpCurrent == this.propData.length - 1 ? 0 : tmpCurrent + 1;
				this.$emit('changeBanner', this.propData[this.current].bg_color);
			},
			click_item(e) {
				this.swiperDotIndex = e;
			},
			banner_event(e) {
				// app.globalData.operation_event(e);
				console.log(e)
				uni.navigateTo({
					url: `/pageg/goods-detail/goods-detail?productId=${e.detailId}&type=1`
				})
			},
		},
	};
</script>
<style>
	.banner {
		transform: translateY(0);
	}

	.banner image {
		min-width: 100%;
	}

	.banner-mini,
	.banner-mini image {
		height: 300rpx !important;
	}

	.banner-default,
	.banner-default image {
		height: 340rpx !important;
	}

	.banner-max,
	.banner-max image {
		height: 420rpx !important;
	}
	
	.banner-middle,
	.banner-middle image {
		height: 320rpx !important;
	}

	/**
	 * 指示点 左右定位
	 */
	.swiper-left /deep/ .uni-swiper__dots-box {
		justify-content: start;
		padding-left: 24rpx;
	}

	.swiper-right /deep/ .uni-swiper__dots-box {
		justify-content: end;
		padding-right: 24rpx;
	}
</style>