<template>
	<view class="water_fall">
		<custom-waterfalls-flow :value="transformedRecommendList" ref="waterfallsFlowRef" :column="column" :columnSpace="1" :seat="2">
			<view class="water_item" v-for="(item,index) in transformedRecommendList" :key="item.acId" slot="slot{{index}}">
			    <view class="water_detail">
			    	<!-- <view class="imageView">
			    		<image :src="item.image" mode="aspectFill" style="width: 35rpx;height: 360rpx;"></image>
			    	</view> -->
			    	<view class="good_detail">
			    		<view class="detail_title">
			    			完美日记双色腮红膏盘正品保湿提亮肤色裸妆腮红自然!保颜保湿......
			    		</view>
			    		<view class="price">
			    			<view class="coinname">
			    				 {{item.tradeCoinName}}
			    			</view>
			    			<view class="value">
			    				{{item.price}}
			    			</view>
			    		</view>
			    		<view class="saledetail">
			    			<view class="title">
			    				{{item.recommendReason}}
			    			</view>
			    			<view class="value">
			    				{{item.salesVolume}}
			    			</view>
			    			<image src="https://file.36sm.cn/xtjyjt/images/common/firehot.png" style="width: 18rpx;height: 18rpx;" mode="aspectFill"></image>
			    			
			    		</view>
			    	</view>
			    </view>
			</view>
		</custom-waterfalls-flow>
	</view>
	
</template>
<script>
	export default {
		data() {
			return {
				transformedRecommendList: [],
				column: 2
			}
		},
		created() {
			
		},
		props: {
			RecommendList: {
				type: Array
			}
		},
		methods: {
			transformData() {
				this.transformedRecommendList = this.RecommendList.map(item => ({
					image: item.recommendImageUrlSquare,
					title: '',
					recommendReason: item.recommendReason,
					id: item.acId,
					salesVolume: item.salesVolume,
					tradeCoinName: item.tradeCoinName,
					price: item.price
				}));
			}
		},
		watch: {
			RecommendList: {
				immediate: true,
				handler(newVal) {
					// 当 RecommendList 变化时的处理逻辑
					console.log(newVal, '推荐列表更新');
					if(newVal.length > 0) {
						this.transformedRecommendList = newVal.map(item => ({
							image: item.recommendImageUrlSquare,
							title: '',
							recommendReason: item.recommendReason,
							id: item.acId,
							salesVolume: item.salesVolume,
							tradeCoinName: item.tradeCoinName,
							price: item.price
						}));
					}
				}
			},
		},
	}

</script>

<style scoped lang="scss">
	.waterfall{
		width: 97%;
		margin: 0 auto;
		margin-top: 10rpx;
		display: flex;
		justify-content: space-around;
		.water_item{
			.waterdetail{
				width: 352rpx;
				min-height: 360rpx;
				background-color: #fff;
				border-radius: 10rpx;
				margin: 5rpx 0 10rpx 0;
xx				.good_detail{
					padding: 10rpx;
					.detail_title{
						 display: -webkit-box;
						  -webkit-box-orient: vertical;
						  -webkit-line-clamp: 2; /* 设置行数为2 */
						  overflow: hidden; /* 隐藏超出部分 */
						  text-overflow: ellipsis; /* 用省略号表示多余文本 */
						  font-family: PingFang SC, PingFang SC;
						  font-weight: 500;
						  font-size: 24rpx;
						  color: #333333;
						  line-height: 40rpx;
						  text-align: left;
						  font-style: normal;
						  text-transform: none;
					}
					.price{
						margin-top: 10rpx;
						height: 30rpx;
						line-height: 30rpx;
						color:#FF4000;
						font-size: 22rpx;
						display: flex;
						align-items: center;
						// justify-content: center;
						font-family: PingFang SC-Regular;
						font-weight: 400;
						text-align: center;
						font-style: normal;
						text-transform: none;
						.value{
							color:#FF4000;
							margin-left: 4rpx;
							font-size: 28rpx;
							font-weight: 600;
							font-family: DIN, DIN;
							font-weight: 600;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}
					.saledetail{
						height: 35rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						line-height: 35rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
						color:#a9a9a9;
						display: flex;
						align-items: center;
						font-size: 20rpx;
						letter-spacing: 3rpx;
						.value{
							margin:0 8rpx;
						}
						
					}
				}
			}
		}
	}
	
</style>