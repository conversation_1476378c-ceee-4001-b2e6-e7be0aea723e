<template>
	<u-overlay :show="show" @click="$emit('previewShow')" :opacity="0.9">
		<view class="w-100 warp">
			<u-swiper
			class="carousel"
			:list="imgList" 
			:current = "imgCurrent"
			height="750rpx"
			width="100vw"
			imgMode="aspectFit"
			@change="toggleImg" 
			:autoplay="false" 
			:indicatorStyle="indicatorStyle">
				<view slot="indicator">
					<text>{{ currentNum + 1 }}/{{ imgList.length }}</text>
				</view>
			</u-swiper>
			<view class="specification">{{ skuSpecList[currentNum] }}</view>	
		</view>
	</u-overlay>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: false,
			},
			productList: {
				type: Array,
				default: [],
			},
			imgCurrent: {
				type: Number,
				default: 0,
			}
		},
		data() {
			return {
				currentNum: 0,
				imgList:[],
				skuSpecList:[],
				indicatorStyle:{
					right:'40rpx',
					backgroundColor:'rgba(0,0,0,0.3)',
					color:'#ffffff',
					padding:"5rpx 10rpx",
					fontSize:'24rpx',
					borderRadius:'20rpx'
				}
			}
		},
		watch:{
			imgCurrent(val){
				this.currentNum = val
			},
			productList(val){
				this.imgList = val.map(item=>item.skuImageUrl)
				this.skuSpecList = val.map(item=>item.skuIntroductionList.join("/"))
			}
		},
		methods:{
			toggleImg(e){
				this.currentNum = e.current
				let skuIntroductionList = []
				skuIntroductionList = this.productList[e.current].skuIntroductionList
				this.$emit("toggleIndex",skuIntroductionList)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.warp {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		width: 100vw;
		flex-direction: column;
		.specification{
			margin-top: 60rpx;
			background-color: #FFFFFF;
			padding: 10rpx;
			border-radius: 10rpx;
		}
	}
	.carousel{
		width: 100vw !important;
	}
</style>