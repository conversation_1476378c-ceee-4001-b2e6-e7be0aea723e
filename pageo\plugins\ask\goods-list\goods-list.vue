<template>
    <view :class="theme_view">
        <view class="padding-main">
            <block v-if="data_list.length > 0">
                <view v-for="(item, index) in data_list" :key="index" class="bg-white border-radius-main padding-main oh" :class="data_list.length > index + 1 ? 'spacing-mb' : ''">
                    <view class="title flex-row jc-sb align-c wh-auto">
                        <view class="name flex-1 flex-width cr-base">{{ item.name }}的提问</view>
                        <view class="date cr-grey-9">{{ item.add_time_date }}</view>
                    </view>
                    <view class="question spacing-mt">
                        <navigator :url="item.url" hover-class="none" class="flex-row">
                            <view class="title cr-white tc margin-right-sm">问</view>
                            <view class="flex-1 flex-width">
                                <view class="fw-b">{{ item.content }}</view>
                                <view v-if="(item.images || null) != null && item.images.length > 0" class="avatar spacing-mt-10 radius margin-right-sm oh">
                                    <image v-for="(img, i) in item.images" class="wh-auto" :key="i" @tap="comment_images_show_event" :data-index="i" :data-ix="i + 1" :src="img" mode="aspectFit"></image>
                                </view>
                            </view>
                        </navigator>
                    </view>
                    <block v-if="item.is_reply == 1 || item.comments_count > 0">
                        <view class="ask flex-row spacing-mt">
                            <view class="title cr-white tc margin-right-sm">答</view>
                            <view class="flex-1 flex-width">
                                <block v-for="(it, ix) in item.comments_list" :key="ix">
                                    <block v-if="item.bool_more">
                                        <view class="cr-base br-b-f9 padding-bottom-main" :class="ix + 1 < item.comments_list.length ? 'margin-bottom-main' : ''">
                                            <block v-if="item.reply">
                                                {{ item.reply }}
                                            </block>
                                            <block v-else>
                                                {{ it.content }}
                                            </block>
                                        </view>
                                    </block>
                                    <block v-else>
                                        <view v-if="ix < 1" class="cr-base br-b-f9 padding-bottom-main" :class="ix + 1 < item.comments_list.length ? 'margin-bottom-main' : ''">
                                            <block v-if="item.reply">
                                                {{ item.reply }}
                                            </block>
                                            <block v-else>
                                                {{ it.content }}
                                            </block>
                                        </view>
                                    </block>
                                    <view v-if="(it.images || null) != null && it.images.length > 0" class="avatar spacing-mt-10 radius margin-right-sm oh">
                                        <image v-for="(img, i) in it.images" :key="i" class="wh-auto" @tap="comment_images_show_event" :data-index="i" :data-ix="i + 1" :src="img" mode="aspectFit"></image>
                                    </view>
                                </block>
                            </view>
                        </view>
                        <view class="more flex-row jc-e align-c spacing-mt">
                            <view v-if="(item.hide_more || false) === false" class="cr-red text-size-xs" @tap="open_more(item.id, index)">
                                <block v-if="!item.hide_comments_list_num"> 查看全部{{ item.comments_count }}个回答 </block>
                                <block v-else>查看更多</block>
                                <iconfont :name="item.bool_more ? 'icon-mendian-jiantou2' : 'icon-fenlei-top'" size="24rpx" prop-class="pr top-xs"></iconfont>
                            </view>
                            <view v-if="item.bool_more" class="cr-red text-size-xs margin-left-main" @tap="close_more(index)">
                                收起回答
                                <iconfont name="icon-fenlei-top" size="24rpx" prop-class="pr top-xs"></iconfont>
                            </view>
                        </view>
                    </block>
                </view>
                <!-- 结尾 -->
                <component-bottom-line :propStatus="data_bottom_line_status"></component-bottom-line>
            </block>
            <block v-else>
                <!-- 提示信息 -->
                <component-no-data :propStatus="data_list_loding_status"></component-no-data>
            </block>
        </view>
    </view>
</template>

<script>
    const app = getApp();
    import componentNoData from '@/components/no-data/no-data';
    import componentBottomLine from '@/components/bottom-line/bottom-line';

    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                data_list: [],
                data_total: 0,
                data_page_total: 0,
                data_page: 1,
                data_list_loding_status: 1,
                data_bottom_line_status: false,
                data_is_loading: 0,
                goods_id: null,
                comments_list: [],
            };
        },

        components: {
            componentNoData,
            componentBottomLine,
        },
        props: {},

        onLoad(params) {
            if (params || params.goods_id) {
                this.setData({
                    goods_id: params.goods_id,
                });
            }
        },

        onShow() {
            this.init();
        },

        // 下拉刷新
        onPullDownRefresh() {
            this.setData({
                data_page: 1,
            });
            this.get_data_list(1);
        },

        methods: {
            init() {
                var user = app.globalData.get_user_info(this, 'init');
                if (user != false) {
                    // 用户未绑定手机则转到登录页面
                    if (app.globalData.user_is_need_login(user)) {
                        uni.redirectTo({
                            url: '/pageo/login/login?event_callback=init',
                        });
                        return false;
                    } else {
                        // 获取数据
                        this.get_data_list();
                    }
                } else {
                    this.setData({
                        data_list_loding_status: 0,
                        data_bottom_line_status: false,
                    });
                }
            },
            get_data_list(is_mandatory) {
                // 分页是否还有数据
                if ((is_mandatory || 0) == 0) {
                    if (this.data_bottom_line_status == true) {
                        uni.stopPullDownRefresh();
                        return false;
                    }
                }

                // 是否加载中
                if (this.data_is_loading == 1) {
                    return false;
                }
                this.setData({
                    data_is_loading: 1,
                    data_list_loding_status: 1,
                });

                // 加载loding
                uni.showLoading({
                    title: '加载中...',
                });

                // 获取数据
                uni.request({
                    url: app.globalData.get_request_url('datalist', 'index', 'ask'),
                    method: 'POST',
                    data: {
                        page: this.data_page,
                        goods_id: this.goods_id,
                        is_comments: 1,
                    },
                    dataType: 'json',
                    success: (res) => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            if (res.data.data.data.length > 0) {
                                if (this.data_page <= 1) {
                                    var temp_data_list = res.data.data.data;
                                } else {
                                    var temp_data_list = this.data_list || [];
                                    var temp_data = res.data.data.data;
                                    for (var i in temp_data) {
                                        temp_data_list.push(temp_data[i]);
                                    }
                                }
                                this.setData({
                                    data_list: temp_data_list,
                                    data_total: res.data.data.total,
                                    data_page_total: res.data.data.page_total,
                                    data_list_loding_status: 3,
                                    data_page: this.data_page + 1,
                                    data_is_loading: 0,
                                });

                                // 是否还有数据
                                this.setData({
                                    data_bottom_line_status: this.data_page > 1 && this.data_page > this.data_page_total,
                                });
                            } else {
                                this.setData({
                                    data_list_loding_status: 0,
                                    data_is_loading: 0,
                                });
                            }
                        } else {
                            this.setData({
                                data_list_loding_status: 0,
                                data_is_loading: 0,
                            });
                            if (app.globalData.is_login_check(res.data, this, 'get_data_list')) {
                                app.globalData.showToast(res.data.msg);
                            }
                        }
                    },
                    fail: () => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_list_loding_status: 2,
                            data_is_loading: 0,
                        });
                        app.globalData.showToast('网络开小差了哦~');
                    },
                });
            },

            // 滚动加载
            scroll_lower(e) {
                this.get_data_list();
            },
            // 评价图片预览
            comment_images_show_event(e) {
                var index = e.currentTarget.dataset.index;
                var ix = e.currentTarget.dataset.ix;
                uni.previewImage({
                    current: this.data_list[index]['images'][ix],
                    urls: this.data_list[index]['images'],
                });
            },
            // 查看更多
            open_more(id, i) {
                var new_data_list = this.data_list;
                new_data_list[i].bool_more = true;
                if (new_data_list[i].bool_api === undefined || new_data_list[i].bool_api === true) {
                    // 加载loding
                    uni.showLoading({
                        title: '加载中...',
                    });
                    // 获取数据
                    uni.request({
                        url: app.globalData.get_request_url('commentsreplylist', 'index', 'ask'),
                        method: 'POST',
                        data: {
                            ask_id: id,
                            ask_comments_id: 0,
                            page: new_data_list[i].page || 1,
                            is_comments: 1,
                        },
                        dataType: 'json',
                        success: (res) => {
                            uni.hideLoading();
                            uni.stopPullDownRefresh();
                            if (res.data.code == 0) {
                                if (res.data.data.data.length > 0) {
                                    if ((new_data_list[i].page || 1) <= 1) {
                                        new_data_list[i].comments_list = res.data.data.data;
                                    } else {
                                        new_data_list[i].comments_list = new_data_list[i].comments_list.concat(res.data.data.data);
                                    }
                                    new_data_list[i].hide_comments_list_num = true;
                                    // 判断当前页数是否小于总页数，如果是则继续显示更多按钮，且当前页+1，如果不是则隐藏更多按钮
                                    if (res.data.data.page < res.data.data.page_total) {
                                        new_data_list[i].hide_more = false;
                                        new_data_list[i].page = (new_data_list[i].page || 1) + 1;
                                    } else {
                                        new_data_list[i].hide_more = true;
                                    }
                                    new_data_list[i].page_total = res.data.data.page_total;
                                    new_data_list[i].bool_api = true;
                                    this.setData({
                                        data_list: new_data_list,
                                    });
                                }
                            } else {
                                if (app.globalData.is_login_check(res.data, this, 'get_data_list')) {
                                    app.globalData.showToast(res.data.msg);
                                }
                            }
                        },
                        fail: () => {
                            uni.hideLoading();
                            app.globalData.showToast('网络开小差了哦~');
                        },
                    });
                } else {
                    // 查看更多是否调用接口
                    new_data_list[i].bool_api = true;
                    // 是否隐藏更多查看按钮
                    new_data_list[i].hide_more = false;
                    if (new_data_list[i].page < new_data_list[i].page_total) {
                        // 是否隐藏更多查看按钮
                        new_data_list[i].hide_more = false;
                    } else {
                        // 是否隐藏更多查看按钮
                        new_data_list[i].hide_more = true;
                    }
                    this.setData({
                        data_list: new_data_list,
                    });
                }
            },
            // 收起更多
            close_more(i) {
                var new_data_list = this.data_list;
                // 查看更多是否调用接口
                new_data_list[i].bool_api = false;
                // 是否隐藏更多查看按钮
                new_data_list[i].hide_more = false;
                // 是否展示更多内容
                new_data_list[i].bool_more = false;
                // 显示查看更多的数量
                new_data_list[i].hide_comments_list_num = false;
                this.setData({
                    data_list: new_data_list,
                });
            },
        },
    };
</script>
<style scoped>
    @import './goods-list.css';
</style>
