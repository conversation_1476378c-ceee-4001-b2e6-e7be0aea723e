<template>
	<view class="page">
		<view class="header" :style="{ 'lineHeight': navBarHeight + 'rpx' }">
			<view class="box-bg" @click="goBack">
				<view class="back">
					<image src="https://file.36sm.cn/xtjyjt/images/common/bback.png"
						style="width: 22rpx;height: 33rpx;"></image>
				</view>
				<view class="viewtitle">
					<text>订单详情</text>
				</view>
				<view class="">
				</view>
			</view>
		</view>
		<view class="OrderDetail">
			<!-- 预约订单才展示店铺信息 -->
			<view class="shopinfo" v-if="orderDettail.typeOrder == 0">
				<view class="shopde">
					<view class="topinfo">
						<view class="leftV">
							<text class="placeName">
								{{shopDetail.placeName}}
							</text>
							<view class="distance">
								<image src="/static/images/navgo.png" mode=""
									style="width: 28rpx;height: 28rpx;margin-right: 4rpx;">
								</image>
								<view class="">
									<text>{{shopDetail.jlInfo}}</text>
								</view>
							</view>
						</view>
						<view class="rightV" @click="callphone">
							<image src="/static/images/phone.png" mode="" style="width: 50rpx;height: 50rpx"></image>
						</view>
					</view>

					<view class="bottominfo">
						<view class="yysj">
							<view class="busineTime">
								<text>{{formatTime(shopDetail.extendDt1)}}--{{formatTime(shopDetail.extendDt2)}}</text>
							</view>
							<view class="line"></view>
							<view class="nearest">
								<text>就近</text>
							</view>
						</view>

						<view class="shpDetail" @click="openlocation(shopDetail)">
							<view class="content">{{shopDetail.adDetails}}</view>
							<image src="https://file.36sm.cn/jaxinfo/2025/02/28/0920510eaf034e358723153d96f6c89b.png"
								mode="" style="width: 48rpx;height: 48rpx;"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="orderinfo" v-if="orderDettail.orderStatus ==4 && orderDettail.codeBase64">
				<view class="Storeinfo">
					<view class="storeName">
						<view class="name">
							<text>核销码</text>
						</view>
					</view>
				</view>
				<view class="baseImg" style="display: flex;align-items: center;justify-content: center;">
					<!-- <image :src="baseImg" style="width: 400rpx;height: 400rpx;"></image> -->
					<image :src="'data:image/jpg;base64,' + baseImg" style="width: 400rpx;height: 400rpx;"></image>
				</view>
			</view>

			<view class="orderinfo">
				<view class="Storeinfo">
					<view class="storeName">
						<view class="name">
							<text>订单信息</text>
						</view>
					</view>
				</view>
				<view class="mxList">
					<view class="mxitem" v-for="(items,i) in orderDettail.mxList" :key="i">
						<view class="mxdetail">
							<view class="">
								<image :src="items.productMainImg" mode=""
									style="width: 150rpx; height: 150rpx;border-radius: 50%;">
								</image>
							</view>
							<view class="right">
								<view class="righttop">
									<view class="proname">
										{{items.productTitle}}
									</view>
									<!-- <view class="serviceTime">
										<text>服务开始倒计时: 48小时16分</text>
									</view> -->
								</view>
								<view class="rightcenter">
									<view class="">
										<text>{{items.extendDt1 || '预约的日期数据'}}</text>
									</view>
									<!-- 商品类型的订单显示数量 -->
									<view class="sellQuantity" v-if="orderDettail.typeOrder == 1">
										<text>× {{items.sellQuantity}}</text>
									</view>
								</view>
								<view class="skudetail">
									<view class="sku">
										{{items.extendDt2 || '预约的时间数据'}}
									</view>
									<view class="proPrice">
										￥ {{items.payOrgPriceTrade}}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="totalPrice" v-if="item.orderStatus == 1">
						<text class="statusName">{{item.orderStatusName}}:</text>
						<text class="pricevalue">￥ {{item.payAmountTrade}}</text>
					</view>

					<view class="totalPrice" v-if="orderDettail.typeOrder == 1">
						<text class="statusName">{{orderDettail.orderStatusName}}:</text>
						<text class="pricevalue">￥ {{orderDettail.payAmountTrade}}</text>
					</view>
				</view>
				<view class="totalPrice" v-if="orderDettail.orderStatus == 1">
					<text class="statusName">{{orderDettail.orderStatusName}}:</text>
					<text class="pricevalue">￥ {{orderDettail.payAmountTrade}}</text>
				</view>

			</view>

			<view class="payinfo">
				<view class="infopay">
					<view class="paytitle">
						<view class="name">
							<text>支付信息</text>
						</view>
					</view>
					<view class="ordernum">
						<view class="title">
							<text>订单编号</text>
						</view>
						<view class="rightN" @click="CopyID(orderDettail.orderId)">
							<text>{{orderDettail.orderId}}</text>
							<image src="https://file.36sm.cn/jaxinfo/2025/02/26/593681d8bf134f65826e97ed0557721f.png"
								mode="" style="width: 30rpx;height: 30rpx;margin-left: 10rpx;"></image>
						</view>
					</view>
					<view class="paytype">
						<view class="title">
							<text>支付方式</text>
						</view>
						<view class="rightN">
							<text>微信支付</text>
						</view>
					</view>

					<view class="paytype">
						<view class="title">
							<text>付款金额</text>
						</view>
						<view class="rightN">
							<text>￥{{orderDettail.payAmountTrade}}</text>
						</view>
					</view>
					<view class="paytime">
						<view class="title">
							<text>支付时间</text>
						</view>
						<view class="rightN">
							<text>{{orderDettail.payTime || orderDettail.createTime}}</text>
						</view>
					</view>
					<view class="paytype" v-if="orderDettail.ddRemarks">
						<view class="title">
							<text>订单备注</text>
						</view>
						<view class="orderremarks">
							<text>{{orderDettail.ddRemarks || '无'}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="servicePro">
				<view class="CDetail">
					<view class="protitle">
						<view class="name">
							<text>服务流程</text>
						</view>
					</view>
					<view class="processlist">
						<view class="process">
							<view class="processDetail">
								<view class="step-item" v-for="(item,index) in stepList" :key="index">
									<view class="stepImg">
										<image :src="item.img" mode="" style="width: 35rpx;height: 35rpx;"></image>
									</view>
									<view v-if="index < stepList.length - 1" class="line"></view>
								</view>
							</view>
							<view class="steps-label">
								<view v-for="(item, index) in stepList" :key="index" class="step-item">
									<view class="step-name">
										<text v-if="index !== 2">{{ item.name }}</text>
										<text v-if="index == 2 && orderDettail.typeOrder == 1">拿货</text>
										<text v-if="index == 2 && orderDettail.typeOrder !== 1">推拿</text>


									</view>
								</view>
							</view>
						</view>
						<view class="bottomTitle">
							<text>见安多年品质承诺: 对症无效, 一键退款</text>
						</view>
					</view>
				</view>
			</view>
			<view style="height: 80rpx;">

			</view>
		</view>
	</view>
</template>

<script>
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				navBarHeight: '',
				orderDettail: {},
				// 门店id
				placeId: '',
				visible: false,
				shopDetail: {},
				stepList: [{
						name: '预约',
						step: 1,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/529a4fc7bfc84214af0342e9966427da.png'
					},
					{
						name: '到店',
						step: 2,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/d4f39f3a30354475a0b70681c8d8af3c.png'
					},
					{
						name: '推拿',
						step: 3,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/45ecce1e4ebf40e1bb89b2e360019a4c.png'
					},
					{
						name: '评价',
						step: 4,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/4e039232fff843a095ca5dc77fb74fba.png'
					}
				],
				// 用来鉴定  当前页面是 从支付订单过来的详情还是 个人中心订单列表过来的1
				isDetail: 0,
				baseImg: '',
				adLatitude: uni.getStorageSync('adLatitude'),
				adLongitude: uni.getStorageSync('adLongitude')
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		onLoad(opt) {
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			if (opt.isDetail) {
				this.isDetail = opt.isDetail
			}
			console.log(opt, '路径参数')
			this.orderDettail = JSON.parse(decodeURIComponent(opt.orderDetail));
			console.log(this.orderDettail, '订单详情');
			if (this.orderDettail.typeOrder == 0) {
				// 预约订单  通过门店id 获取门店信息
				this.placeId = this.orderDettail.adUserName
				// this.placeId = '1890236814091571200'
				this.getShopDetail(this.placeId)

			}
			if (this.orderDettail.orderStatus == 4) {
				this.baseImg = this.orderDettail.codeBase64.replace(/^data:image\/[a-zA-Z]+;base64,/, "");
			}
		},
		onShow() {},
		methods: {
			formatTime(dateString) {
				// 提取时间部分并返回
				const time = dateString.split(' ')[1]; // 获取时间部分
				return time.slice(0, 5); // 截取前五个字符（小时和分钟部分）
			},
			CopyID(id) {
				console.log(id, '复制的id')
				uni.setClipboardData({
					data: id,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success',
							duration: 1000
						});
					},
					fail: (err) => {
						console.log(err, '报错信息')
						uni.showToast({
							title: '复制失败，请重试',
							icon: 'none',
							duration: 1000
						});
					}
				});
			},
			base64ToFile(base64Str, fileName, callback) {
				// 去除base64前缀
				var index = base64Str.indexOf(',')
				var base64Str = base64Str.slice(index + 1, base64Str.length)

				plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
					fs.root.getFile(fileName, {
						create: true
					}, function(entry) {

						// 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
						var fullPath = entry.fullPath;

						let platform = uni.getSystemInfoSync().platform
						if (platform == 'android') {
							var Base64 = plus.android.importClass("android.util.Base64");
							var FileOutputStream = plus.android.importClass("java.io.FileOutputStream");
							try {
								var out = new FileOutputStream(fullPath);
								// 此处Base64.decode有长度限制，如果不能满足需求，可以考虑换成官方原生插件市场的【Base64转文件】
								var bytes = Base64.decode(base64Str, Base64.DEFAULT);
								out.write(bytes);
								out.close();
								// 回调  
								callback && callback(entry.toLocalURL());
							} catch (e) {
								console.log(e.message);
							}
						} else if (platform == 'ios') {
							var NSData = plus.ios.importClass('NSData');
							var nsData = new NSData();
							nsData = nsData.initWithBase64EncodedStringoptions(base64Str, 0);
							if (nsData) {
								nsData.plusCallMethod({
									writeToFile: fullPath,
									atomically: true
								});
								plus.ios.deleteObject(nsData);
							}
							// 回调  
							callback && callback(entry.toLocalURL());
						}
					})
				})
			},
			goBack() {
				if (this.isDetail) {
					uni.navigateBack({
						success: () => {},
						fail: (err) => {
							uni.reLaunch({
								url: "/pages/home/<USER>"
							})
						}
					})
				} else {
					uni.navigateTo({
						url: '/pageu/user-order/user-order'
					});
				}
			},
			getShopDetail(id) {
				const params = {
					adLongitude: this.adLongitude,
					adLatitude: this.adLatitude
				}
				api({
					url: `tx/place/id/info/${id}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					setTimeout(() => {
						this.$data.visible = true
					}, 1500)
					if (res.code == 200) {
						console.log(res.data.data, '店铺详情数据')
						this.shopDetail = res.data.data
						console.log(this.shopDetail, '店铺详情数据')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			openlocation(detail) {
				// 打开门店详情
				uni.openLocation({
					latitude: detail.adLatitude,
					longitude: detail.adLongitude,
					scale: 18,
					success() {
						console.log('打开地图成功')
					},
					fail(error) {
						uni.showToast({
							title: '地图打开失败',
							icon: "none"
						});
					}
				})
			},
			callphone() {
				if (this.shopDetail.contactsInfoList.length == 0) {
					uni.showToast({
						title: '暂无联系电话',
						icon: "none"
					});
					return
				}
				uni.makePhoneCall({
						phoneNumber: this.shopDetail.contactsInfoList[0].phone
					})
					.catch((e) => {
						// console.log (e)
					});
			}
		}

	}
</script>

<style>
	.page {
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.header {
		width: 100%;
		padding-top: 110rpx;
		height: 60rpx;
		background-color: #FFFFFF;
		position: fixed;
		z-index: 999;

		.box-bg {
			height: 40rpx;
			line-height: 40rpx;
			width: 94%;
			margin: 0 auto;
			// background-color: skyblue;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.viewtitle {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.OrderDetail {
		height: calc(100vh - 180rpx);
		overflow-y: auto;
		width: 94%;
		margin: 0 auto;
		margin-top: 190rpx;

		.shopinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.shopde {
				width: 94%;
				margin: 0 auto;
				padding: 25rpx 0;

				.topinfo {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 80rpx;

					// background-color: skyblue;
					.leftV {
						display: flex;
						align-items: center;

						.placeName {
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 32rpx;
							color: #000;
							font-style: normal;
							text-transform: none;
							margin-right: 10rpx;
						}

						.distance {
							display: flex;
							align-items: center;
						}
					}
				}

				.bottominfo {
					margin-top: 25rpx;

					.yysj {
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;

						.busineTime {
							color: #b8b8b8;
						}

						.line {
							width: 4rpx;
							height: 16rpx;
							background: #E2DED4;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
							margin: 0 8rpx;
						}

						.nearest {
							width: 84rpx;
							height: 32rpx;
							line-height: 32rpx;
							border-radius: 24rpx;
							border: 2rpx solid #FF6900;
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 26rpx;
							color: #FF6900;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}

					.shpDetail {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.content {
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 28rpx;
							color: #b8b8b8;
							// background-color: skyblue;
							font-style: normal;
							text-transform: none;
							width: 600rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}
			}
		}

		.orderinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;
			padding: 0 0 25rpx 0;

			.Storeinfo {
				width: 94%;
				margin: 0 auto;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #ededed;

				.storeName {
					display: flex;
					align-items: center;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.orderstatus {
					color: #ccc;
					font-size: 25rpx;
					letter-spacing: 2rpx;
					font-weight: 300;
				}
			}

			.mxList {
				.mxitem {
					width: 94%;
					margin: 0 auto;
					padding: 25rpx 0;
					// border-bottom: 1rpx solid #ededed;

					.mxdetail {
						display: flex;
						justify-content: space-between;

						.right {
							flex: 1;
							margin-left: 15rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;

							.righttop {
								font-weight: 600;
								color: #000;
								display: flex;
								justify-content: space-between;
								margin-bottom: 10rpx;

								.proname {
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.serviceTime {
									font-family: PingFang SC, PingFang SC;
									font-weight: 600;
									font-size: 28rpx;
									color: #FF6900;
									line-height: 40rpx;
									font-style: normal;
									text-transform: none;
								}
							}

							.rightcenter {
								display: flex;
								justify-content: space-between;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 28rpx;
								color: #999999;
								font-style: normal;
								text-transform: none;
							}

							.skudetail {
								display: flex;
								justify-content: space-between;
								margin-top: 10rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;

								.sku {
									font-family: PingFang SC, PingFang SC;
									font-weight: 600;
									font-size: 24rpx;
									color: #555;
									font-style: normal;
									text-transform: none;
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

							}

							.bottomC {
								color: #ccc;
								font-weight: 300;
							}
						}
					}
				}

				.totalPrice {
					margin: 0 auto;
					width: 94%;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					padding-top: 5rpx;

					.statusName {
						margin-right: 8rpx;
					}

					.pricevalue {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}
				}
			}

			.showMore {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #c1c1c1;
			}



			.OrderTime {
				display: flex;
				align-items: center;
				padding: 0 30rpx;

				.title {
					color: #ccc;
					margin-right: 20rpx;
				}
			}

			.totalPrice {
				margin: 0 auto;
				padding-top: 25rpx;
				width: 94%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-bottom: 2rpx;

				.statusName {
					margin-right: 8rpx;
				}

				.pricevalue {
					font-weight: 600;
					font-family: PingFang SC, PingFang SC;
					font-size: 28rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
				}
			}

			.zfbtn {
				margin: 0 auto;
				width: 94%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;

				.qxdd {
					width: 100rpx;
					margin-right: 20rpx;
				}

				.ljzf {
					padding: 10rpx 20rpx;
					color: #FF6900;
					border-radius: 94rpx;
					border: 2rpx solid #FF6900;
				}
			}
		}

		.payinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.infopay {
				padding: 0 0 15rpx 0;
				margin: 0 auto;
				width: 94%;

				.paytitle {
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					// border-bottom: 1rpx solid #ededed;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.ordernum,
				.paytype,
				.paytime {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 60rpx;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #bababa;
						font-style: normal;
						text-transform: none;
					}

					.rightN {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}

					.orderremarks {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						max-width: 500rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						// background-color: skyblue;
					}
				}

				.ordernum {
					.rightN {
						display: flex;
						align-items: center;
					}
				}
			}
		}

		.servicePro {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;

			.CDetail {
				width: 94%;
				margin: 0 auto;

				.protitle {
					height: 80rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.name {
						color: #000;
						font-weight: 600;
						margin-right: 12rpx;
						max-width: 400rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis
					}
				}

				.processlist {
					width: 100%;
					margin: 0 auto;

					.process {
						// border-bottom: 1rpx solid #ededed;
						// background-color: skyblue;
						padding: 0 0 20rpx 0;

						.processDetail {
							display: flex;
							justify-content: space-between;
							align-items: center;

							.step-item {
								position: relative;
								text-align: center;
								flex: 1;
								display: flex;
								justify-content: center;

								.stepImg {
									width: 44rpx;
									height: 44rpx;
									border-radius: 50%;
									display: flex;
									justify-content: center;
									align-items: center;
									font-size: 22rpx;
									font-weight: bold;
									margin-bottom: 10rpx;
								}

								.line {
									height: 2px;
									border-bottom: 3rpx dashed #ccc;
									margin-left: 10px;
									width: 60rpx;
									left: 70%;
									top: 45%;
									position: absolute;
								}
							}
						}

						.steps-label {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.step-item {
								width: 25%;
								display: flex;
								justify-content: center;

								.step-name {
									padding: 5rpx 10rpx;
									border-radius: 30rpx;
									color: #fff;
									background: #FF6900;
									font-size: 22rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									color: #FFFFFF;
									font-style: normal;
									text-transform: none;
								}
							}
						}
					}

					.bottomTitle {
						width: 80%;
						margin: 0 auto;
						border-top: 1rpx solid #ededed;
						padding: 20rpx 0;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #999999;
						font-style: normal;
						text-transform: none;
						letter-spacing: 2rpx;
					}
				}
			}
		}
	}
</style>