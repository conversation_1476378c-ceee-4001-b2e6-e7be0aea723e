<template>
	<view>
		<view class="headerTwo" :style="{ 'padding-top': navBarHeight + 9 + 'px' }">
			<div class="navbar">
				<view class="" @click="back()">
					<image src="https://file.36sm.cn/xtjyjt/images/common/bback.png" mode="" style="width: 26rpx;height: 48rpx;">
					</image>
				</view>
				<view class="title">
					文件预览
				</view>
				<view class="title1" style="width: 80rpx;"> </view>
			</div>
		</view>
		<view class="" style="padding-top: 200rpx;">
			<view class="">
				附件:  {{schedule}}%
			</view>
			
			<view class="preview_box" @click="previewFile()">点击预览</view>
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				navBarHeight: 0,
				pdfurl: "",
				schedule: 0, //下载进度1-100%
			}
		},
		onLoad(params) {
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度 
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			console.log(params, 'params')
			if(params.url) {
				this.pdfurl = params.url
				this.previewFile()
				// uni.downloadFile({
				//   url: this.pdfurl,
				//   success: function (res) {
				//     var filePath = res.tempFilePath;
				//     uni.openDocument({
				//       filePath: filePath,
				//       showMenu: true,
				//       success: function (res) {
				//         console.log('打开文档成功');
				//       }
				//     });
				//   }
				// });
			}
		},
		methods: {
			back() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				})
			},
			previewFile() {
				// 流程步骤: 利用下载文件功能 先生成临时文件 - 调用api打开临时文件里面的内容,从而生成预览的效果,但是这个未能真实的下载到本地
				uni.showLoading({
					title: '正在打开...'
				});
				this.schedule = 0; //载入进度为0
				const downloadTask = uni.downloadFile({
					url: this.pdfurl, // 图片或者文件地址
					success: function(res) {
						console.log('下载的res', res);
						var filePath = res.tempFilePath; // 临时文件存储路径
						//  文件打开文件预览
						uni.openDocument({
							filePath: encodeURI(filePath),
							success: function(res) {
								uni.hideLoading();
								console.log('打开文档成功');
							},
							fail: function(err) {
								uni.hideLoading();
								uni.showToast({
									title: '打开失败',
									duration: 1500,
									icon: 'none'
								});
								console.log('打开失败');
							}
						});
					},
					fail: function(err) {
						console.log('下载失败原因', err);
						uni.hideLoading();
						uni.showModal({
							title: '您需要授权相册权限',
							success(res) {
								if (res.confirm) {
									uni.openSetting({
										success(res) {},
										fail(res) {
											console.log(res);
										}
									});
								}
							}
						});
					}
				});
				downloadTask.onProgressUpdate(res => {
					// console.log('下载进度' + res.progress);
					this.schedule = res.progress
				});
			}
	},
}
</script>

<style lang="scss" scoped>
	.headerTwo {
		width: 100%;
		height: 77rpx;
		// background-color: #f8f8f8;
		position: fixed;
		z-index: 9999;
		// position: absolute;
		top: 0rpx;
		background-color: #fff;

		.navbar {
			height: 48rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 10rpx;
			box-sizing: border-box;

			image {
				margin-left: 35rpx;
			}

			.title {
				// width: 470rpx;
				font-size: 34rpx;
				// font-weight: 800;

			}
		}

		.NavTab {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 35rpx 10px 10px 10px;
			background-color: #fff;
		}

	}
</style>
