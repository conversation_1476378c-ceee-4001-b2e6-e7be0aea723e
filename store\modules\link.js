import http from "@/common/http/http.js"
const state ={
	digitalBottom: [],
}
const getters ={
	
}
const mutations={
	get_digital_codeId(state) {
		http({
			url: "v1/vd/code/get/two/list",
			method: "GET",
			data: {
				parentName: "数字分身"
			},
		}).then(res => {
			if (res.code == 200) {
				state.digitalBottom = res.data.data.list
			} else {
				uni.showToast({
					title: res.msg,
					icon: "none"
				})
			}
		}).catch(err => {
			uni.showToast({
				title: err.msg,
				icon: "none"
			})
		})
	},
	
	get_shoplist(state) {
		http({
			url: `tx/place/get/list?page=1&size=1`,
			header: {
				"jarepair-platform": uni.getStorageSync("token")
			},
			method: "GET"
		}).then(res => {
			if (res.code == 200) {
				const data = res.data.data.list
			} else {
				uni.showToast({
					title: res.msg,
					icon: "none"
				})
			}
		}).catch(err => {
			uni.showToast({
				title: err.msg,
				icon: "none"
			})
		})
	}
}
const actions = {
	
}