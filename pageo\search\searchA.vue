<template>
	<view class="page">
		<view class="header">
			<view class="box-bg">
				<view class="back" @click="goBack">
					 <image src="https://file.36sm.cn/xtjyjt/images/common/bback.png" style="width: 22rpx;height: 42rpx;"></image>
				</view>
				<view class="inputView">
					<view class="search" @click="SearchPro">
						<image src="https://file.36sm.cn/xtjyjt/images/common/search.png" style="width: 28rpx;height: 28rpx;" ></image>
					</view>
					<input type="text" :placeholder="$t('accountUser.qslgjc')" v-model="searchValue" @confirm="onEnterPress"/>
					<image src="https://file.36sm.cn/xtjyjt/images/common/camera.png" style="width: 44rpx;height: 35rpx;" ></image>
				</view>
				
				<view class="tabIcon" @tap="useagreement">
					<image src="https://file.36sm.cn/xtjyjt/images/common/yhxy.png" mode="" style="width: 34rpx;height: 34rpx;"></image>
				</view>
			</view>
			<view class="select">
				<view class="selectitem">
					<!-- <text>综合</text> -->
					<text>{{$t('search.zh')}}</text>
					<image src="https://file.36sm.cn/xtjyjt/images/common/upsel.png" mode="" style="width: 22rpx;height: 12rpx; margin-left: 5rpx;"></image>
				</view>
				<view class="selectitem">
					<!-- <text>价格</text> -->
					<text>{{$t('search.jg')}}</text>
				</view>
				<view class="selectitem">
					<!-- <text>销量</text> -->
					<text>{{$t('search.xl')}}</text>
					<view>
						
					</view>
				</view>
				<view class="selectitem" @tap="changeType">
					<image :src="goodsType == 'one' ? 'https://file.36sm.cn/xtjyjt/images/common/one.png' : 'https://file.36sm.cn/xtjyjt/images/common/double.png'" mode="" style="width: 28rpx;height: 28rpx;"></image>
				</view>
			</view>
		</view>
			<view v-if="visible">
				<scroll-view class="scroll-container" scroll-y="true"  @scrolltolower="onscrollBottom">
				 <view class="SeaProList">
					<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
						:use-page-scroll="true" :auto-show-back-top-top="true">
						<refresh-loading slot="refresher"></refresh-loading>
						<view class="question">
							<image src="https://file.36sm.cn/xtjyjt/images/common/question.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
							<text style="margin-left: 10rpx; color: #aaa;font-size: 22rpx;">{{$t('search.ssjgbzq')}}</text>
							<text style="margin-left: 15rpx;color: #0F82E0;font-size: 24rpx;">{{$t('search.jjfa')}}</text>
						</view>
							<view class="onegoodlist" v-if="goodsType == 'one'">
								<view class="oneitem" v-for="item in SearchProList" :key="item.productCode">
									<view class="prodetail" @tap="goProdetail(item)">
										<image :src="item.productMainImg" mode=""></image>
										<view style="padding: 0 10rpx;">
											<view class="title">
												{{item.productTitle}}
											</view>
											<view class="USD">
												USD
												<view class="price">
													{{item.productSellPrice}}
												</view>
											</view>
											<view class="salemonth">
												30天销量
												<view class="value">
													{{item.monthSold}}
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="twogoodlist" v-if="goodsType == 'double'">
								<view class="twoitem" v-for="item in SearchProList" :key="item.productCode">
									<view class="prodetail" @tap="goProdetail(item)">
										<view class="left">
											<image :src="item.productMainImg" mode=""></image>
										</view>
										<view class="right">
											<view class="protitle">
												<u--text :lines="2" :text="item.productTitle"></u--text>
											</view>
											<view class="numprice">
												<view class="USD">
													USD
													<view class="price">
														{{item.productSellPrice}}
													</view>
												</view>
												<view class="salemonth">
													30天销量
													<view class="value">
														{{item.monthSold}}
													</view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="noOrder" v-if="SearchProList.length == 0">
								<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode="" style="width: 200rpx;height: 200rpx;"></image>
								<text class="notitle">暂无商品~</text>
							</view>
						<!-- 结尾 -->
						<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
						<component-bottom-line  :propMsg="$t('accountUser.mysjl')" v-else></component-bottom-line>
						
					</z-paging>
				</view>
				</scroll-view>
			</view>
         <cc-gifLoading v-if="!visible"
		    	gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
				
		<uni-popup ref="infoPopup" type="center" :is-mask-click="false">
		   	<view class="InfoPopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<!-- <text>搜索结果不准确，解决方案</text> -->
						<text>{{$t('search.ssjgbzqjjfa')}}</text>
		   			</view>
					<view class="content">
						<view class="text1">
							<!-- <text>尊敬的客户您好,见安修脚技术团队已收到您的反馈~</text> -->
							<text>{{$t('search.zjdkf')}}</text>
						</view>
						<view class="text1">
							<!-- <text>您可以使用「图搜」功能搜索您想要的商品，会更加准确哦</text> -->
							<text>{{$t('search.nkysy')}}</text>
						</view>
						<view class="text1">
							<!-- <text>搜索结果不准确的原因:可能由于机器翻译不准确导致.我们会人工丰富翻译词库,以提高后续搜索成功率</text> -->
							<text>{{$t('search.sssyts')}}</text>
						</view>
						<view class="text1">
							<!-- <text>非常感谢您信任见安修脚!</text> -->
							<text>{{$t('search.fcgxxr')}}</text>
						</view>
						<view class="text1">
							<!-- <text>如有问题请联系客服</text> -->
							<text>{{$t('search.rywtlxkf')}}</text>
							<image src="https://file.36sm.cn/xtjyjt/images/common/kefu.png" mode="" style="width: 40rpx;height: 40rpx;margin-left: 20rpx;"></image>
						</view>
					</view>
					<view class="bottombtn">
						<u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.wzdl')"  @tap="closepopup"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>


	</view>
	<!-- <showLoading v-else></showLoading> -->
</template>

<script>
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import {
			onLoad,
			onShow,
			onReachBottom,
			onShareAppMessage,
			onShareTimeline
		} from "@dcloudio/uni-app"
	export default {
		data() {
			return {
				visible: false,
				// 下拉触底加载效果
				loadingStatus: true,
				searchValue: "",
				params:{
					size: 10,
					page: 1
				},
				SearchProList: [],
				goodsType: 'one',
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
			};
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		onReachBottom() {
			// console.log('上拉触底')
			// this.loadingStatus = true
			// this.params.page ++
		},
		methods: {
			moveHandle() {},
			useagreement() {
				this.$refs.infoPopup.open()
			},
			closepopup() {
				this.$refs.infoPopup.close()
			},
			goProdetail(item) {
				const token = uni.getStorageSync("token")
				if(!token) {
					uni.showToast({
						title: "请先登录!",
						icon: 'none'
					})
					// setTimeout(() => {
						uni.navigateTo({
							url: '/pageo/login/login',
						})
					// }, 1000);
					return
				}
				uni.navigateTo({
					url: `/pageo/product_details/product_details?acid=${item.productCode}`
				})
			},
			onscrollBottom(){
				console.log('上拉触底111')
				this.loadingStatus = true
				this.params.page ++
				this.searchList()
			},
			queryList(){
				// this.SearchProList = []
				this.params.page = 1
				this.params.size = 10
				this.searchList()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			changeType() {
				this.goodsType = this.goodsType === 'one' ? 'double' : 'one'
			},
			goBack() {
				// uni.navigateBack()
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			onEnterPress() {
				this.SearchPro()
			},
			SearchPro() {
				// this.SearchProList = []
				this.params.page = 1
				this.params.size = 10
				console.log(this.searchValue, '关键词')
				this.searchList()
			},
			// 商品关键词搜索
			searchList() {
				api({
				    url: `goods/search/get/list/?keyWords=${this.searchValue}&location=${this.location}&page=${this.params.page}&size=${this.params.size}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'get'
				}).then(res => {
				    if (res.code === 200) {
						this.$data.visible = true
						console.log(res.data.data, '搜索')
						if(this.SearchProList.length == res.data.data.totalCount) {
							// 已获取到全部的数据
							setTimeout(() => {
								 // loading
								this.loadingStatus = false
							}, 1500)
						} else {
							if(this.params.page == 1) {
								this.SearchProList = [...res.data.data.list]
							} else {
							  this.SearchProList = [...this.SearchProList, ...res.data.data.list]
							}
							console.log(this.SearchProList, '查询到的数据')
							setTimeout(() => {
								 // loading
								this.loadingStatus = false
							}, 1500)
						}
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			}
		},
		created() {
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
		},
		onLoad(params) {
			console.log(params, '参数参数')
			this.searchValue = params.keyvalue
			
			if(params.name) {
				this.searchValue = params.name
				this.SearchPro()
			}
			// setTimeout(() => {
			// 	this.$data.visible = true
			// }, 1200)
		},
		onShow() {
			this.$data.visible = false
			this.searchList() 
		},
		computed: {
			activeText() {
			}
		}
	}
</script>

<style>
	.page {
		/* background-color: #fff; */
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.header {
		width: 100%;
		height: 200rpx;
		background-color: #FFFFFF;
		line-height: 110rpx;
		position: fixed;
		z-index: 999;
		.box-bg {
			padding: 0 15rpx;
			margin-top: 70rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.back{
				margin-top: 15rpx;
			}
			.inputView{
				width:82%;
				height: 70rpx;
				padding: 5rpx 20rpx;
				line-height: 70rpx;
				border-radius:40rpx;
				background-color: #eeeeee;
				display: flex;
				align-items: center;
				justify-content: space-between;
				input{
					width: 500rpx;
				}
			}
		}
		.select{
			height: 86rpx;
			line-height: 86rpx;
			width: 100%;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin-top: 5rpx;
			.selectitem{
				// color: #FF4000;
			}
		
		}
	}

	
	.scroll-container{
		margin-top: 280rpx;
		height: calc(100vh - 280rpx);
		overflow-y: auto;
		.SeaProList{
			// background-color: #eeeeee;
			width: 100%;
			.question{
				width: 96%;
				margin: 0 auto;
				height: 90rpx;
				display: flex;
				align-items: center;
				text{
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 22rpx;
					color: #AAAAAA;
					font-style: normal;
					text-transform: none;
				}
			}
			
			.onegoodlist{
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: space-between;
				padding: 0 10rpx;
				.oneitem{
					width: 49%;
					background-color: #fff;
					border-radius: 15rpx;
					margin-bottom: 15rpx;
					.prodetail{
					   image{
						border-top-right-radius: 15rpx;
						border-top-left-radius: 15rpx;
					   	width: 360rpx;
					   	height: 350rpx;
					   }
					   .title{
						    display: -webkit-box;
						    -webkit-box-orient: vertical;
						    -webkit-line-clamp: 2; /* 设置行数为2 */
						    overflow: hidden; /* 隐藏超出部分 */
						    text-overflow: ellipsis; /* 用省略号表示多余文本 */
						    font-weight: 400;
						    font-size: 24rpx;
							letter-spacing: 1rpx;
						    color: #333333;
						    line-height: 40rpx;
						    text-align: left;
						    font-style: normal;
						    text-transform: none;
					   }
					   .USD{
						   margin-top: 10rpx;
						   height: 30rpx;
						   line-height: 30rpx;
						   color:#FF4000;
						   font-size: 22rpx;
						   display: flex;
						   align-items: center;
						   // justify-content: center;
						   font-family: PingFang SC-Regular;
						   font-weight: 300;
						   text-align: center;
						   font-style: normal;
						   text-transform: none;
						   .price{
							   color:#FF4000;
							   margin-left: 4rpx;
							   font-size: 28rpx;
							   font-weight: 600;
							   font-family: DIN, DIN;
							   font-weight: 600;
							   text-align: center;
							   font-style: normal;
							   text-transform: none;
						   }
					   }
					   
					   .salemonth{
						   height: 35rpx;
						   font-family: PingFang SC, PingFang SC;
						   font-weight: 400;
						   line-height: 35rpx;
						   text-align: left;
						   font-style: normal;
						   text-transform: none;
						   color:#a9a9a9;
						   display: flex;
						   align-items: center;
						   font-size: 20rpx;
						   letter-spacing: 3rpx;
						   .value{
								margin:0 8rpx;
							   }
					   }
					}
				}
			}
			.twogoodlist {
				.twoitem{
					background-color: #fff;
					margin-bottom: 15rpx;
					.prodetail{
						height: 260rpx;
						padding: 0 20rpx;
						display: flex;
						align-items: center;
						.left{
							image{
								border-top-right-radius: 15rpx;
								border-top-left-radius: 15rpx;
								width: 235rpx;
								height: 235rpx;
							}
						}
						.right{
							height: 80%;
							margin-left: 20rpx;
							.numprice{
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								.USD{
									margin-top: 20rpx;
									height: 30rpx;
									line-height: 30rpx;
									color:#FF4000;
									font-size: 22rpx;
									display: flex;
									align-items: center;
									font-family: PingFang SC-Regular;
									font-weight: 300;
									text-align: center;
									font-style: normal;
									text-transform: none;
									.price{
										color:#FF4000;
										margin-left: 4rpx;
										font-size: 28rpx;
										font-weight: 600;
										font-family: DIN, DIN;
										font-weight: 600;
										text-align: center;
										font-style: normal;
										text-transform: none;
									}
								}
								
								.salemonth{
									height: 35rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									line-height: 35rpx;
									text-align: left;
									font-style: normal;
									text-transform: none;
									color:#a9a9a9;
									display: flex;
									align-items: center;
									font-size: 20rpx;
									letter-spacing: 3rpx;
									.value{
										margin:0 8rpx;
									}
								}
							}
						}
					}
				}
			}
			
			.noOrder{
				height: 800rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.notitle{
					margin-top: 50rpx;
					color: #ccc;
					letter-spacing: 2rpx;
				}
			}
		}
	}
	
	
	.InfoPopup{
		width: 600rpx;
		min-height: 750rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 99999;
		margin-bottom: -80rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				height: 150rpx;
				line-height: 150rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
			.content{
				.text1{
					line-height: 1.5;
					margin-bottom: 20rpx;
					letter-spacing: 2rpx;
					text{
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}
					
					display: flex;
					align-items: center;
				}
			}
			
			.bottombtn{
				margin-top: 50rpx;
			}
		}
	}
</style>