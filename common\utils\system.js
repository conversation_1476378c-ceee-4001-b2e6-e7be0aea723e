
// 用来计算小程序导航栏相关尺寸

// 获取系统信息
const SYSTEM_INFO = uni.getSystemInfoSync();

// 返回设备栏的高度
export const getStatusBarHeight = ()=> SYSTEM_INFO.statusBarHeight || 15;

// 获取标题栏的高度
export const getTitleBarHeight = ()=>{
	if(uni.getMenuButtonBoundingClientRect){
		let {top,height} = uni.getMenuButtonBoundingClientRect();
		return height + (top - getStatusBarHeight())*2		
	}else{
		return 40;
	}
}

// 获取导航栏的总高度
export const  getNavBarHeight = ()=> getStatusBarHeight()+getTitleBarHeight();

// 计算左侧图标的左侧位置
export const getLeftIconLeft = ()=> {
	// #ifdef MP-TOUTIAO
		let {leftIcon:{left,width}}  = tt.getCustomButtonBoundingClientRect();
		return left+ parseInt(width);
	// #endif
	
	// #ifndef MP-TOUTIAO
		return 0
	// #endif	
}