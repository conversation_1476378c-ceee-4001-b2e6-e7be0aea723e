/*
 * 优惠劵
 */
.plugins-coupon-container .item {
    overflow: hidden;
    height: 180rpx;
}

.plugins-coupon-container .v-left {
    width: calc(100% - 140rpx);
    padding: 30rpx 0 30rpx 20rpx;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.plugins-coupon-container .v-left .base {
    color: #D2364C;
}

.plugins-coupon-container .v-left .base .symbol {
    font-family: Verdana, Tahoma;
    font-weight: 700;
}

.plugins-coupon-container .v-left .base .price {
    font-weight: 700;
    font-family: arial;
    font-size: 76rpx;
}

.plugins-coupon-container .v-left .base .unit {
    margin-left: 5rpx;
}

.plugins-coupon-container .v-left .base .desc {
    margin-left: 20rpx;
}

.plugins-coupon-container .v-left .base-tips,
.plugins-coupon-container .v-left .base-time {
    margin-top: 10rpx;
}

.plugins-coupon-container .v-right {
    background: #d2364c;
    width: 140rpx;
    height: 180rpx;
    color: #fff;
    font-weight: 500;
    position: relative;
    text-align: center;
}

.plugins-coupon-container .v-right:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.plugins-coupon-container .v-right .circle {
    display: block;
    position: absolute;
    left: -1px;
    top: -3px;
    width: 3px;
    height: 180rpx;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAACpCAYAAADur4c3AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3NpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo3MjUzYzIwOS04ZWNlLTRlNTctODQ4OC01ZDExOTkwOGNkYmMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTM1QzgxREZGRDI5MTFFNTg3QjhGRUQ1MDY5OURERUQiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTM1QzgxREVGRDI5MTFFNTg3QjhGRUQ1MDY5OURERUQiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChNYWNpbnRvc2gpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NTJiNzVkOGUtZDc2Yi00MzEzLWFmNmYtYTJkNTRlYTI4YTY1IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjcyNTNjMjA5LThlY2UtNGU1Ny04NDg4LTVkMTE5OTA4Y2RiYyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pvy+vnQAAAEqSURBVHjaYvz//z8DDDAxIAFyOVeBOAHEYfyPMDsLmXMfmfMT2YADDP8h4CEQq4A4aUDMA1LNSKZDXwJxGcg1yJaWIXOeInO+IxuwA+acK0AsA+IEADEbic7hhPOAer4DcQcQMyNb2oLMeYVsADcyZwPMObuBWBTEsQFpI9E54sjO+QvEc0F+YoHKJgHxJ2TnvEM2gBmZswrmnA1AzAXiaJPhHC1k58BNQ3bBTGTOR2QD/iJzFsH8Mw/kHxBHggzn2KA7BxzWyC5Yisz5imwACmc2LLY7QbEN4nCS4ZwAIGZFds5lUEpEdsF6nKn3PTJnAsiAV0BcBsSM5GamFCDmQXYOOJ8iu2Anzrz9HKU8ABlwDYgTKcnbo0XNaFEzWtQgipqOYVLUAAQYAKPWa4c8cIHnAAAAAElFTkSuQmCC) no-repeat;
}

.plugins-coupon-container .item-disabled .v-right {
    background: #dfdfdf !important;
    color: #c0c0c0 !important;
    cursor: no-drop !important;
}

/**
 * 标签
 */
.plugins-label {
    z-index: 2;
    box-sizing: border-box;
    white-space: initial;
    max-width: 100%;
}

.plugins-label-text {
    padding: 10rpx 10rpx 0 10rpx;
}

.plugins-label .lv:not(:last-child) {
    margin-right: 10rpx;
}

.plugins-label .lv {
    margin-bottom: 10rpx;
}

.plugins-label-img.plugins-label-bottom-left .lv,
.plugins-label-img.plugins-label-bottom-center .lv,
.plugins-label-img.plugins-label-bottom-right .lv {
    margin-bottom: 0;
    margin-top: 10rpx;
}

.plugins-label .lv view {
    padding: 4rpx 12rpx;
    -webkit-box-shadow: 0px 1px 2px -1px rgb(0 0 0 / 60%);
    box-shadow: 0px 1px 2px -1px rgb(0 0 0 / 60%);
}

.plugins-label-img image {
    width: 80rpx !important;
    height: 80rpx !important;
}

.plugins-label-top-left {
    left: 0;
    top: 0;
}

.plugins-label-top-center {
    left: 0;
    top: 0;
    width: 100%;
    text-align: center;
}

.plugins-label-top-right {
    top: 0;
    right: 0;
}

.plugins-label-bottom-left,
.plugins-label-bottom-center,
.plugins-label-bottom-right {
    bottom: calc(100% - 380rpx);
}

.plugins-label-bottom-left {
    left: 0;
}

.plugins-label-bottom-center {
    width: 100%;
    text-align: center;
}

.plugins-label-bottom-right {
    right: 0;
}

/**
 * 商品列表标签处理
 */
.goods-data-rolling-list .plugins-label-bottom-left,
.goods-data-rolling-list .plugins-label-bottom-center,
.goods-data-rolling-list .plugins-label-bottom-right {
    bottom: calc(100% - 240rpx) !;
}

.goods-data-list .plugins-label-bottom-left,
.goods-data-list .plugins-label-bottom-center,
.goods-data-list .plugins-label-bottom-right {
    bottom: 0;
}

/**
 * 灰度样式
 */
.grayscale {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
}


/**
 * 博客 - 列表
 */
.plugins-blog-list .blog-img {
    width: 200rpx !important;
    height: 170rpx !important;
}

.plugins-blog-list .base {
    width: calc(100% - 220rpx);
}

/*
 * 博客 - 滚动
 */
.plugins-blog-rolling-list swiper,
.plugins-blog-rolling-list .item .blog-img {
    height: 180rpx !important;
}

.plugins-blog-rolling-list .item .blog-title {
    left: 0;
    bottom: 0;
    width: calc(100% - 26rpx);
    background-color: rgba(0, 0, 0, .5);
}

/**
 * 博客 - 九方格
 */
.plugins-blog-grid-list .item {
    width: calc(33% - 10rpx);
    float: left;
    /* padding-bottom: 10rpx; */
    margin-bottom: 20rpx;
}

.plugins-blog-grid-list .item:nth-of-type(3n + 1) {
    margin-right: 10rpx;
}

.plugins-blog-grid-list .item:nth-of-type(3n) {
    margin-left: 10rpx;
}

.plugins-blog-grid-list .item .blog-img {
    width: 100%;
}

/**
* 门店 - 数据列表
*/
.plugins-realstore-data-list .item .address-content {
    width: calc(100% - 150rpx);
}

.plugins-realstore-data-list .item .address-distance {
    right: 20rpx;
    bottom: 18rpx;
}

.plugins-realstore-data-list .item .icon-list {
    right: 24rpx;
    top: 36rpx;
}

.plugins-realstore-data-list .item .icon-list .icon-item {
    width: 46rpx;
    height: 46rpx;
}

.plugins-realstore-data-list .item .icon-item .badge-icon {
    top: -10px;
    right: 2px;
}

.plugins-realstore-data-list .item .icon-list .icon-item {
    background: rgba(226, 44, 8, 0.08);
    border-radius: 8rpx;
    color: #E46248;
}

.plugins-realstore-data-list .item .icon-list .icon-item:not(:last-child) {
    margin-right: 26rpx;
}

.plugins-realstore-data-list .item .logo {
    width: 100rpx;
    height: 100rpx !important;
}

.plugins-realstore-data-list .item .base-right {
    padding-left: 14rpx;
}

.plugins-realstore-data-list .item .base-right .title {
    width: calc(100% - 110rpx);
}

/**
 * 多商户 - 数据列表
 */
.plugins-shop-data-list .item .logo {
    width: 100rpx;
    height: 100rpx !important;
}

.plugins-shop-data-list .item .item-right-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.plugins-shop-data-list .item .right-content {
    padding-left: 14rpx;
    margin-right: 50rpx;
}

.plugins-shop-data-list .item .auth-icon .icon {
    width: 28rpx;
    height: 28rpx !important;
    margin-right: 10rpx;
}

.plugins-shop-data-list .item .desc {
    min-height: 72rpx;
    line-height: 36rpx;
}