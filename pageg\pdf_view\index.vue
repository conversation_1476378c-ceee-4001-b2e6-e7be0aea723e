<template>
	<view class="page">
		<scroll-view class="contract" scroll-y="true">
			<image :src="qmitem.htUrl" mode="" class="contract-image" v-if="qmitem.sfqm == 0"></image>
			<image :src="qmitem.qmUrl" mode="" class="contract-image" v-if="qmitem.sfqm == 1"></image>
			<view class="" style="width: 100%;height: 100rpx;background-color: #fff;">
			</view>
		</scroll-view>

		<view class="signature">
			<view class="btnbtn">
				<u-button color="linear-gradient(to right,#FF6900  , #FF6900)" text="签名" @click="GoSignature"
					v-if="qmitem.sfqm == 0"
					:custom-style="{ borderRadius: '94rpx', height: '60rpx', width: '150rpx',color: '#fff' }"></u-button>
				<!-- v-if="!htUrl"></u-button> -->
				<u-button color="linear-gradient(to right,#000  , #000)" text="重签" @click="Verification"
					v-if="qmitem.sfqm == 1"
					:custom-style="{ borderRadius: '94rpx', height: '60rpx', width: '150rpx',color: '#fff' }"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qmitem: {
					htUrl: ''
				},
				navHeight: 0,
				htUrl: '',
				signImg: ''
			}
		},
		onLoad(params) {
			console.log(params, '路径参数')
			if (params.qmitem) {
				this.qmitem = JSON.parse(params.qmitem)
				console.log(this.qmitem, '详情')
				console.log(this.qmitem.htUrl, '合同链接')
				console.log(this.qmitem.qmUrl, '签名后合同链接')
			}
		},
		onShow() {
			console.log(this.signImg, 'signImg')
			if (this.signImg) {
				this.qmitem = JSON.parse(this.signImg)
				console.log(this.qmitem, '重签名后的数据')
				// this.qmitem.htUrl = this.signImg
			} else {
				// this.htUrl = ''
			}
		},
		methods: {
			gotopdf() {
				uni.navigateTo({
					url: `/pageg/webview/filePreview?url=${this.qmitem.htUrl}`
				})
			},
			GoSignature() {
				console.log('去签名')
				uni.navigateTo({
					url: `/pageg/pdf_view/signature?qmitem=${JSON.stringify(this.qmitem)}`
				})
			},
			Verification() {
				console.log('重新去签名')
				uni.navigateTo({
					url: `/pageg/pdf_view/signature?qmitem=${JSON.stringify(this.qmitem)}`
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}

	.contract {
		width: 100%;
		height: 100%;
		// height: calc(100vh - 50rpx);
		/* 设置可滚动区域的高度 */
		overflow-y: auto;
		/* 启用垂直滚动 */
		-webkit-overflow-scrolling: touch;
	}

	.contract-image {
		width: 100%;
		height: 100%;
		display: block;
		/* 消除图片底部间隙 */
		object-fit: cover;
		/* 保持比例同时填满宽度 */
		min-height: 100vh;
		/* 保持原始比例，不拉伸 */
		// object-fit: contain;
		/* 保持图片比例，避免裁剪 */
	}

	.signature {
		position: fixed;
		width: 40%;
		border-radius: 70rpx;
		padding: 10rpx 20rpx;
		color: #FFFFFF;
		bottom: 200rpx;
		z-index: 9;
		left: 50%;
		transform: translateX(-50%);
		font-weight: 600;
		font-size: 28rpx;

		.btnbtn {
			width: 100%;
			display: flex;
			justify-content: space-between;
		}

	}
</style>