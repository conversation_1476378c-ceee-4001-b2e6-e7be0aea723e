<template>
	<view class="container">
		<view class="sigh-btns">
			<button class="btn" @tap="handleCancel">取消</button>
			<button class="btn" @tap="handleReset">重写</button>
			<button class="btn" @tap="handleConfirm">确认</button>
		</view>
		<view class="sign-box">
			<canvas class="mycanvas" :style="{width:width +'px',height:height +'px'}" canvas-id="mycanvas"
				@touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>

			<canvas canvas-id="camCacnvs" :style="{width:height +'px',height:width +'px'}" class="canvsborder"></canvas>

		</view>
	</view>

</template>
<script>
	import api from '@/common/request/index.js'
	var x = 20;
	var y = 20;
	var tempPoint = []; //用来存放当前画纸上的轨迹点
	var id = 0;
	var type = '';
	let that;
	let canvasw;
	let canvash;
	var qmitem = {}
	export default {
		data() {
			return {
				ctx: '', //绘图图像
				points: [], //路径点集合,
				width: 0,
				height: 0,
				confirmLoading: false
			};
		},
		mounted() {},
		onLaunch() {
			wx.setEnableDebug({
				enableDebug: true
			}) // 强制开启调试
		},
		onLoad(option) {
			that = this;
			console.log(option, '路径参数');
			if (option.qmitem) {
				that.qmitem = JSON.parse(option.qmitem)

				console.log(that.qmitem, '参数参数参数')
			}
			this.ctx = uni.createCanvasContext('mycanvas', this); //创建绘图对象
			//设置画笔样式
			this.ctx.lineWidth = 4;
			this.ctx.lineCap = 'round';
			this.ctx.lineJoin = 'round';

			uni.getSystemInfo({
				success: function(res) {
					console.log(res);
					that.width = res.windowWidth * 0.8;
					that.height = res.windowHeight;
				}
			});
		},

		methods: {
			//触摸开始，获取到起点
			touchstart: function(e) {
				let startX = e.changedTouches[0].x;
				let startY = e.changedTouches[0].y;
				let startPoint = {
					X: startX,
					Y: startY
				};

				/* **************************************************
				    #由于uni对canvas的实现有所不同，这里需要把起点存起来
				 * **************************************************/
				this.points.push(startPoint);

				//每次触摸开始，开启新的路径
				this.ctx.beginPath();
			},

			//触摸移动，获取到路径点
			touchmove: function(e) {
				let moveX = e.changedTouches[0].x;
				let moveY = e.changedTouches[0].y;
				let movePoint = {
					X: moveX,
					Y: moveY
				};
				this.points.push(movePoint); //存点
				let len = this.points.length;
				if (len >= 2) {
					this.draw(); //绘制路径
				}
				tempPoint.push(movePoint);
			},

			// 触摸结束，将未绘制的点清空防止对后续路径产生干扰
			touchend: function() {
				this.points = [];
			},

			/* ***********************************************	
			#   绘制笔迹
			#   1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
			#   2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
			#   3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
			************************************************ */
			draw: function() {
				let point1 = this.points[0];
				let point2 = this.points[1];
				this.points.shift();
				this.ctx.moveTo(point1.X, point1.Y);
				this.ctx.lineTo(point2.X, point2.Y);
				this.ctx.stroke();
				this.ctx.draw(true);
			},

			handleCancel() {
				uni.navigateBack({
					delta: 1
				});
			},

			//清空画布
			handleReset: function() {
				console.log('handleReset');
				that.ctx.clearRect(0, 0, that.width, that.height);
				that.ctx.draw(true);
				tempPoint = [];
			},

			// 将签名的图片与 pdf图片进行合成
			PictureCompositing: function(imgurl) {
				const params = {
					htId: this.qmitem.htId,
					htUrl: this.qmitem.htUrl,
					qmDate: imgurl,
					orderId: this.qmitem.orderId
				}
				api({
					url: `goods/order/hx/ht/hc`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data
						console.log(data, '签名生成的图片')
						uni.showToast({
							title: '签名生成成功',
							icon: 'none'
						});
						// const signImg = data.htUrl
						setTimeout(() => {
							uni.hideLoading()

							const pages = getCurrentPages(); // 获取页面栈
							const prevPage = pages[pages.length - 2]; // 获取上一页的实例

							if (prevPage) {
								prevPage.$vm.signImg = JSON.stringify(data); // 将 signImg 传递给上一页
							}

							uni.navigateBack({
								success: () => {},
								fail: (err) => {
									uni.reLaunch({
										url: "/pages/home/<USER>"
									})
								}
							})
						}, 500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					this.confirmLoading = false

				}).catch(err => {
					console.log(err)
					uni.showToast({
						title: err.msg,
						icon: "none"
					});
					this.confirmLoading = false
				})
			},

			//将签名笔迹上传到服务器，并将返回来的地址存到本地
			handleConfirm: function() {
				if (tempPoint.length == 0) {
					uni.showToast({
						title: '请先签名',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (this.confirmLoading) {
					return
				}
				this.confirmLoading = true
				setTimeout(() => {
					this.confirmLoading = false
				}, 4000)
				uni.showLoading({
					title: '签名图片生成中，请耐心等候'
				})
				// 确保使用正确的this引用
				const that = this;
				uni.canvasToTempFilePath({
					canvasId: 'mycanvas',
					success: function(res) {
						let tempPath = res.tempFilePath;
						const ctx = uni.createCanvasContext('camCacnvs', that);

						// 添加旋转和绘制
						ctx.translate(0, that.width);
						ctx.rotate((-90 * Math.PI) / 180);
						ctx.drawImage(tempPath, 0, 0, that.width, that.height);

						// 关键修复：使用draw的回调确保绘制完成
						ctx.draw(true, () => {
							// 直接在这里执行后续操作，移除setTimeout
							uni.canvasToTempFilePath({
								canvasId: 'camCacnvs',
								success: function(res) {
									let path = res.tempFilePath;
									console.log('生成的图片资源', path);

									uni.uploadFile({
										url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
										filePath: path,
										name: "multipartFile",
										header: {
											"jarepair-platform": uni
												.getStorageSync("token")
										},
										success: (res) => {
											try {
												const data = JSON.parse(res
													.data);
												if (data.code == 200) {
													const Imgurl = data
														.data.data.fileUrl;
													that.PictureCompositing(
														Imgurl);
												}
											} catch (e) {
												console.error('JSON解析失败',
													e);
											}
											uni.hideLoading();
											that.confirmLoading = false;
										},
										fail: err => {
											console.error('上传失败', err);
											uni.hideLoading();
											that.confirmLoading = false;
										}
									});
								},
								fail: err => {
									console.error('canvasToTempFilePath失败', err);
									uni.hideLoading();
									that.confirmLoading = false;
								}
							});
						});
					},
					fail: err => {
						console.error('初始canvasToTempFilePath失败', err);
						uni.hideLoading();
						that.confirmLoading = false;
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	// .container {
	// 	display: flex;
	// 	flex-direction: row;
	// }

	// .sign-box {
	// 	width: 80%;
	// 	height: 90%;
	// 	margin: auto;
	// 	display: flex;
	// 	flex-direction: column;
	// 	text-align: center;
	// }

	// .sign-view {
	// 	height: 100%;
	// }

	// .sigh-btns {
	// 	margin: auto;
	// 	display: flex;
	// 	flex-direction: column;
	// 	justify-content: space-around;
	// 	// background-color: skyblue;
	// 	height: 600rpx;

	// 	.btn {
	// 		margin: auto;
	// 		// padding: 8rpx;
	// 		transform: rotate(90deg);
	// 		border: grey 1rpx solid;
	// 	}
	// }

	// .mycanvas {
	// 	margin: auto 0rpx;
	// 	background-color: #ececec;
	// }



	/* 修改后的样式 */
	.container {
		display: flex;
		flex-direction: row;
		height: 100vh;
		/* 新增 */
		overflow: hidden;
		/* 新增 */
	}

	.sign-box {
		width: 80%;
		height: 100vh;
		/* 修改 */
		margin: 0;
		/* 修改 */
		position: relative;
		/* 新增 */
	}

	.sigh-btns {
		margin: auto;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 60vh;
		/* 调整按钮容器高度 */
		touch-action: none;

		/* 新增 禁止触摸滚动 */
		.btn {
			margin: auto;
			// padding: 8rpx;
			transform: rotate(90deg);
			border: grey 1rpx solid;
		}
	}

	.mycanvas {
		margin: auto 0;
		background-color: #ececec;
		touch-action: none;
		/* 新增 */
	}


	.canvsborder {
		border: 1rpx solid #333;
		position: fixed;
		top: 0;
		left: 10000rpx;
	}
</style>