<template>
    <view :class="theme_view">
        <!-- 分类 -->
        <scroll-view v-if="(shop_category || null) != null && shop_category.length > 0" class="nav-base scroll-view-horizontal bg-white oh" scroll-x="true">
            <block v-for="(item, index) in shop_category" :key="index">
                <view :class="'item cr-grey dis-inline-block padding-horizontal-main ' + (nav_active_value == item.id ? 'cr-main nav-active-line fw-b' : '')" @tap="nav_event" :data-value="item.id">
                    {{ item.name }}
                </view>
            </block>
        </scroll-view>

        <!-- 列表 -->
        <scroll-view :scroll-y="true" class="scroll-box scroll-box-ece-nav plugins-shop-data-list" @scrolltolower="scroll_lower" lower-threshold="60">
            <view v-if="(data_list || null) != null && data_list.length > 0" class="data-list padding-horizontal-main padding-top-main oh">
                <block v-for="(item, index) in data_list" :key="index">
                    <view class="item border-radius-main bg-white oh spacing-mb">
                        <navigator :url="'/pages/plugins/shop/detail/detail?id=' + item.id" hover-class="none">
                            <image :src="item.logo_long" class="logo" mode="aspectFit"></image>
                            <view class="tc">
                                <view class="single-text padding-horizontal-main padding-top-main">
                                    <!-- 标题 -->
                                    <text class="fw-b text-size-md va-m">{{ item.name }}</text>
                                    <!-- 认证信息 -->
                                    <view v-if="(data_base.is_enable_auth || 0) == 1 && ((item.auth_type != -1 && (item.auth_type_msg || null) != null) || ((item.bond_status || 0) == 1 && (item.bond_status_msg || null) != null))" class="auth-icon dis-inline-block margin-left-sm">
                                        <!-- 实名认证 -->
                                        <block v-if="item.auth_type != -1 && (item.auth_type_msg || null) != null">
                                            <block v-if="item.auth_type == 0">
                                                <image :src="data_base.shop_auth_personal_icon" class="icon va-m" mode="aspectFill"></image>
                                            </block>
                                            <block v-if="item.auth_type == 1">
                                                <image :src="data_base.shop_auth_company_icon" class="icon va-m" mode="aspectFill"></image>
                                            </block>
                                        </block>
                                        <!-- 保证金认证 -->
                                        <block v-if="(item.bond_status || 0) == 1 && (item.bond_status_msg || null) != null">
                                            <image :src="data_base.shop_auth_bond_icon" class="icon va-m" mode="aspectFill"></image>
                                        </block>
                                    </view>
                                </view>
                                <view class="cr-grey padding-main">
                                    <text class="multi-text">{{ item.describe }}</text>
                                </view>
                                <view class="oh br-t-dashed padding-main">
                                    <view class="fl cr-grey-9 single-text"
                                        >商品 <text class="cr-black fw-b padding-left-sm">{{ item.goods_count }}</text></view
                                    >
                                    <view class="fr cr-grey-9 single-text"
                                        >销量 <text class="cr-black fw-b padding-left-sm">{{ item.goods_sales_count }}</text></view
                                    >
                                </view>
                            </view>
                        </navigator>
                    </view>
                </block>
            </view>
            <view v-else>
                <!-- 提示信息 -->
                <component-no-data :propStatus="data_list_loding_status" :propMsg="data_list_loding_msg"></component-no-data>
            </view>

            <!-- 结尾 -->
            <component-bottom-line :propStatus="data_bottom_line_status"></component-bottom-line>
        </scroll-view>
    </view>
</template>
<script>
    const app = getApp();
    import componentNoData from '../../../../components/no-data/no-data';
    import componentBottomLine from '../../../../components/bottom-line/bottom-line';
    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                data_list_loding_status: 1,
                data_list_loding_msg: '',
                data_bottom_line_status: false,
                data_is_loading: 0,
                data_list: [],
                data_total: 0,
                data_page_total: 0,
                data_page: 1,
                params: null,
                data_base: null,
                shop_category: [],
                nav_active_value: 0,
                // 自定义分享信息
                share_info: {},
            };
        },
        components: {
            componentNoData,
            componentBottomLine,
        },
        props: {},
        onLoad(params) {
            this.setData({
                params: params,
            });
            // 数据加载
            this.get_data();
        },
        // 下拉刷新
        onPullDownRefresh() {
            this.setData({
                data_page: 1,
            });
            this.get_data_list(1);
        },
        methods: {
            // 初始化
            get_data() {
                uni.showLoading({
                    title: '加载中...',
                });
                uni.request({
                    url: app.globalData.get_request_url('index', 'index', 'shop'),
                    method: 'POST',
                    data: {},
                    dataType: 'json',
                    success: (res) => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            var data = res.data.data;
                            this.setData({
                                data_base: data.base || null,
                                shop_category: data.shop_category || [],
                            });
                            if ((this.data_base || null) != null) {
                                // 基础自定义分享
                                this.setData({
                                    share_info: {
                                        title: this.data_base.seo_title || this.data_base.application_name,
                                        desc: this.data_base.seo_desc,
                                        path: '/pages/plugins/shop/index/index',
                                    },
                                });
                                // 导航名称
                                if ((this.data_base.application_name || null) != null) {
                                    uni.setNavigationBarTitle({
                                        title: this.data_base.application_name,
                                    });
                                }
                            }
                            // 获取列表数据
                            this.get_data_list(1);
                        } else {
                            this.setData({
                                data_list_loding_status: 2,
                                data_list_loding_msg: res.data.msg,
                            });
                        }
                        // 分享菜单处理
                        app.globalData.page_share_handle(this.share_info);
                    },
                    fail: () => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_list_loding_status: 2,
                            data_list_loding_msg: '网络开小差了哦~',
                        });
                    },
                });
            },
            // 获取数据列表
            get_data_list(is_mandatory) {
                // 分页是否还有数据
                if ((is_mandatory || 0) == 0) {
                    if (this.data_bottom_line_status == true) {
                        uni.stopPullDownRefresh();
                        return false;
                    }
                }
                // 是否加载中
                if (this.data_is_loading == 1) {
                    return false;
                }
                this.setData({
                    data_is_loading: 1,
                });
                // 加载loding
                uni.showLoading({
                    title: '加载中...',
                });
                // 获取数据
                uni.request({
                    url: app.globalData.get_request_url('shoplist', 'index', 'shop'),
                    method: 'POST',
                    data: {
                        page: this.data_page,
                        category_id: this.nav_active_value || 0,
                    },
                    dataType: 'json',
                    success: (res) => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            var data = res.data.data;
                            if (data.data.length > 0) {
                                if (this.data_page <= 1) {
                                    var temp_data_list = data.data;
                                } else {
                                    var temp_data_list = this.data_list || [];
                                    var temp_data = data.data;
                                    for (var i in temp_data) {
                                        temp_data_list.push(temp_data[i]);
                                    }
                                }
                                this.setData({
                                    data_list: temp_data_list,
                                    data_total: data.total,
                                    data_page_total: data.page_total,
                                    data_list_loding_status: 3,
                                    data_page: this.data_page + 1,
                                    data_is_loading: 0,
                                });
                                // 是否还有数据
                                this.setData({
                                    data_bottom_line_status: this.data_page > 1 && this.data_page > this.data_page_total,
                                });
                            } else {
                                this.setData({
                                    data_list_loding_status: 0,
                                    data_is_loading: 0,
                                });
                                if (this.data_page <= 1) {
                                    this.setData({
                                        data_list: [],
                                        data_bottom_line_status: false,
                                    });
                                }
                            }
                        } else {
                            this.setData({
                                data_list_loding_status: 0,
                                data_list_loding_msg: res.data.msg,
                                data_is_loading: 0,
                            });
                            app.globalData.showToast(res.data.msg);
                        }
                    },
                    fail: () => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_list_loding_status: 2,
                            data_is_loading: 0,
                        });
                        app.globalData.showToast('网络开小差了哦~');
                    },
                });
            },
            // 滚动加载
            scroll_lower(e) {
                this.get_data_list();
            },
            // 导航事件
            nav_event(e) {
                this.setData({
                    nav_active_value: e.currentTarget.dataset.value || 0,
                    data_page: 1,
                });
                this.get_data_list(1);
            },
        },
    };
</script>
<style>
    @import './index.css';
</style>
