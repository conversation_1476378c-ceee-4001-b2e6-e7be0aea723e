/**
* 排序导航
*/
.nav-sort-content .item {
    height: 80rpx;
    line-height: 80rpx;
    width: calc(20% - 33.33rpx);
}
.nav-sort-content .item .icon {
    width: 30rpx;
    height: 30rpx !important;
}
.screening-submit {
    width: 50rpx;
    height: 50rpx;
    top: 15rpx;
    right: 80rpx !important;
}
.show-type-submit {
    width: 50rpx;
    height: 50rpx !important;
    top: 15rpx;
    right: 16rpx;
}

/**
* 条件
*/
.search-map {
    height: calc(100vh - 160rpx);
    overflow-y: scroll;
    overflow-x: hidden;
}
.search-map,
.search-map .search-submit {
    width: 660rpx;
}
.map-keywords {
    padding: 0 20rpx;
    line-height: 66rpx;
    height: 66rpx;
    font-size: 26rpx;
    box-sizing: border-box;
}
.map-nav text:first-child {
    font-weight: bold;
}
.map-nav .arrow-bottom {
    top: 0;
    right: 10rpx;
    padding-right: 46rpx;
}
.map-item {
    line-height: 66rpx;
}
.map-content {
    line-height: 60rpx;
}
.map-content .item {
    margin-bottom: 20rpx;
}
.map-content .item:not(:last-child) {
    margin-right: 20rpx;
}
.map-text-item .item,
.map-images-text-item .item {
    padding: 0 15rpx;
    border: 1px solid transparent;
}
.map-images-text-item .item {
    vertical-align: middle;
    border: 1px solid #eee;
    width: 150rpx;
    height: 72rpx;
    line-height: 72rpx;
}
.map-images-text-item .item image {
    width: 150rpx;
    height: calc(100% - 8rpx);
    display: block;
    margin: 0 auto;
    margin-top: 4rpx;
}
.search-map .search-submit {
    left: 0;
    bottom: 0;
}
.search-map .search-submit button {
    height: 80rpx;
    line-height: 80rpx;
}