<template>
	<view class="page">
		<newNavBar title="我的" width='100vw' textSize="40rpx" iconLeft="none" textColor="#000"></newNavBar>
		<view class="homeView" v-if="haveToken">
			<view class="HomeTop">
				<image :src="userInfo.dataInfo1" mode=""
					style="width: 150rpx;height: 150rpx;border-radius: 20rpx;margin-right: 30rpx;"></image>
				<view class="rightInfo">
					<view class="name">
						<text>{{userInfo.dataInfo2}}</text>
					</view>
					<view class="Content">
						<text>{{userInfo.dataInfo3}}</text>
					</view>
				</view>
			</view>
			<view class="yeji">
				<view class="yjitem" v-for="(item,index) in yjdata" :key="index">
					<text class="title">{{item.title}}</text>
					<text class="value">{{item.nrInfo}}</text>
				</view>
			</view>
			<view class="select">
				<view class="title">
					<text>我的订单</text>
				</view>
				<u-tabs :list="orderType" @click="sectionChange" :current="current" keyName="name" lineWidth="30"
					lineColor="#FF6900" :activeStyle="{ color: '#FF6900', fontWeight: 'bold', transform: 'scale(1.05)'}"
					:inactiveStyle="{ color: '#333', transform: 'scale(1)'}"
					itemStyle="padding: 0 15rpx; height: 33px;"></u-tabs>
			</view>

			<view class="orderView" v-if="visible">
				<scroll-view class="scroll-container" scroll-y="true" @scrolltolower="onscrollBottom">
					<view class="SeaProList">
						<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
							:use-page-scroll="true" :auto="false" :auto-show-back-top-top="true">
							<refresh-loading slot="refresher"></refresh-loading>
							<view class="orderList" v-if="orderList.length > 0">
								<view class="orderitem" v-for="(item,index) in orderList" :key="item.orderId"
									@click="OrderDetail(item)">
									<view class="topheader">
										<view class="leftt">
										</view>
										<view class="topcen">
											<image src="/static/images/timeicon.png" mode=""
												style="width: 30rpx;height: 30rpx;"></image>
											<text class="title">{{item.dataInfo1}}</text>
										</view>
										<view class="ordername">
											<text>{{item.orderStatusName}}</text>
										</view>
									</view>
									<view class="bottomC">
										<view class="leftP">
											<view class="title">
												<text>项目</text>
											</view>
											<view class="name">
												<text>{{item.dataInfo2}}</text>
											</view>
										</view>
										<view class="centerP">
											<view class="title">
												<text>预定人</text>
											</view>
											<view class="name">
												<text>{{item.dataInfo3}}</text>
											</view>
										</view>
										<view class="rightP">
											<view class="title">
												<text>时长</text>
											</view>
											<view class="name">
												<text>{{item.dataInfo4}}</text>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="noOrder" v-if="orderList.length == 0">
								<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode=""
									style="width: 200rpx;height: 200rpx;"></image>
								<text class="notitle">{{$t('accountUser.zwdd')}}</text>
							</view>

							<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
							<component-bottom-line :propStatus="bottom_line_status" :propMsg="$t('accountUser.mysjl')"
								v-if="!loadingStatus && orderList.length > 0"></component-bottom-line>
						</z-paging>
					</view>
				</scroll-view>
			</view>
		</view>
		<TabBar @showPopup="handleShowPopup"></TabBar>
		<uni-popup ref="BtnPopup" type="bottom" :mask-click="false">
			<view class="PopupBtn">
				<view class="qrcode" @click="ScanCode">
					<text>扫码</text>
				</view>
				<view class="Billing" @click="billstart">
					<text>开单</text>
				</view>

				<view class="cancel" @click="closeBtnPopup">
					<text>取消</text>
				</view>
			</view>
		</uni-popup>
		<cc-gifLoading v-if="!visible"
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
	import {
		postCancelPaymentOrder,
		getShoppingOrder,
		getTypeOrder
	} from "@/common/request/api/goods.js"
	import api from '@/common/request/index.js'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import TabbarCom from '../../components/tabbar/tabbar.vue'
	export default {
		data() {
			return {
				onoff: 0,
				current: 0,
				statusBarHeight: 0,
				spProductName: '',
				visible: false,
				prolist: [],
				searchValue: '',
				// 下拉触底加载效果
				loadingStatus: true,
				orderShow: false,
				params: {
					page: 1,
					size: 10,
				},
				orderStatus: 4,
				// 全部
				bottom_line_status: false,
				loadingType: 1,
				butText: '',
				totalPage: 1,
				status: 'loadmore',
				navBarHeight: 0,
				yjdata: [],
				userInfo: {},
				orderType: [],
				orderList: [],
				haveToken: uni.getStorageSync("token")
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright,
			TabbarCom
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
			isOk() {
				return this.$store.state.tabbar.isOk
			}
		},
		created() {},
		onLoad(opt) {
			console.log(opt, '路径参数')
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			setTimeout(() => {
				this.visible = true
				this.loadingStatus = false
			}, 1200)
		},
		onShow() {
			console.log('onshow展示')
			this.params.page = 1
			this.params.size = 10
			const token = uni.getStorageSync("token")
			console.log(token, 'token  onshowoshow')
			if(token) {
			   	this.getLocation()
			}
			this.init()
			this.$store.dispatch('setIndex', 0)
		},
		methods: {
			closeBtnPopup() {
				this.$refs.BtnPopup.close()
				this.$store.dispatch('setIndex', 0)
			},
			ScanCode() {
				uni.scanCode({
					success: (res) => { // 改为箭头函数
						console.log(res.result, 'res');
						const data = JSON.parse(res.result);
						console.log(data, '信息信息');
						if (data.codeId) {
							this.closeBtnPopup(); // 这样 this 指向正确
							uni.navigateTo({
								url: `/pageg/oder_detail/order_detail?orderId=${data.codeId}&isQrcode=true`
							});
						}
					},
					fail: (err) => {
						console.log('扫码失败', err);
					},
					complete: () => {
						console.log('扫码结束');
					}
				});
			},
			handleShowPopup(value) {
				console.log(value)
				if (value) {
					// 底部弹出弹窗
					this.$refs.BtnPopup.open()
				}
			},
			OrderDetail(item) {
				uni.navigateTo({
					url: `/pageg/oder_detail/order_detail?orderId=${item.orderId}`
				})
			},
			billstart() {
				uni.navigateTo({
					url: `/pageg/shop_detail/index`
				})
				setTimeout(() => {
					this.closeBtnPopup()
				}, 2500)
			},
			queryList() {
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 500)
				this.params.page = 1
				this.params.size = 10
				this.getOrderList()
			},
			onscrollBottom() {
				console.log('上拉触底11111111')
				this.loadingStatus = true
				this.params.page++
				this.getOrderList()
			},
			// onReachBottom() {
			// 	console.log('上拉触底222')
			// },
			sectionChange(item) {
				console.log(item, 'item')
				this.params.page = 1
				this.current = item.index;
				this.orderStatus = item.orderStatus
				this.getOrderList()
			},
			async init() {
				await this.getYj()
				await this.getHteacher()
				await this.getOrderType()
				await this.getOrderList()
			},
			getYj() {
				api({
					url: `sx/get/yj/list?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						this.yjdata = [...res.data.data]
						console.log(this.yjdata, '业绩基本数据')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			getLocation() {
				let that = this
				// 获取用户是否开启 授权获取当前的地理位置、速度的权限。
				uni.getSetting({
					success(res) {
						console.log(res, '授权的权限信息')
						// 如果没有授权
						if (!res.authSetting['scope.userLocation']) {
							// 则拉起授权窗口
							uni.authorize({
								scope: 'scope.userLocation',
								success() {
									//点击允许后--就一直会进入成功授权的回调 就可以使用获取的方法了
									uni.getLocation({
										type: 'wgs84',
										success: function(res) {
											that.adLongitude = res.longitude
											that.adLatitude = res.latitude
											console.log(res, '位置信息')
											console.log('当前位置的经度：' + res.longitude)
											console.log('当前位置的纬度：' + res.latitude)
											uni.setStorageSync('adLatitude', res.latitude)
											uni.setStorageSync('adLongitude', res.longitude)
										},
										fail(error) {
											console.log('失败', error)
										}
									})
								},
								fail(error) {
									//点击了拒绝授权后--就一直会进入失败回调函数--此时就可以在这里重新拉起授权窗口
									console.log('拒绝授权', error)
									uni.showModal({
										title: '提示',
										content: '若点击不授权，将无法使用位置功能',
										cancelText: '不授权',
										cancelColor: '#999',
										confirmText: '授权',
										confirmColor: '#f94218',
										success(res) {
											console.log(res)
											if (res.confirm) {
												// 选择弹框内授权
												uni.openSetting({
													success(res) {
														console.log(res.authSetting)
													}
												})
											} else if (res.cancel) {
												// 选择弹框内 不授权
												console.log('用户点击不授权')
											}
										}
									})
								}
							})
						} else {
							// 有权限则直接获取
							uni.getLocation({
								type: 'wgs84',
								success: function(res) {
									console.log(res, '位置信息')
									that.adLongitude = res.longitude
									that.adLatitude = res.latitude
									console.log('当前位置的经度：' + res.longitude)
									console.log('当前位置的纬度：' + res.latitude)
									uni.setStorageSync('adLatitude', res.latitude)
									uni.setStorageSync('adLongitude', res.longitude)
								},
								fail(error) {
									uni.showToast({
										title: '请勿频繁调用！',
										icon: 'none',
									})
									console.log('失败', error)
								}
							})
						}
					}
				})

			},
			getHteacher() {
				api({
					url: `sx/get/member/info?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						this.userInfo = res.data.data

						console.log(this.userInfo, '首页基本个人基本信息')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			getOrderType() {
				api({
					url: `sx/get/order/type/list?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						this.orderType = res.data.data
						console.log(this.orderType, '订单状态类型')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			getOrderList() {
				api({
					url: `sx/get/order/list?orderStatus=${this.orderStatus}&page=${this.params.page}&size=${this.params.size}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					setTimeout(() => {
						this.visible = true
					}, 1200)
					if (res.code == 200) {
						const data = res.data.data
						console.log(data.list, '订单列表数据')
						if (this.orderList.length == data.totalCount) {
							if (this.params.page == 1) {
								this.orderList = [...data.list]
							}
							// 数据加载完全
							setTimeout(() => {
								this.loadingStatus = false
								this.bottom_line_status = true
							}, 400)


						} else {
							if (this.params.page == 1) {
								this.orderList = [...data.list]
							} else {
								this.orderList = [...this.orderList, ...data.list]
							}
							// 确保视图刷新
							this.$set(this, 'orderList', this.orderList);
							setTimeout(() => {
								this.loadingStatus = false
							}, 500)
							console.log(this.orderList, '订单列表')
						}

					} else {
						uni.showToast({
							title: res.msg || '请求超时',
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			}
		},
	}
</script>

<style>
	.page {
		/* background-color: #FBF5F1; */
		background: linear-gradient(180deg, #FFE8DA 0%, #FFFFFF 100%);
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.homeView {
		width: 90%;
		margin: 0 auto;

		.HomeTop {
			display: flex;

			.rightInfo {
				flex: 1;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
					line-height: 40rpx;
					font-style: normal;
					text-transform: none;
				}

				.Content {
					margin-top: 20rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					text-transform: none;

					width: 500rpx;
					// background-color: skyblue;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					/* 处理溢出文本并显示逗号 */
					text-overflow: ellipsis;
					white-space: normal;
					/* 允许换行 */

				}
			}
		}

		.yeji {
			display: flex;
			justify-content: space-between;
			height: 150rpx;
			line-height: 150rpx;
			align-items: center;

			.yjitem {}
		}

		.select {
			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
			}
		}

		.orderView {
			margin-top: 20rpx;

			.scroll-container {
				height: 850rpx;
				overflow-y: auto;

				.SeaProList {
					width: 100%;

					.orderList {
						.orderitem {
							border-radius: 15rpx;
							padding: 35rpx 25rpx;
							background-color: #fff;
							margin-bottom: 35rpx;
							box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.1);

							.topheader {
								display: flex;
								justify-content: space-between;
								margin-bottom: 25rpx;

								.leftt {
									flex: 1;
									display: flex;
									align-items: center;
									justify-content: center;
								}

								.topcen {
									display: flex;
									align-items: center;
									justify-content: center;
									flex: 1;

									.title {
										margin-left: 15rpx;
										font-family: PingFang SC, PingFang SC;
										font-weight: 600;
										font-size: 32rpx;
										color: #333333;
										line-height: 40rpx;
										font-style: normal;
										text-transform: none;

									}
								}

								.ordername {
									flex: 1;
									display: flex;
									align-items: center;
									justify-content: center;
								}
							}

							.bottomC {
								display: flex;
								justify-content: space-between;

								.leftP,
								.centerP,
								.rightP {
									flex: 1;
									display: flex;
									flex-direction: column;
									align-items: center;
									justify-content: center;

									.title {
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 26rpx;
										color: #777777;
										line-height: 40rpx;
										font-style: normal;
										text-transform: none;
									}

									.name {
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 30rpx;
										color: #000;
										line-height: 40rpx;
										font-style: normal;
										text-transform: none;
										margin-top: 10rpx;
									}
								}

								.centerP {
									position: relative;
									/* 需要设置相对定位，以便::before能相对于它定位 */
									display: flex;
									align-items: center;
									/* 垂直居中对齐 */
								}

								.centerP::before {
									content: "";
									/* 必须定义 content */
									position: absolute;
									top: 50%;
									/* 垂直居中 */
									transform: translateY(-50%);
									/* 垂直居中调整 */
									left: -10rpx;
									/* 左边线条的偏移量，可以调整 */
									height: 25rpx;
									/* 线条的高度 */
									width: 2rpx;
									/* 线条的宽度 */
									background-color: #FF6900;
									/* 线条的颜色 */
								}

								.centerP::after {
									content: "";
									/* 必须定义 content */
									position: absolute;
									top: 50%;
									/* 垂直居中 */
									transform: translateY(-50%);
									/* 垂直居中调整 */
									right: -10rpx;
									/* 右边线条的偏移量，可以调整 */
									height: 25rpx;
									/* 线条的高度 */
									width: 2rpx;
									/* 线条的宽度 */
									background-color: #FF6900;
									/* 线条的颜色 */
								}

								.rightP {}
							}
						}
					}

					.noOrder {
						height: 600rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.notitle {
							margin-top: 50rpx;
						}
					}
				}
			}
		}
	}

	.PopupBtn {
		width: 100%;
		height: 340rpx;
		background-color: #EEEEEE;
		// border-radius: 30rpx 30rpx 0 0;
		// padding-bottom: 80rpx;
		margin-bottom: -80rpx;
		z-index: 999 !important;

		position: relative;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
		font-style: normal;
		text-transform: none;

		.qrcode,
		.Billing {
			background-color: #fff;
			width: 100%;
			height: 95rpx;
			line-height: 95rpx;
			text-align: center;
		}

		.cancel {
			height: 95rpx;
			line-height: 95rpx;
			position: absolute;
			text-align: center;
			background-color: #fff;
			width: 100%;
			bottom: 20rpx;
		}
	}
</style>