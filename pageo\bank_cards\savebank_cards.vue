<template>
	<view class="page">
		<newNavBar :title="$t('accountUser.yhk')" width="100vw" @clickLeftIcon="$back"></newNavBar>
		<view class="u-p-10" style="background-color: #ffffff;margin: 20rpx;border-radius: 20rpx;">
			<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm">
				<u-form-item :label="$t('accountUser.yhmc')" prop="bank" borderBottom labelWidth="250rpx">
					<u-input v-model="formData.bank" border="none" :placeholder="$t('accountUser.yhmc')" type="text">
						<u--text :text="$t('accountUser.mc')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				<u-form-item :label="$t('accountUser.zhmc')" borderBottom labelWidth="250rpx">
					<u-input v-model="formData.branch" border="none" :placeholder="$t('accountUser.zhmc')" type="text">
						<u--text :text="$t('accountUser.mc')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<u-form-item :label="$t('accountUser.yhkh')" borderBottom labelWidth="400rpx">
					<u-input v-model="formData.cardNo" border="none" :placeholder="$t('accountUser.yhkh')" type="text" @change="inputValCardNo">
						<u--text :text="$t('accountUser.yhkh')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
					</u-input>
				</u-form-item>
				
				<!-- <u-form-item label="提现到个人账户" borderBottom labelWidth="250rpx">
					<view class="" style="display: flex;">
						<u-input v-model="formData.totalAmount" border="none" placeholder="请输入提现金额" type="number" @change="inputVal">
							<u--text text="￥" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
						</u-input>
						<u-input v-model="arrivedAmount" border="none" type="text" disabled>
							<u--text text="到账" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
						</u-input>
					</view>
				</u-form-item> -->
				<!-- <u-form-item label="提现备注" borderBottom labelWidth="200rpx">
					<u--textarea v-model="formData.remarks" placeholder="如提现多久到账、手续费多少" autoHeight border="none" clearable
						disableDefaultPadding></u--textarea>
				</u-form-item> -->
				
			</u--form>
		</view>
		
		<view class="u-p-20">
			<u-button :text="$t('accountUser.tj')" color="linear-gradient(to right,#2A3A88FF , #3c9cff)" @click="submit" :loading="loading"></u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	
	import {
		deletedczdata,postSavecz
	} from '@/common/request/api/user.js'
	import api from '@/common/request/index.js'
	
	// var common_static_url = app.globalData.get_static_url('common');

	
	export default {
		data() {
			return {
				// common_static_url: require('https://file.36sm.cn/mttttxxs/2025/01/23/31e560f417ce48718649fbfff24e4848.png'),
				pickerShow: false,
				type:0,
				loading:false,
				// 金额单位
				coinName: "",
				// 充值地址二维码
				czqrImg: "",
				formData: {
					// 银行名称
					bank: "",
					// 银行卡号
					cardNo: "",
					// 支行名称
					branch: "",
					bankId: ""
				},
				rules: {
					bank: {
						required: true,
						// message: '请选择银行名称',
						message: this.$t('message.qxzyh'),
						trigger: ['blur']
					},
					branch: {
						required: true,
						// message: '请选择支行',
						message: this.$t('message.qxzzh'),
						trigger: ['blur', 'change']
					},
					cardNo: {
						required: true,
						// message: '请输入银行卡号',
						message: this.$t('message.qsryhkh'),
						trigger: ['blur', 'change']
					}
				}
			}
		},
		onLoad(data) {
			console.log(data.infoData, '参数')
			const infoData = JSON.parse(data.infoData)
			
			console.log(infoData, 'infoData')
			if(infoData !== '') {
				const {
				        bank = "",
				        cardNo = "",
				        branch = "",
				        bankId = ""
				    } = infoData;
				 this.formData = {
				        bank,
				        cardNo,
				        branch,
				        bankId
				    };
					
			console.log(this.formData, 'formData')
			}
		},
		onReady() {
		},
		created() {
			this.getTxfree()
		},
		computed: {
			// arrivedAmount() {
			// 	if(this.formData.totalAmount > 0) {
			// 		return this.formData.totalAmount - this.formData.fee
			// 	} else {
			// 		return 0
			// 	}
			// }
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods: {
			// 获取会员充值码信息
			getTxfree() {
				api({
				    url: `mb/tx/get/sxf/info?location=${this.location}`,
				    header: {
				        "jarepair-platform": uni.getStorageSync("token")
				    },
				    method: 'post'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, 'res.data')
						this.formData.fee = res.data.data.fee
					} else {
						uni.showToast({
							title: '异常',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '异常',
						icon: "none"
					})
				})
			},
			validateField(field) {
				this.$refs.form.validateField(field);
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						console.log(this.formData, '提交参数')
						if(this.formData.bankId =='') {
							// 新增
							console.log(this.formData, '新增提交的参数')
							this.SaveBank()
						} else {
							//编辑
							console.log(this.formData, '编辑提交的参数')
							this.EditBank()
						}
					}
				})
			},
			// 新增银行卡
			SaveBank() {
				// 新增银行卡
				this.loading = true
				setTimeout(() => {
					this.loading = false
				}, 2000)
				api({
					url: `mb/bd/save`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '新增成功',
							title: this.$t('message.czcg'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
			},
			// 编辑银行卡
			EditBank() {
				// 新增银行卡
				this.loading = true
				setTimeout(() => {
					this.loading = false
				}, 2000)
				api({
					url: `mb/bd/update`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: this.formData
				}).then(res=>{
					if(res.code == 200){
						console.log(res, 'resres')
						uni.showToast({
							// title: '编辑成功',
							title: this.$t('message.czcg'),
							icon:'none'
						})
						setTimeout(() => {
							this.$back()
						}, 500)
					}
				})
			},
			inputVal(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.totalAmount = temp
				})
			},
			inputValCardNo(val) {
				console.log(val)
				let temp = val.replace(/[^0-9.]+/g, '')
				this.$nextTick(() =>  {
					this.formData.cardNo = temp
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>