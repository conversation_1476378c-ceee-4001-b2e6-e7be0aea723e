{"accountUser": {"ddgl": "orderManage", "qb": "All", "dzf": "pending pay", "dfh": "to be Purchased", "ysz": "In Transit", "ywc": "Signed", "wdqb": "my purse", "wytx": "Payouts", "wycz": "recharge", "csxhhr": "copartner", "kthhr": "Open a partner", "ye": "balance", "jf": "Integral", "gx": "contribute", "yj": "results", "dzgl": "address", "yhkgl": "IDcard", "zjzr": "Fund Transfer", "fkjy": "feedback", "xtxx": "System Message", "gywm": "about us", "qhyy": "Switch language", "language": "English", "wdtjm": "My referral code", "wdyqlb": "My invite list", "sysyqhy": "<PERSON>an to become my friend and enjoy discounts", "yqhy": "Copy link", "djgl": "<PERSON>lick to manage", "zjmm": "Fund password", "xgmm": "Change password", "bbxx": "Version info", "tcdl": "Logout", "TX": "avatar", "yhm": "Username", "bc": "Save", "shdz": "Shipping address", "mrdz": "Default address", "swmr": "Set as default", "ts": "Tip", "hyk": "Bank card", "fen": "points", "zhang": "张", "scdzm": "Are you sure you want to delete this address?", "xm": "Name", "sjh": "Phone number", "yzm": "Verification code", "dq": "Region", "dz": "Address", "xqdz": "Detailed address", "mr": "<PERSON><PERSON><PERSON>", "sz": "Settings", "yhnc": "User nickname", "hqyzm": "Get code", "bm": "Best-seller", "xfk": "To pay", "yfk": "Paid", "jlzfjs": "Time remaining to pay", "ljzf": "Pay now", "qxdd": "Cancel", "zwdd": "No orders", "tx": "Withdraw", "yhksz": "Bank card settings", "yhmc": "Bank name", "mc": "Name", "zhmc": "Branch name", "yhkh": "Bank account number", "skrmc": "Payee name", "txdgrzh": "Withdraw to personal account", "txje": "Withdrawal amount", "DZ": "arrival", "ktxje": "Available balance for withdrawal", "txbz": "<PERSON><PERSON><PERSON> note", "cz": "Recharge", "hkje": "Remittance amount", "hkdz": "Remittance address", "hkpz": "Remittance proof", "hkbz": "Remittance note", "tj": "Submit", "zjye": "Fund balance", "mysjl": "No data", "jfjl": "Points record", "gxzjl": "Contribution record", "yjjl": "Performance record", "xzshdz": "Add shipping address", "yhk": "Bank card", "xzhyk": "Add member bank card", "dfsjh": "Other party's phone number", "sl": "Quantity", "jyts": "Transaction tips", "zh": "Account", "jy": "Suggestion", "wtms": "Problem description", "tpbc": "Image supplement", "tjtp": "Add image", "sy": "Home", "dzdz": "Custom", "tblj": "Paste 1688/Taobao product link", "xlbp": "Best-selling", "wrtk": "Popular-pro", "jxhw": "Selected goods", "cdyh": "Super low discount", "wd": "Mine", "ljck": "View now", "wdewm": "my qrocde", "grzl": "Profile", "xzdz": "add address", "bjdz": "edit address", "mm": "password", "jqqd": "Stay tuned", "qslgjc": "Please enter a keyword", "jzz": "loading...", "qslyxmc": "Please input your bankname", "myxgsj": "no data", "xxsm": "Could you please elaborate so that we can solve the problem (within 200 words)", "fk": "pay", "fkz": "paying", "jemx": "Amount details", "qx": "Select All", "czje": "Recharge amount:", "czz": "Charging"}, "search": {"zh": "synthesis", "jg": "price", "xl": "Sales", "ssjgbzq": "The search results are inaccurate", "jjfa": "solution", "ssjgbzqjjfa": "Inaccurate search results, solution", "zjdkf": "Dear customers, 见安修脚 technical team has received your feedback~", "nkysy": "You can use the `Image Search` feature to find products more accurately", "sssyts": "The inaccurate search results may be due to machine translation errors. We will manually improve the translation database to enhance future search accuracy.", "fcgxxr": "Thank you very much for trusting 见安修脚!", "rywtlxkf": "If you have any questions, please contact customer service", "wzdl": "Iknow", "gwc": "Shopping cart", "wc": "done", "gl": "manage", "gd": "more", "ndgwcskd": "Your cart is empty, go shopping now!", "qx": "Select all", "hj": "total", "bhgjf": "No-international-fees", "xd": "Order", "sc": "Delete", "qrdd": "Confirm the order", "tjshdz": "add address", "bz": "remark", "dycfk": "First payment", "hl": "exchange rate", "spzj": "total price", "zgyf": "China costs", "cghj": "Total purchases", "dgfwf": "Purchasing service fee", "yhf": "Inspection fee", "zj": "total", "yggjf": "International shipping fee", "tb": "Insurance", "gzwpjytb": "Insurance for valuable items recommended", "jgfw": "Reinforcement service", "gfwsf": "This is a paid service, see details", "thsb": "Special goods declaration", "qrssb": "Please declare truthfully to avoid customs issues. Special goods incur extra charges, see details", "thjssm": "Special goods extra charge details", "wyydbty": "have read and agree", "dgfwsm": "Purchasing-service-details", "mx": "Details", "ljxd": "order now", "QX": "Cancel", "qd": "Confirm", "zf": "Pay", "zdysc": "Bill generated, please pay", "fymx": "Fee details", "jsbz": "<PERSON><PERSON><PERSON><PERSON>", "kjzf": "Quick Pay", "zhye": "Account balance", "dzf": "Pending payment", "cz": "Recharge", "ljzf": "Pay now", "zfmm": "Payment password", "ssls": "Search history", "rmss": "Popular searches", "spfl": "Product categories", "zwsp": "No products available", "sy": "Home", "dz": "Custom", "ztlj": "Paste 1688/Taobao link", "tp": "Image", "yx": "1688 Select", "jrdp": "Enter store", "ck": "见安修脚 Warehouse", "yfgz": "Shipping rules", "zgd": "China section", "kjd": "Cross-border section", "xdszf": "Pay when ordering", "zckjyf": "Pay cross-border shipping", "ggcs": "Specifications", "gmxz": "Purchase notice", "shfw": "After-sales service", "dptj": "Store recommendations", "spxq": "Product details", "hyzazxd": "We will buy strictly according to the order link. Contact customer service for any questions", "ljgm": "Buy now", "xxgl": "Messages", "xxxq": "Details", "YX": "selected", "jiaz": "loading", "jgfwsm": "Reinforcement Service", "zfz": "Processing", "qhkz": "Please transfer to the following account", "bcewmz": "1、Save the QR code to your local gallery", "syszz": "2、<PERSON><PERSON> to transfer", "bcewm": "save code", "tjhkpz": "Submit remittance proof", "shangj": "Merchant", "kc": "Inventory", "jzjz": "loading", "dp": "Shop", "kf": "Support", "scsc": "collect", "qding": "Minimum order"}, "tabbar": {"sy": "Home", "gwc": "<PERSON><PERSON>", "dd": "Orders", "wd": "Mine"}, "message": {"SCcg": "Delete successful", "qqsb": "Request failed", "zt": "Yesterday", "gndtj": "Feature to be added", "yc": "Exception", "sccg": "Upload successful", "bccg": "Save successful", "tjsb": "Submission failed, please try again", "wlkxc": "Network issue~", "ts": "Tip", "qdyscdz": "Are you sure to delete this address?", "mrdzjzsc": "Default address cannot be deleted", "scsb": "Delete failed", "qscshrxm": "Please enter recipient's name", "qsrsjh": "Please enter phone number", "sjhbzq": "Invalid phone number", "qxzdq": "Please select region", "qsrxxdz": "Please enter detailed address", "bjcg": "Edit successful", "xzsc": "Add successful", "qsrzjmmlw": "fund password (6 digits)", "hqyzm": "Get verification code", "qsryzm": "Please enter verification code", "zzhqyzm": "Getting verification code", "yzmfscg": "Verification code sent", "qqyc": "Request exception", "djsjszfs": "Send after countdown ends", "qsrmm": "Please enter password", "dl": "<PERSON><PERSON>", "qsrxmm": "Please enter new password", "qryqm": "Enter invitation code", "wxsryqm": "No invitation code required", "yzmdl": "Verification code login", "mmdl": "Password login", "yjyzhljdl": "Already have an account? Log in", "hmzhljzc": "No account? Register now", "fhdl": "Return to login", "zhmm": "Retrieve password", "ydbty": "Read and agree", "fwxy": "Service Agreement", "ysqzc": "Privacy Policy", "zg": "china", "zpz": "Cambodia", "qrzc": "Confirm Registration", "qrtj": "Confirm Submit", "wzgj": "Unknown Country", "qtyyxtk": "Please read and agree to the terms", "zjmm": "Fund Password (6 digits)", "qxzyh": "Please select bank name", "qxzzh": "Please select branch", "qsryhkh": "Please enter bank card number", "qsrtxje": "Please enter withdrawal amount", "zstx": "Minimum withdrawal", "ktxjebz": "Insufficient withdrawal balance", "bsdshjzsc": "Not under review, cannot delete", "czcg": "Operation successful", "qsrdfsjh": "Please enter recipient's phone number", "qsrsl": "Please enter quantity", "qsrzjmm": "Please enter fund password", "zrcg": "Transfer successful", "qdqxgddm": "Confirm cancel this order?", "qxddcg": "Order cancellation successful", "ztmyc": "Status code error", "nhmyxzddo": "No order selected", "nhmyxzspo": "You haven't selected a product yet.", "mpfm": "Brand, Powder, Pure Battery", "slyp": "Small quantity of medicine, supplements, epidemic prevention, chemicals", "pbbjb": "Tablets, Laptops", "sjck": "Mobile phone, SIM card watch", "qgxwyyd": "Please check 'I have read and agree'", "qxzshdz": "Please select a delivery address", "ckgd": "View more", "sq": "Collapse", "zfcg": "Payment successful", "qscczpz": "Please upload recharge proof", "czcgdsh": "Recharge successful, under review", "qxdl": "Please log in first", "hwxzsp": "No products selected yet", "cgjrgwc": "Successfully added to cart", "jrgwc": "added to cart", "fzcg": "Copy successful", "sctx": "Are you sure you want to delete this withdrawal record", "wtms": "Please enter the issue description", "zdsc": "Up to 4 images can be uploaded", "zsxzyz": "Please upload at least one image", "qxbdsj": "Please bind your phone first", "qdytcdl": "Are you sure you want to log out?", "qxtc": "You have canceled the logout", "yyy": "and", "ysxy": "Privacy Policy", "yhfwxy": "User Service Agreement", "dzygndtj": "Customized page function to be added", "yqjl": "Invitation Record", "yqlb": "Invitation List", "yjpm": "Performance Ranking", "kfxx": "Customer Info", "ssbtn": "search", "china": "china", "jpz": "Cambodia", "wdsc": "collection", "yxrz": "email authentication", "tgrz": "TG authentication", "scsc": "collect", "scskd": "no collection, Go shopping", "sstxl": "30-day sales", "scz": "Collectible", "yxxx": "email info", "qsryxxx": "please email info", "yxyzm": "email code", "tgxx": "TG info", "qsrtgxx": "please TG info", "qsryzmyzm": "Please enter the email verification code", "sccgl": "Collected successfully", "qxcg": "successful", "szcg": "Successfully set", "gj": "country", "xxdz": "full address", "fwz": "station", "dzzp": "Address photos (up to 4 photos)"}}