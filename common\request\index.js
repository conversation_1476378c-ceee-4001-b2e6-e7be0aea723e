// import LuchRequest from '@/uni_modules/uview-ui/libs/luch-request/index.js'
// import store from '@/store/index.js'
// const baseURL = 'https://manger.tuanku.vip/jpzWeb/v1/'; //请求服务器公共地址
// // const baseURL = 'http://*************:8030/jpzMweb/v1/'; //请求服务器公共地址
// // const baseURL = 'http://**************:8030/ertWeb/v1/' //测试接口
// let isPostLogin = true

// export default function api(data, requestConfig = true){
// 	const request = new LuchRequest({baseURL: requestConfig.baseURL?requestConfig.baseURL:baseURL})
// 	let auth = true
// 	let isLocation = false
// 	let isLoading = false
// 	let loadingMsg = ''
// 	if(typeof requestConfig === 'boolean') {
// 		auth = requestConfig
// 	} else {
// 		auth = requestConfig.auth === false ? false : true
// 		isLocation = requestConfig.isLocation
// 		isLoading = requestConfig.isLoading
// 		loadingMsg = requestConfig.loadingMsg || '请求中...'
// 	}
// 	//请求之前拦截器
// 	request.interceptors.request.use((config, cancel) =>{
// 		var token = store.state.user.token
// 		config.header['jarepair-platform'] = token
// 		isLoading && uni.showLoading({
// 			title: loadingMsg,
// 			mask:true
// 		})
// 		return config
// 	})
// 	//响应拦截
// 	request.interceptors.response.use(response=>{
// 		/* 响应拦截器 */
// 		isLoading && uni.hideLoading()
// 		if(response.data.code !== 200){
// 			uni.$u.toast(response.data.msg)
// 		}
// 		return response.data
// 	})
// 	if(data.data) data.data = deleteNullValue(data.data) //处理空值
// 	if(data.params) data.params = deleteNullValue(data.params) //处理空值
// 	return request.request(data)
// }
//  //处理空值
// function deleteNullValue(params) {
// 	if(params) {
// 		Object.keys(params).forEach(key => {
// 			if(params[key] === undefined || params[key] === null || params[key] === '') {
// 				delete params[key]
// 			}
// 		})
// 	}
// 	return params
// }


import LuchRequest from '@/uni_modules/uview-ui/libs/luch-request/index.js'
import store from '@/store/index.js'

// const baseURL = 'https://cos.jiananxiujiao.com/khrWeb/v1/'; // 请求服务器公共地址
const baseURL = 'https://cos.jiananxiujiao.com/jaxWebBs/v1/'; // 请求服务器公共地址
let isRedirecting = false;

// const baseURL = 'http://43.156.1.184/khrWeb/v1/'; //请求服务器公共地址


export default function api(data, requestConfig = true) {
	const request = new LuchRequest({
		baseURL: requestConfig.baseURL ? requestConfig.baseURL : baseURL,
	});

	let auth = true;
	let isLoading = false;
	let loadingMsg = '';
	if (typeof requestConfig === 'boolean') {
		auth = requestConfig;
	} else {
		auth = requestConfig.auth === false ? false : true;
		isLoading = requestConfig.isLoading;
		loadingMsg = requestConfig.loadingMsg || '请求中...';
	}

	// 请求之前拦截器
	request.interceptors.request.use((config, cancel) => {
		var token = store.state.user.token;
		config.header['jarepair-platform'] = token;

		// Set content type to JSON
		config.header['Content-Type'] = 'application/json';

		isLoading && uni.showLoading({
			title: loadingMsg,
			mask: true,
		});

		// Transform data to JSON string if it's an object
		if (typeof data.data === 'object') {
			config.data = JSON.stringify(data.data);
		}

		return config;
	});

	// 响应拦截
	// request.interceptors.response.use(response => {
	// 	isLoading && uni.hideLoading();
	// 	if (response.data.code !== 200) {
	// 		uni.$u.toast(response.data.msg);
	// 	}
	// 	return response.data;
	// });
	
	request.interceptors.response.use(response => {
			isLoading && uni.hideLoading();
			if (response.data.code == 200) {
				// 返回正常数据
				return response.data;
			  }
		  
			 //  if (response.data.code == 401) {
				// // 提示用户未授权
				// uni.$u.toast(response.data.msg);
				
				// setTimeout(() => {
				// 	uni.navigateTo({
				// 		url: '/pageo/login/login',
				// 	});
					
				// 	// 返回一个空的响应，防止后续代码继续执行
				// 	return Promise.reject(new Error(response.data.msg));  // 你也可以根据需要抛出错误
				// }, 1500)
			 //  }
			 if (response.data.code == 401 && !isRedirecting) {
					 // 提示用户未授权
					 isRedirecting = true;
					 uni.$u.toast(response.data.msg);
					 setTimeout(() => {
						uni.navigateTo({
							url: '/pageo/login/login',
						});
						isRedirecting = false
						// 返回一个空的响应，防止后续代码继续执行
						return Promise.reject(new Error(response.data.msg));  // 你也可以根据需要抛出错误
					 }, 100)
				 }
			if (response.data.code !== 200 && response.data.code !== 401) {
				uni.$u.toast(response.data.msg);
			}
			return response.data;
		});
	if (data.data) data.data = deleteNullValue(data.data); // 处理空值
	if (data.params) data.params = deleteNullValue(data.params); // 处理空值
	return request.request(data);
}

// 处理空值
function deleteNullValue(params) {
	if (params) {
		Object.keys(params).forEach(key => {
			if (params[key] === undefined || params[key] === null || params[key] === '') {
				delete params[key];
			}
		});
	}
	return params;
}