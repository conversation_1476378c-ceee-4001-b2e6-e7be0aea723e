<template>
	<view class="page">
		<view class="header" :style="{ 'lineHeight': navBarHeight + 'rpx' }">
			<view class="box-bg">
				<view class="back" @click="goBack">
					<image src="https://file.36sm.cn/xtjyjt/images/common/bback.png"
						style="width: 22rpx;height: 33rpx;"></image>
				</view>
				<view class="viewtitle">
					<text>退单</text>
				</view>
				<view class="">
				</view>
			</view>
		</view>
		<view class="OrderDetail">

			<view class="topSelect">
				<view class="lefttitle">
					<text>退单项目</text>
				</view>
				<view class="rightcheck" @click="allSelect">
					<text>{{isallStitle}}</text>
				</view>
			</view>

			<view class="orderinfo">
				<view class="mxList">
					<view class="mxitem" v-for="(item,i) in orderDettail.mxList" :key="i">
						<view class="mxdetail">
							<view class="">
								<image :src="item.productMainImg" mode=""
									style="width: 150rpx; height: 150rpx;border-radius: 50%;">
								</image>
							</view>
							<view class="right">
								<view class="righttop">
									<view class="proname">
										{{item.productTitle}}
									</view>
									<view class="checkbox">
										<checkbox-group @change="selectedShop(item, i)">
											<label>
												<!-- #ifndef APP -->
												<checkbox class="selected" color="#FF4000" :checked="item.ischecked" disabled
													:customStyle="{ 
												    borderRadius: '50%',
												    border: '2px solid #FF4000', 
												    padding: '0', 
												    width: '20px',
												    height: '20px',
												    backgroundColor: item.ischecked ? '#FF4000' : '#fff'
												  }" style="transform:scale(0.9);" shape="circle" />
												<!-- #endif -->
											</label>
										</checkbox-group>
									</view>
								</view>
								<view class="rightcenter">
									<view class="">
										<text>{{item.extendDt1 || '预约的日期数据'}}</text>
									</view>
									<!-- 商品类型的订单显示数量 -->
									<view class="sellQuantity" v-if="item.typeOrder == 1">
										<text>× {{item.sellQuantity}}</text>
									</view>
								</view>
								<view class="skudetail">
									<view class="sku">
										{{item.extendDt2 || '预约的时间数据'}}
									</view>
									<view class="proPrice">
										￥ {{item.payOrgPriceTrade}}
									</view>
								</view>
								<!-- <view class="skudetail">
									<view class="sku">
										{{item.productSkuName}}
									</view>
									<view class="proPrice">
										￥ {{item.payOrgPriceTrade}}
									</view>
								</view> -->
							</view>
						</view>
					</view>
				</view>
				<view class="totalPrice">
					<text class="statusName">退款总金额:</text>
					<text class="pricevalue">￥ {{ totalPprice }}</text>
				</view>

			</view>
			<view class="topSelect">
				<view class="lefttitle">
					<text>退单原因</text>
				</view>

			</view>
			<view class="payinfo">
				<view class="Ccback">
					<view class="cbreason">
						<view class="reasonitem" v-for="(item,index) in CBreason" @click="clickReason(item,index)" :key="item" :class="isIndex == index ? 'isactive' : ''">
							<text>{{item}}</text>
						</view>
					</view>

					<view class="input">
						<u-input placeholder="其它原因说明" v-model="orderRemark" type="textarea" :height="500" border="none"
							:customStyle="{lineHeight: '36px',height: '100rpx', padding: ' 0 20rpx'}" />
					</view>

					<view class="Chargeback">
						<view class="title">
							<text>退单规则</text>
						</view>
						<view class="chargeContent">
							<view class="list-item">
								<text>{{ orderDettail.tdDescribe[0] }}</text>
							</view>
							<view class="list-item">
								<text>{{ orderDettail.tdDescribe[1] }}</text>

							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="bottomBtn">
			<view class="leftitle">
				<text>退单总金额</text>
				<text>￥ {{totalPprice.toFixed(2)}}</text>
			</view>
			<view class="rightbtn">
				<u-button text="提交" class="addOrder" color="#fff" @tap="Applyrefund" :disabled="isloading"
					:loading="isloading" loadingText="提交中"
					:custom-style="{ borderRadius: '94rpx', height: '80rpx', width: '200rpx',color: '#FF6900' }"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				navBarHeight: '',
				orderDettail: {
					mxList: []
				},
				// 门店id
				placeId: '',
				visible: false,
				shopDetail: {},
				isallStitle: '全选',
				stepList: [{
						name: '预约',
						step: 1,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/529a4fc7bfc84214af0342e9966427da.png'
					},
					{
						name: '到店',
						step: 2,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/d4f39f3a30354475a0b70681c8d8af3c.png'
					},
					{
						name: '推拿',
						step: 3,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/45ecce1e4ebf40e1bb89b2e360019a4c.png'
					},
					{
						name: '评价',
						step: 4,
						img: 'https://file.36sm.cn/jaxinfo/2025/02/26/4e039232fff843a095ca5dc77fb74fba.png'
					}
				],
				ischecked: false,
				totalPprice: 0,
				CBreason: ['行程有变', '下错时间', '选错调理师'],
				orderRemark: '',
				adLatitude: uni.getStorageSync('adLatitude'),
				adLongitude: uni.getStorageSync('adLongitude'),
				isIndex: 3,
				pricevalue: {
					sfJe: '',
					tkBl: '',
					tkJe: '',
				}
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		onLoad(opt) {
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			console.log(opt, '路径参数')
			// this.orderDettail = JSON.parse(opt.orderDetail)
			this.orderDettail = JSON.parse(decodeURIComponent(opt.orderDetail));
			// console.log(this.orderDettail, '订单详情')

			// 给orderDettail.mxList 添加ischecked 用来鉴别是否选中
			if (this.orderDettail.mxList.length > 0) {
				this.orderDettail.mxList.forEach((item) => {
					item.ischecked = false
				})
			}
			console.log(this.orderDettail, '订单详情')

			if (this.orderDettail.typeOrder == 0) {
				// 预约订单  通过门店id 获取门店信息
				this.placeId = this.orderDettail.adUserName
				// this.placeId = '1890236814091571200'
				this.getShopDetail(this.placeId)
			}

			console.log(this.orderDettail.orderId, '当前订单的ID')
		},
		// computed: {
		// 	totalPPrice() {
		// 		let totalprice = 0
		// 		this.orderDettail.mxList.forEach(shop => {
		// 			if(shop.ischecked) {
		// 				totalprice += shop.productPriceTotalTrade;
		// 			}
		// 		})
		// 		return totalprice
		// 	},
		// },
		onShow() {},
		methods: {
			clickReason(item,index) {
				this.isIndex = index
			},
			allSelect() {
				 const isAllSelected = this.orderDettail.mxList.every(item => item.ischecked);
				
				    // 如果当前所有项都已选中，则取消全选，否则执行全选
				    if (isAllSelected) {
				      // 取消全选
				      this.orderDettail.mxList.forEach(item => {
				        item.ischecked = false;
				      });
				      this.isallStitle = '全选'; // 更新文字为 '全选'
				    } else {
				      // 执行全选
				      this.orderDettail.mxList.forEach(item => {
				        item.ischecked = true;
				      });
				      this.isallStitle = '取消全选'; // 更新文字为 '取消全选'
				    }
				this.calculateTotalPrice()
				
			},
			async Applyrefund() {
				
				 const isAllSelected = this.orderDettail.mxList.every(item => item.ischecked);
				 
				 if(!isAllSelected) {
					 uni.showToast({
					 	title: '请选择退单产品',
					 	icon: "none"
					 });
					 return
				 }
				await this.getCkRefund();
			},
			getCkRefund() {
				const params = {
					memberNotes: this.orderRemark,
					orderId: this.orderDettail.orderId,
					qmList: this.shopDetail.mxList
				}
				api({
					url: 'goods/ck/for/refund/je',
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, 'data')
						this.pricevalue = res.data.data
						
						uni.showModal({
							title: '退单提示',
							// content: `订单实付金额${pricevalue}元， 现在退单退您实付金额100.00%(${pricevalue}元),请确认是否退单`,
							content: `订单实付金额 ${this.pricevalue.sfJe} 元，现在退单退您实付金额 ${(this.pricevalue.tkBl * 100).toFixed(2)}%(${this.pricevalue.tkJe} 元)，请确认是否退单`,
							showCancel: true,
							cancelText: '取消',
							confirmText: '确定',
							cancelColor: '#333',
							confirmColor: '#FF6900',
							success: (res) => {
								if (res.confirm) {
									uni.showLoading({
										title: '加载中'
									})
									console.log('取消订单操作')
									this.refundOrder()
								} else if (res.cancel) {
									console.log('用户点击取消');
									uni.showToast({
										title: '取消退单',
										icon: "none"
									});
								}
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					uni.hideLoading()
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			refundOrder() {
				const params = {
					memberNotes: this.orderRemark,
					orderId: this.orderDettail.orderId,
					qmList: this.shopDetail.mxList
				}
				api({
					// url: `goods/cance/order/mx`,
					url: `goods/apply/for/refund`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '退单成功',
							icon: 'none'
						})
						setTimeout(() => {
							uni.navigateBack({
								success: () => {},
								fail: (err) => {
									uni.reLaunch({
										url: "/pages/home/<USER>"
									})
								}
							})
						}, 400)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					uni.hideLoading()
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			calculateTotalPrice() {
				let totalprice = 0;
				this.orderDettail.mxList.forEach(shop => {
					console.log(shop.productPriceTotalTrade); // 检查价格
					if (shop.ischecked) {
						totalprice += shop.productPriceTotalTrade;
					}
				});
				this.totalPprice = totalprice;
			},
			selectedShop(item, index) {
				// 切换子元素的选中状态
				this.orderDettail.mxList[index].ischecked = !this.orderDettail.mxList[index].ischecked;
				console.log(this.orderDettail.mxList, 'mxlist')
				this.calculateTotalPrice()
			},
			goBack() {
				// uni.navigateTo({
				// 	url: '/pageu/user-order/user-order'
				// });

				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			getShopDetail(id) {
				const params = {
					adLongitude: this.adLongitude,
					adLatitude: this.adLatitude
				}
				api({
					url: `tx/place/id/info/${id}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					setTimeout(() => {
						this.$data.visible = true
					}, 1500)
					if (res.code == 200) {
						console.log(res.data.data, '店铺详情数据')
						this.shopDetail = res.data.data
						console.log(this.shopDetail, '店铺详情数据')
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			openlocation(detail) {
				// 打开门店详情
				uni.openLocation({
					latitude: detail.adLatitude,
					longitude: detail.adLongitude,
					scale: 18,
					success() {
						console.log('打开地图成功')
					},
					fail(error) {
						uni.showToast({
							title: '地图打开失败',
							icon: "none"
						});
					}
				})
			},
			callphone() {
				if (this.shopDetail.contactsInfoList.length == 0) {
					uni.showToast({
						title: '暂无联系电话',
						icon: "none"
					});
					return
				}
				uni.makePhoneCall({
						phoneNumber: this.shopDetail.contactsInfoList[0].phone
					})
					.catch((e) => {
						// console.log (e)
					});
			}
		}

	}
</script>

<style>
	.page {
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
	}
</style>

<style lang="scss" scoped>
	.header {
		width: 100%;
		padding-top: 110rpx;
		height: 60rpx;
		background-color: #FFFFFF;
		position: fixed;
		z-index: 999;

		.box-bg {
			height: 40rpx;
			line-height: 40rpx;
			width: 94%;
			margin: 0 auto;
			// background-color: skyblue;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.viewtitle {
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.OrderDetail {
		height: calc(100vh - 180rpx);
		overflow-y: auto;
		width: 94%;
		margin: 0 auto;
		margin-top: 190rpx;

		.topSelect {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			line-height: 80rpx;
			padding: 0 15rpx;

			.lefttitle {
				font-family: PingFang SC, PingFang SC;
				color: #000;
				font-weight: 600;
			}

			.rightcheck {
				font-family: PingFang SC, PingFang SC;
				color: #FF6900;
				font-weight: 600;
			}
		}

		.orderinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;
			margin-bottom: 15rpx;
			padding: 0 0 25rpx 0;

			.mxList {
				.mxitem {
					width: 94%;
					margin: 0 auto;
					padding: 25rpx 0;
					border-bottom: 1rpx solid #ededed;

					.mxdetail {
						display: flex;
						justify-content: space-between;

						.right {
							flex: 1;
							margin-left: 15rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-around;

							.righttop {
								font-weight: 600;
								color: #000;
								display: flex;
								justify-content: space-between;
								margin-bottom: 10rpx;

								.proname {
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.checkbox {
									/deep/ .uni-checkbox-input {
										width: 36rpx;
										height: 36rpx;
										border-radius: 50%;
										border: 1px solid #ccc;
									}
								}
							}

							.rightcenter {
								display: flex;
								justify-content: space-between;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 28rpx;
								color: #999999;
								font-style: normal;
								text-transform: none;
							}

							.skudetail {
								display: flex;
								justify-content: space-between;
								margin-top: 10rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;
								
								.sku {
									font-family: PingFang SC, PingFang SC;
									font-weight: 600;
									font-size: 24rpx;
									color: #555;
									font-style: normal;
									text-transform: none;
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

							}

							.bottomC {
								color: #ccc;
								font-weight: 300;
							}
						}
					}
				}
			}

			.showMore {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #c1c1c1;
			}



			.OrderTime {
				display: flex;
				align-items: center;
				padding: 0 30rpx;

				.title {
					color: #ccc;
					margin-right: 20rpx;
				}
			}

			.totalPrice {
				margin: 0 auto;
				padding-top: 25rpx;
				width: 94%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-bottom: 2rpx;

				.statusName {
					margin-right: 8rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
					font-style: normal;
					text-transform: none;
				}

				.pricevalue {
					font-weight: 600;
					font-family: PingFang SC, PingFang SC;
					font-size: 28rpx;
					color: #FF6900;
					font-style: normal;
					text-transform: none;
				}
			}

			.zfbtn {
				margin: 0 auto;
				width: 94%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 25rpx 0;

				.qxdd {
					width: 100rpx;
					margin-right: 20rpx;
				}

				.ljzf {
					padding: 10rpx 20rpx;
					color: #FF6900;
					border-radius: 94rpx;
					border: 2rpx solid #FF6900;
				}
			}
		}

		.payinfo {
			background-color: #FFFFFF;
			border-radius: 18rpx;

			.Ccback {
				width: 94%;
				margin: 0 auto;
				margin-bottom: 15rpx;
				padding: 25rpx 0;

				.cbreason {
					display: flex;
					align-items: center;

					.reasonitem {
						background-color: #f3f3f3;
						border-radius: 20rpx;
						padding: 8rpx 15rpx;
						margin-right: 20rpx;
						color: #000;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						font-style: normal;
						text-transform: none;
					}
					.isactive{
						background-color: #FF6900;
						color: #fff;
					}
				}

				.input {
					background-color: #f3f3f3;
					border-radius: 10rpx;
					margin: 30rpx 0 60rpx 0;
				}

				.Chargeback {
					border-radius: 20rpx;
					// background-color: skyblue;
					padding: 30rpx 20rpx;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 28rpx;
						color: #000;
						font-style: normal;
						text-transform: none;
						margin-bottom: 15rpx;
					}

					.chargeContent {
						.list-item {
							display: flex;
							align-items: center;
							margin-bottom: 15rpx;
							/* 控制列表项之间的间距 */
						}

						.list-item::before {
							content: "•";
							/* 使用圆点作为列表项标记 */
							font-size: 28rpx;
							color: #ccc;
							margin-right: 10rpx;
							/* 圆点和文本之间的间距 */
						}

						.list-item text {
							font-size: 24rpx;
							color: #000;
						}
					}
				}
			}
		}

	}

	.bottomBtn {
		position: fixed;
		width: 90%;
		background-color: #FF6900;
		border-radius: 70rpx;
		padding: 30rpx;
		color: #FFFFFF;
		bottom: 50rpx;
		z-index: 999;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
</style>
