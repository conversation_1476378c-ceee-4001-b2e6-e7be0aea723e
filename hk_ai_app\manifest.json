{
    "name" : "见安修脚",
    "appid" : "__UNI__80D53B1",
    "description" : "见安修脚",
    "versionName" : "1.0.0",
    "versionCode" : 100,
    "transformPx" : false,
    "app-plus" : {
        "safearea" : {
            //安全区域配置，仅iOS平台生效  
            "bottom" : {
                // 底部安全区域配置  
                "offset" : "none" // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"  
            }
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "VideoPlayer" : {},
            "OAuth" : {},
            "Camera" : {},
            "Geolocation" : {},
            "Payment" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            "ios" : {
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx60ab9d3ad6c005b4"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "",
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "appleiap" : {},
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx60ab9d3ad6c005b4"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common"
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "requiredPrivateInfos" : [
            "chooseLocation",
            "getLocation",
            "chooseAddress",
            "onLocationChange",
            "startLocationUpdate",
            "startLocationUpdateBackground"
        ],
        "lazyCodeLoading" : "requiredComponents",
        "appid" : "wx60ab9d3ad6c005b4",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "ignoreDevUnusedFiles" : false,
            "ignoreUploadUnusedFiles" : false,
            "postcss" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置将用于小程序中相应业务位置服务使用"
            }
        },
        "__usePrivacyCheck__" : true,
        "plugins" : {}
    },
    // 直播（需要到小程序后台设置->第三方设置->插件管理里面添加【小程序直播组件】插件，教程 https://mp.weixin.qq.com/wxopen/pluginbasicprofile?action=intro&appid=wx2b03c6e691cd7370）
    // "live-player-plugin" : {
    //     "version" : "1.3.5",
    //     "provider" : "wx2b03c6e691cd7370"
    // },
    // 腾讯地图路线规划插件（需要到小程序后台设置->第三方设置->插件管理里面添加【腾讯位置服务路线规划】插件，教程 https://mp.weixin.qq.com/wxopen/plugindevdoc?appid=wx50b5593e81dd937a）
    // "routePlan" : {
    //     "version" : "1.0.19",
    //     "provider" : "wx50b5593e81dd937a"
    // }
    "mp-alipay" : {
        "usingComponents" : true,
        "appid" : "2021001173639600"
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : "ttfb628ddf6458b04f",
        "setting" : {
            "urlCheck" : false
        }
    },
    "networkTimeout" : {
        "request" : 10000,
        "downloadFile" : 10000
    },
    "mp-qq" : {
        "appid" : "**********",
        "setting" : {
            "urlCheck" : false
        }
    },
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "56SBZ-PCC3G-US2QM-IXYFE-4DE5H-GRBDK"
                }
            }
        },
        "devServer" : {
            "https" : false,
            "port" : 8082
        },
        "router" : {
            "mode" : "history",
            "base" : "./"
        },
        "title" : "ZhiLApp",
        "template" : "template.h5.html"
    },
    "vueVersion" : "2"
}
