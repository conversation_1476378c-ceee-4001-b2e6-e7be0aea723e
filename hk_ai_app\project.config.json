{"appid": "wx3315b41b71096eaf", "compileType": "miniprogram", "libVersion": "3.3.1", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "miniprogramRoot": "unpackage/dist/dev/mp-weixin/", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "srcMiniprogramRoot": "unpackage/dist/dev/mp-weixin/", "mp-weixin": {"permission": {"scope.userLocation": {"desc": "用于选择图片和上传文件"}}}}