<template>
	<view class="pay_popup" :class="isOpen ? 'on' : ''">
		<!-- 左侧模块: 日期及星期 -->
		<picker-view :value="value" @change="bindChange" immediate-change class="picker-view" indicator-class="picker-box">
			<picker-view-column>
				<view class="leftitem" v-for="(item, index) in dateList" :key="index" @click="selectItem(index)" :style="{
						backgroundColor: selectedDateIndex === index ? '#FF6900' : 'transparent', 
						color: selectedDateIndex === index ? '#fff' : '#FF6900',
						fontWeight: selectedDateIndex === index ? '600' : 'normal'}">
					{{ item.date }}
					<text v-if="item.date !== '今天'">
						<text style="margin-left: 5rpx;">{{ item.weekday }}</text>
					</text>
				</view>
			</picker-view-column>

			<!-- 中间模块: 小时 -->
			<picker-view-column>
				<view class="centeritem" v-for="(item, index) in hours" :key="index" :style="{
			              backgroundColor: selectedHourIndex === index ? '#FFF3EB' : 'transparent', 
			              color: selectedHourIndex === index ? '#FF6900' : '#999999',
						  fontWeight: selectedHourIndex === index ? '600' : 'normal'}" @click="selectHour(index)">
					{{ item }}点
				</view>
			</picker-view-column>

			<!-- 右侧模块: 分钟 -->
			<picker-view-column>
				<view class="rightitem" v-for="(item, index) in minutes" :key="index" :style="{
			              backgroundColor: selectedMinuteIndex === index ? '#FFF3EB' : 'transparent', 
			              color: selectedMinuteIndex === index ? '#FF6900' : '#999999',
						  fontWeight: selectedMinuteIndex === index ? '600' : 'normal'}" @click="selectMinute(index)">
					{{ item }}分
				</view>
			</picker-view-column>

		</picker-view>
	</view>
</template>
<script>
	import {
		mapGetters
	} from "vuex";

	export default {
		props: {
			isOpen: {
				default: false,
				type: Boolean
			},
			modelValue: { // 选择时间格式 yyyy-MM-dd
				default: '',
				type: String
			},
			isGreaterY: { // 是否选择年月日，y 表示选择年月日，n 表示选择年月
				default: 'n',
				type: String
			}
		},
		data() {
			const date = new Date();
			const daysInWeek = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
			// 获取当天的日期信息
			const year = date.getFullYear();
			const month = date.getMonth() + 1; // 月份是从0开始的
			const day = date.getDate();
			const weekday = daysInWeek[date.getDay()];

			// 生成前3天 + 今天 + 后7天的日期数组
			const dateList = [];
			for (let i = -3; i <= 7; i++) {
				const currentDate = new Date(date);
				currentDate.setDate(date.getDate() + i);
				const dateStr = `${currentDate.getMonth() + 1}月${currentDate.getDate()}日`;
				const weekdayStr = daysInWeek[currentDate.getDay()];
				const isToday = i === 0 ? '今天' : dateStr;
				dateList.push({
					date: isToday,
					weekday: weekdayStr
				});
			}

			// 获取小时和分钟的数据
			const hours = Array.from({
				length: 24
			}, (_, i) => i);
			const minutes = Array.from({
				length: 60
			}, (_, i) => i);

			return {
				dateList, // 左侧显示日期和星期
				hours, // 中间显示小时
				minutes, // 右侧显示分钟
				value: [3, date.getHours(), date.getMinutes()], // 默认选择当前时间
				selectedDateIndex: 3, // 默认选中的日期索引
				selectedHourIndex: date.getHours(), // 默认选中的小时
				selectedMinuteIndex: date.getMinutes(), // 默认选中的分钟
				year,
				month,
				day
			};
		},
		created() {
			this.setDefaultValues();
			this.bindChange({
				detail: {
					value: this.value // 使用默认的value数组来触发bindChange
				}
			});
		},
		methods: {
			selectItem(index) {
				// console.log(index, 'index')
			},
			setDefaultValues() {
				const date = new Date();
				this.value = [3, date.getHours(), date.getMinutes()]; // 默认选中当前小时和分钟
				// this.selectedDateIndex = 3; // 默认选中日期是今天
				this.selectedHourIndex = date.getHours(); // 默认选中当前小时
				this.selectedMinuteIndex = date.getMinutes(); // 默认选中当前分钟
			},
			bindChange(e) {
				// console.log(e, '选取的时间');
				const val = e.detail.value;
				this.value = val;
				// 计算日期、时间变化并更新
				const selectedDate = this.dateList[val[0]].date;
				const selectedHour = this.hours[val[1]];
				const selectedMinute = this.minutes[val[2]];
				this.selectedDateIndex = val[0]; // 更新选中的日期索引
				this.selectedHourIndex = val[1]; // 更新选中的小时
				this.selectedMinuteIndex = val[2]; // 更新选中的分钟
				this.$emit('bindChange', {
					selectedDate,
					selectedHour,
					selectedMinute
				});
			},
			selectHour(index) {
				// this.selectedHourIndex = index;
				// this.$emit('bindChange', {
				// 	selectedDate: this.dateList[this.value[0]].date,
				// 	selectedHour: this.hours[index],
				// 	selectedMinute: this.minutes[this.value[2]]
				// });
			},
			selectMinute(index) {
				// this.selectedMinuteIndex = index;
				// this.$emit('bindChange', {
				// 	selectedDate: this.dateList[this.value[0]].date,
				// 	selectedHour: this.hours[this.value[1]],
				// 	selectedMinute: this.minutes[index]
				// });
			}
		}

	};
</script>
<style lang="less" scoped>
	.pay_popup {
		// background-color: skyblue;
		.picker-view {
			width: 100%;
			// height: 500rpx;
			height: 600rpx;
			border: none !important;
			.picker-box {
				border: none !important;
				height: 100rpx;
				background: #C90F07;
				font-weight: 500;
				color: #45454C;
				line-height: 80rpx;
				font-family: 'alimm-font' !important;
				opacity: 0.6;
			}

			::v-deep .picker-box::after {
				border-bottom: none;
			}

			::v-deep .picker-box::before {
				border-top: none;
			}
		}

		.picker-view-column {
			& :first-child {
				background-color: skyblue !important;

				.picker-box {
					border-top-left-radius: 16rpx;
					border-bottom-left-radius: 16rpx;
				}

				border-right: 2rpx solid #ccc;
			}

			& :last-child {
				.picker-box {
					border-top-right-radius: 16rpx;
					border-bottom-right-radius: 16rpx;
				}
			}
		}

		.leftitem {
			width: 90%;
			margin: 0 auto;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			text-align: center;
			font-size: 32rpx;
			font-family: PingFang SC;
			border-radius: 4rpx;
			color: #FF6900;
			// border-right: 1rpx solid #45454C;
			// background-color: skyblue !important;
		}

		.centeritem,
		.rightitem {
			margin: 0 auto;
			width: 90%;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			font-family: PingFang SC;
			border-radius: 4rpx;
			color: #999999;
		}

		.selected {
			background-color: #FF6900;
			color: #fff;
			border-radius: 4rpx;
		}

		.unselected {
			color: #FF6900;
		}
	}
</style>