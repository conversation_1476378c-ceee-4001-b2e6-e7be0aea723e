import api from '../index.js'


const location = uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh';

//获取登录用户基本信息
export const getUserInfo= () =>
	api({
		url: `mb/login/base/info?location=${location}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
	})

//更新登录用户基本信息

export const UpdateUserInfo = data => {
	api({
		url: `mb/login/base/info?location=${location}`,
		method: 'post',
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		data
	})
}



// 获取用户地址
export const getAddress = params => {
	api({
		url: 'mb/address/get/list',
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		params
	})
}
// 设置默认地址
export const postUpdateIsDefault = id => {
	api({
		url: `mb/updateIsDefault/address/${id}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}
// 删除地址
export const deletedAddress = dateId => {
	api({
		url: `mb/deleted/address/${dateId}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}
// 新增地址
export const postSaveAddress = data => {
	api({
		url: `mb/save/address`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}
// 编辑地址
export const postUpdateAddress = data => {
	api({
		url: `mb/update/address`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}

// 领取积分
export const postUpdateIntegral = orderId => {
	api({
		url: `mb/update/Integral?orderId=${orderId}`,
		method: 'post',
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在领取积分'
	})
}
	
	
// 获取会员的订单列表数量(待付款、待发货、待收货、已完成)
export const getOrderNum = data => {
	api({
		url: `goods/order/get/mx`,
			header: {
				"jarepair-platform": uni.getStorageSync("token")
			},
			method: 'get',
			data
		})
}



// 删除会员提现数据
export const deletedtxdata = dateId => {
	api({
		url: `mb/tx/deleted/${dateId}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}

// 会员增加提现数据
export const postSavetx = data => {
	api({
		url: `mb/tx/save`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}


// 获取提现记录的状态类型
export const getTxtype = data => {
	api({
		url: `mb/tx/get/type/list?location=${location}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}


// 删除会员银行卡
export const deletedbd = dateId => {
	api({
		url: `mb/bd/deleted/${dateId}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}


// 会员增加银行卡
export const postSavebd = data => {
	api({
		url: `mb/bd/save`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}


// 会员编辑银行卡

export const postEditbd = data => {
	api({
		url: `mb/bd/update`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}


// 会员设置默认银行卡
export const postbdIsDefault = id => {
	api({
		url: `mb/bd/updateIsDefault/address/${id}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}




// 删除会员充值数据
export const deletedczdata = dateId => {
	api({
		url: `mb/cz/deleted/${dateId}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
	})
}

// 会员增加提现数据
export const postSavecz = data => {
	api({
		url: `mb/cz/save`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}


// 获取提现记录的状态类型
export const getCztype = data => {
	api({
		url: `mb/cz/get/type/list?location=${location}`,
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		method: 'post',
		data
	})
}