<template>
	<view>
		<newNavBar :title="$t('accountUser.sz')" width='100vw' @clickLeftIcon="$back"></newNavBar>
		<view :class="theme_view" class="page" v-if="visible">
			<view style="padding-top: 20rpx;">
				<!-- 主体内容 -->
				<block v-if="data_list_loding_status == 3">
					<view class="padding-horizontal-main border-radius-main oh spacing-mb">
						<view class="topInfo padding-top-xxl padding-bottom-xxl arrow-right oh"
							data-value="/pages/personal/personal" @tap="goPersonal">
							<!-- url_event  goPersonal-->
							<view class="left">
								<image :src="user.logoUrl" mode="widthFix"
									class="circle br fl user-avatar">
								</image>
								<view class="fl margin-left">
									<view>{{ user.nickName || $t('accountUser.yhnc') }}</view>
									<!-- <view class="leader">
										我的领路人 <text style="margin-left: 15rpx;">无
										</text>
										<uni-icons type="plus" size="20" style="margin-left: 10rpx;"></uni-icons>
									</view> -->
								</view>
		
							</view>
							<image class="codeImage" src="https://file.36sm.cn/xtjyjt/images/common/barcode/code.png" mode=""></image>
						</view>
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right"
							data-value="/pageu/user-address/user-address" @tap="url_event">
							<text>{{$t('accountUser.dzgl')}}</text>
							<text class="fr cr-grey">{{$t('accountUser.djgl')}}</text>
						</view> -->
					</view>
		
					<view class="padding-horizontal-main border-radius-main oh spacing-mb" style="margin-top: 10rpx;">
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-t" @tap="fundpassword">
							<text>{{$t('accountUser.zjmm')}}</text>
							<text class="fr cr-grey">
								{{jjmmName}}
							</text>
						</view> -->
						<view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-t" @tap="changepassword">
							<text>{{$t('accountUser.xgmm')}}</text>
							<text class="fr cr-grey"></text>
						</view>
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-t" @tap="emailcertified">
							<text>{{$t('message.yxrz')}}</text>
							<text class="fr cr-grey" v-if="!emaildata">
								{{emailname}}
							</text>
						</view>
						<view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-t" @tap="tgcertified">
							<text>{{$t('message.tgrz')}}</text>
							<text class="fr cr-grey" v-if="!tgdata">
								{{tgname}}
							</text>
						</view> -->
					</view>
		
					<view class="padding-horizontal-main border-radius-main oh spacing-mb" style="margin-top: 10rpx;">
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-b"
							@tap="open_setting_event">
							<text>消息推送</text>
							<text class="fr cr-grey">已开启</text>
						</view>
						<view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-b">
							<text>开启定位</text>
							<text class="fr cr-grey">去设置</text>
						</view> -->
						<view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-b" @tap="aboutus">
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-b" @tap="call_event"> -->
							<text>{{$t('accountUser.gywm')}}</text>
						</view>
						<view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right br-b">
							<text>{{$t('accountUser.bbxx')}}</text>
							<text class="fr cr-grey">{{version_info}}</text>
						</view>
						<!-- <view class="padding-top-xxl padding-bottom-xxl padding-right-xxxl arrow-right"
							data-value="/pages/logout/logout" @tap="url_event">
							<text>账号注销</text>
							<text class="fr cr-grey">注销后无法恢复</text>
						</view> -->
					</view>
					<button @tap="remove_user_cache_event" style="margin-top: 10rpx;">退出登录</button>
				</block>
		
				<!-- 错误提示 -->
				<component-no-data :propStatus="data_list_loding_status"
					:propMsg="data_list_loding_msg"></component-no-data>
			</view>
		</view>
		<showLoading v-else></showLoading>
		
		
		<uni-popup ref="emailPopup" type="center" :is-mask-click="false">
		   	<view class="remarksinfo" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<text>{{$t('message.yxrz')}}</text>
		   			</view>
					<view class="content">
						<u--form labelPosition="top" :model="formData" :rules="rules" ref="uForm" >
							<u-form-item :label="$t('message.yxxx')" borderBottom labelWidth="300rpx" prop="emailInfo">
								<u-input v-model="formData.emailInfo" border="none" :placeholder="$t('message.qsryxxx')"
								 type="text" @input="validatePhoneNumber">
									<u--text :text="$t('message.yxxx')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
								</u-input>
							</u-form-item>
							
							<u-form-item :label="$t('message.yxyzm')" borderBottom labelWidth="300rpx" prop="emailCode">
								<u--input v-model="formData.emailCode" type="number" border="none" :placeholder="$t('message.yxyzm')" clearable></u--input>
								<template slot="right">
									<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
									<u-button @tap="getCode" :text="btntext" type="warning" size="mini" style="background: linear-gradient(to right, #2A3A88FF, #3c9cff) !important;"></u-button>
								</template>
							</u-form-item>
							
						</u--form>
					</view>
					<view class="bottombtn">
						<view class="btnbtn">
							<u-button iconColor="#FF4000" :text="$t('search.QX')"  @tap="closepopup" class="cancelbtn"></u-button>
						</view>
						<view class="btnbtn">
						  <u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.qd')"  @tap="submitemail" :loading="subloading"></u-button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
		<uni-popup ref="tgPopup" type="center" :is-mask-click="false">
		   	<view class="tginfo" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
		   				<text>{{$t('message.tgrz')}}</text>
		   			</view>
					<view class="content">
						<u--form labelPosition="top" :model="formData" :rules="rules" ref="tguForm" >
							<u-form-item :label="$t('message.tgxx')" borderBottom labelWidth="300rpx" prop="tgInfo">
								<u-input v-model="formData.tgInfo" border="none" :placeholder="$t('message.qsrtgxx')"
								 type="text" @input="validatePhoneNumber">
									<u--text :text="$t('message.tgxx')" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
								</u-input>
							</u-form-item>
						</u--form>
					</view>
					<view class="bottombtn">
						<view class="btnbtn">
							<u-button iconColor="#FF4000" :text="$t('search.QX')"  @tap="closeTGpopup" class="cancelbtn"></u-button>
						</view>
						<view class="btnbtn">
						  <u-button color="linear-gradient(to right, #FF4000 , #FF4000)" :text="$t('search.qd')"  @tap="submitTG" :loading="subloading"></u-button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
	</view>
</template>
<script>
	const app = getApp();
	import componentNoData from '../../components/no-data/no-data';
	import { mapState,mapActions } from 'vuex'
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				visible: false,
				theme_view: app.globalData.get_theme_value_view(),
				data_list_loding_status: 1,
				data_list_loding_msg: '',
				default_avatar: app.globalData.data.default_user_head_src,
				user: null,
				common_app_customer_service_tel: null,
				plugins_invoice: null,
				version_info: "",
				btntext: this.$t('message.hqyzm'),
				jjmmName: "",
				jjmmstatus: "",
				memberId: "",
				emailname: "",
				emailstatus: "",
				emaildata: {},
				tgname: "",
				tgstatus: "",
				tgdata: {},
				formData: {
					memberId: '',
					emailInfo: "",
					emailCode: "",
					tgInfo: ""
				},
				subloading: false,
				rules: {
					emailInfo: {
						required: true,
						message: this.$t('message.qsryxxx'),
						trigger: ['blur']
					},
					tgInfo: {
						required: true,
						message: this.$t('message.qsrtgxx'),
						trigger: ['blur']
					},
					emailCode: {
						required: true,
						message: this.$t('message.qsryzmyzm'),
						trigger: ['blur']
					}
				},
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			};
		},

		components: {
			componentNoData,
		},
		computed:{
			...mapState({
				userInfo: state => state.user.userInfo
			}),
			// location() {
			// 	return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			// },
		},
		onShow() {
			// 数据加载
			this.init();

			// 初始化配置
			this.init_config();
		},
		onLoad() {
			setTimeout(() => {
				this.$data.visible = true
			}, 1200)
		},

		methods: {
			...mapActions(['outLogin']),
			// 初始化配置
			init_config(status) {
				if ((status || false) == true) {
					this.setData({
						common_app_customer_service_tel: app.globalData.get_config(
							'config.common_app_customer_service_tel'),
						plugins_invoice: app.globalData.get_config('plugins_base.invoice', null),
					});
				} else {
					app.globalData.is_config(this, 'init_config');
				}
			},
			closepopup() {
				this.$refs.emailPopup.close()
				this.formData.emailInfo = ''
				this.formData.emailCode = ''
				this.subloading = false
				// this.$refs.emailPopup.resetFields();
			},
			closeTGpopup() {
				this.$refs.tgPopup.close()
				this.formData.tgInfo = ''
				this.subloading = false
				this.$refs.tguForm.resetFields();
			},
			submitemail() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						const data = {
							emailInfo: this.formData.emailInfo,
							memberId: this.memberId,
							emailCode: this.formData.emailCode
						}
						if(this.subloading) {
							return
						}
						this.subloading = true
						api({
							url: `mb/member/email/auth/info?location=${this.location}`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: data
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									// title: '操作成功',
									title: '认证成功',
									icon:'none'
								})
								this.closepopup()
								// setTimeout(() => {
								// 	this.getauthStatus()
								// }, 1000)
							}
						})
					}
				})
			},
			submitTG() {
				this.$refs.tguForm.validate().then(res => {
					if(res) {
						const data = {
							tgInfo: this.formData.tgInfo,
							memberId: this.memberId,
						}
						if(this.subloading) {
							return
						}
						this.subloading = true
						api({
							url: `mb/member/tg/auth/info?location=${this.location}`,
							header: {
							    "jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: data
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									// title: '操作成功',
									title: '认证成功',
									icon:'none'
								})
								this.closeTGpopup()
								// setTimeout(() => {
								// 	this.getauthStatus()
								// }, 500)
							}
						})
					}
				})
			},
			validatePhoneNumber() {},
	        moveHandle() {},
			codeChange(text) {
				this.btntext = text;
			},
			getCode() {
				this.$refs.uForm.validateField('emailInfo', (res) => {
					if (!res.length) {
						if (this.$refs.uCode.canGetCode) {
							uni.showLoading({
								title: this.$t('message.zzhqyzm')
								
							})
							let data = {
								emailInfo: this.formData.emailInfo,
								memberId: this.memberId
							}
							api({
							    url: `mb/member/email/auth/code?location=${this.location}`,
							    header: {
							        "jarepair-platform": uni.getStorageSync("token")
							    },
							    method: 'POST',
								data: data
							}).then(res => {
								if (res.code == 200) {
									// console.log(res)
									this.$refs.uCode.start();
									uni.showToast({
										// title: '验证码发送成功',
										title: this.$t('message.yzmfscg'),
										icon: 'none'
									})
								} else {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}
							}).catch(err => {
								uni.showToast({
									title: '网络超时,请重试',
									icon: "none"
								})
							})
							
						} else {
							uni.$u.toast(this.$t('message.djsjszfs'));
						}
					}
				})
			
			},
			// 获取数据
			async init() {
				var user = this.userInfo;
				console.log(user, '个人信息')
				if (user != false) {
					// 用户未绑定手机则转到登录页面
					if (app.globalData.user_is_need_login(user)) {
						uni.redirectTo({
							url: '/pageo/login/login',
						});
						this.setData({
							data_list_loding_status: 0,
							data_list_loding_msg: this.$t('message.qxbdsj'),
						});
						return false;
					} else {
						this.setData({
							data_list_loding_status: 3,
							user: user,
						});
					}
				} else {
					this.setData({
						data_list_loding_status: 0,
						data_list_loding_msg: this.$t('message.qxdl'),
					});
				}
				await this.get_version()
				// await this.getjjmminfo()
				// await this.getauthStatus()
			},
			get_version() {
				api({
					url: 'bs/version/get/list',
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '数据数据')
						this.version_info = res.data.data.versionCode
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: "none"
					})
				})
			},
			// 获取资金密码的状态
			getjjmminfo() {
				api({
					url: `mb/login/jjmm/info?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '资金密码状态')
						const data = res.data.data
						this.jjmmName = data.statusName
						this.jjmmstatus = data.status
						this.memberId = data.memberId
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: "none"
					})
				})
			},
			// 获取对应的认证状态
			getauthStatus() {
				api({
					url: `mb/member/auth/status/list?location=${this.location}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res.data.data, '对应的认证状态')
						const data = res.data.data
						this.emailname = data[0].describe
						this.emailstatus = data[0].type
						this.emaildata = data[0].data
						
						this.tgname = data[1].describe
						this.tgstatus = data[1].type
						this.tgdata = data[1].data
						
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: "none"
					})
				})
			},
			fundpassword() {
				// 设置资金密码
				uni.navigateTo({
					url: `/pageu/fund_password/fund_password?jjmmName=${this.jjmmName}&jjmmstatus=${this.jjmmstatus}&memberId=${this.memberId}`
				})
			},
			changepassword() {
				// 修改密码
				uni.navigateTo({
					url: `/pageu/change_password/change_password`
				})
			},
			emailcertified() {
				if(this.emaildata) {
					return
				}
				console.log('邮箱认证')
				this.$refs.emailPopup.open()
			},
			tgcertified() {
				if(this.tgdata) {
					return
				}
				console.log('tg认证')
				this.$refs.tgPopup.open()
			},
			// url事件
			url_event(e) {
				console.log(e, 'eee')
				app.globalData.url_event(e);
			},
			goPersonal(e) {
				app.globalData.url_event(e);
				uni.navigateTo({
					url: "/pageo/personal/personal"
				})
			},

			// 打开小程序权限中心
			open_setting_event() {
				// uni.openSetting();
			},


			// 关于我们
			call_event() {
				if (this.common_app_customer_service_tel == null) {
					app.globalData.showToast(this.$t('accountUser.jqqd'));
				} else {
					app.globalData.call_tel(this.common_app_customer_service_tel);
				}
			},
			aboutus() {
				// 关于我们协议
				uni.navigateTo({
					// url: `/pageo/message_manage/about_us`,
					url: '/pageu/about_us/about_us'
				})
			},
			// 清除缓存
			remove_user_cache_event(e) {
				uni.showModal({
					// title: "确定要退出登录吗？",
					title: this.$t('message.qdytcdl'),
					confirmText: this.$t('search.qd'),
					cancelText: this.$t('search.QX'),
					success: (res) => {
						if (res.confirm) {
							this.outLogin()
							this.$store.dispatch("setIndex", 0)
							app.globalData.remove_user_cache_event();
							// 退出登录后触发购物车数量更新为0
							// uni.$emit('updateCartNum', 0);

						} else if (res.cancel) {
							uni.showToast({
								// title: "您已取消退出",
								title: this.$t('message.qdytcdl'),
								icon: 'none'
							})
						}
					}
				})
			},
		},
	};
</script>
<style scoped lang="scss">
	@import './setup.css';

	.padding-horizontal-main {
		background-color: #FFFFFF;
	}

	.topInfo {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-between;

		.codeImage {
			width: 30rpx;
			height: 30rpx;
			transform: translateX(-50rpx);
		}
	}

	.fl {
		.leader {
			margin-top: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	button {
		width: 100%;
		height: 87rpx;
		margin-top: 26rpx;
		background-color: #FFFFFF;
		font-size: 29rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		color: #3D3D3D;
		line-height: 87rpx;
		letter-spacing: 1px;
	}
	
	
	.remarksinfo{
			width: 650rpx;
			min-height: 600rpx;
			background-color: #fff;
			border-radius: 30rpx;
			z-index: 999;
			margin-bottom: -80rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				.topCon{
					height: 120rpx;
					line-height: 120rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #333333;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
				.content{
					height: 350rpx;
					// margin-bottom: 20rpx;
				}
				
				.bottombtn{
					display: flex;
					justify-content: space-between;
					.btnbtn{
						width: 45%;
						.cancelbtn{
							color: #FF4000 !important;
							border: 1rpx solid #FF4000 !important;
						}
					}
				}
			}
		}
		
		.tginfo{
			width: 650rpx;
			min-height: 420rpx;
			background-color: #fff;
			border-radius: 30rpx;
			z-index: 999;
			margin-bottom: -80rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				.topCon{
					height: 120rpx;
					line-height: 120rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #333333;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
				.content{
					height: 150rpx;
					margin-bottom: 20rpx;
				}
				
				.bottombtn{
					display: flex;
					justify-content: space-between;
					.btnbtn{
						width: 45%;
						.cancelbtn{
							color: #FF4000 !important;
							border: 1rpx solid #FF4000 !important;
						}
					}
				}
			}
		}
</style>

<style>
	.page {
		width: 100%;
		background-color: #F2F6FA;
		height: 100vh;
	}
</style>