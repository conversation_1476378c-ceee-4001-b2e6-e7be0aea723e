<template>
    <view :class="theme_view">
        <!-- 轮播 -->
        <view v-if="slider_list.length > 0" class="padding-horizontal-main padding-top-main">
            <component-banner :propData="slider_list" propSize="mini"></component-banner>
        </view>

        <!-- 分类 -->
        <scroll-view v-if="(activity_category || null) != null && activity_category.length > 0" class="scroll-view-horizontal bg-white oh" scroll-x="true">
            <view :class="'item cr-grey dis-inline-block padding-horizontal-main padding-top-main padding-bottom-sm ' + (nav_active_value == 0 ? 'cr-main nav-active-line bg-main-befor fw-b' : '')" @tap="nav_event" data-value="0">全部 </view>
            <block v-for="(item, index) in activity_category" :key="index">
                <view :class="'item cr-grey dis-inline-block padding-horizontal-main padding-top-main padding-bottom-sm ' + (nav_active_value == item.id ? 'cr-main nav-active-line bg-main-befor fw-b' : '')" @tap="nav_event" :data-value="item.id">{{ item.name }}</view>
            </block>
        </scroll-view>

        <!-- 列表 -->
        <scroll-view :scroll-y="true" class="scroll-box scroll-box-ece-nav" @scrolltolower="scroll_lower" lower-threshold="30" :style="slider_list.length > 0 ? 'height:calc(100vh - 320rpx);' : ''">
            <view v-if="(data_list || null) != null && data_list.length > 0" class="data-list padding-horizontal-main padding-top-main oh">
                <block v-for="(item, index) in data_list" :key="index">
                    <view class="item oh spacing-mb">
                        <navigator :url="'/pages/plugins/activity/detail/detail?id=' + item.id" hover-class="none">
                            <image :src="item.cover" mode="widthFix" class="wh-auto border-radius-main"></image>
                        </navigator>
                    </view>
                </block>
            </view>
            <view v-else>
                <!-- 提示信息 -->
                <component-no-data :propStatus="data_list_loding_status" :propMsg="data_list_loding_msg"></component-no-data>
            </view>

            <!-- 结尾 -->
            <component-bottom-line :propStatus="data_bottom_line_status"></component-bottom-line>
        </scroll-view>
    </view>
</template>
<script>
    const app = getApp();
    import componentBanner from '../../../../components/slider/slider';
    import componentNoData from '../../../../components/no-data/no-data';
    import componentBottomLine from '../../../../components/bottom-line/bottom-line';

    export default {
        data() {
            return {
                theme_view: app.globalData.get_theme_value_view(),
                data_list_loding_status: 1,
                data_list_loding_msg: '',
                data_bottom_line_status: false,
                data_is_loading: 0,
                data_list: [],
                data_total: 0,
                data_page_total: 0,
                data_page: 1,
                params: null,
                data_base: null,
                slider_list: [],
                activity_category: [],
                nav_active_value: 0,
                // 自定义分享信息
                share_info: {},
            };
        },

        components: {
            componentBanner,
            componentNoData,
            componentBottomLine,
        },
        props: {},

        onLoad(params) {
            this.setData({
                params: params,
            });

            // 数据加载
            this.get_data();
        },

        // 下拉刷新
        onPullDownRefresh() {
            this.setData({
                data_page: 1,
            });
            this.get_data_list(1);
        },

        methods: {
            // 初始化
            get_data() {
                uni.showLoading({
                    title: '加载中...',
                });
				let _this = this;
				setTimeout(function() {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					var data = {
						base: {
							"application_name": "活动",
							"home_data_list_number": "",
							"goods_detail_icon": "",
							"seo_title": "",
							"seo_keywords": "",
							"seo_desc": "",
							"is_actas_price_original": "0",
							"is_home_auto_play": "1",
							"system_type": "default"
						},
						"slider_list": [],
						"activity_category": [{
							"id": "1",
							"name": "推荐"
						}, {
							"id": "2",
							"name": "最新"
						}],
					};
					_this.setData({
					    data_base: data.base || null,
					    slider_list: data.slider_list || [],
					    activity_category: data.activity_category || [],
					});
					
					if ((_this.data_base || null) != null) {
					    // 基础自定义分享
					    _this.setData({
					        share_info: {
					            title: _this.data_base.seo_title || _this.data_base.application_name,
					            desc: _this.data_base.seo_desc,
					            path: '/pages/plugins/activity/index/index',
					        },
					    });
					
					    // 标题
					    if ((_this.data_base.application_name || null) != null) {
					        uni.setNavigationBarTitle({
					            title: _this.data_base.application_name,
					        });
					    }
					}
					
					// 获取列表数据
					_this.get_data_list(1);
				}, 2000);
				return;
                uni.request({
                    url: app.globalData.get_request_url('index', 'index', 'activity'),
                    method: 'POST',
                    data: {},
                    dataType: 'json',
                    success: (res) => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            var data = res.data.data;
                            this.setData({
                                data_base: data.base || null,
                                slider_list: data.slider_list || [],
                                activity_category: data.activity_category || [],
                            });

                            if ((this.data_base || null) != null) {
                                // 基础自定义分享
                                this.setData({
                                    share_info: {
                                        title: this.data_base.seo_title || this.data_base.application_name,
                                        desc: this.data_base.seo_desc,
                                        path: '/pages/plugins/activity/index/index',
                                    },
                                });

                                // 标题
                                if ((this.data_base.application_name || null) != null) {
                                    uni.setNavigationBarTitle({
                                        title: this.data_base.application_name,
                                    });
                                }
                            }

                            // 获取列表数据
                            this.get_data_list(1);
                        } else {
                            this.setData({
                                data_list_loding_status: 2,
                                data_list_loding_msg: res.data.msg,
                            });
                        }

                        // 分享菜单处理
                        app.globalData.page_share_handle(this.share_info);
                    },
                    fail: () => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_list_loding_status: 2,
                            data_list_loding_msg: '网络开小差了哦~',
                        });
                    },
                });
            },

            // 获取数据列表
            get_data_list(is_mandatory) {
                // 分页是否还有数据
                if ((is_mandatory || 0) == 0) {
                    if (this.data_bottom_line_status == true) {
                        uni.stopPullDownRefresh();
                        return false;
                    }
                }

                // 是否加载中
                if (this.data_is_loading == 1) {
                    return false;
                }
                this.setData({
                    data_is_loading: 1,
                });

                // 加载loding
                uni.showLoading({
                    title: '加载中...',
                });

                // 获取数据
				let _this = this;
				setTimeout(function() {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					var temp_data_list = data.data;
					_this.setData({
						data_list: temp_data_list,
						data_total: data.total,
						data_page_total: data.page_total,
						data_list_loding_status: 3,
						data_page: _this.data_page + 1,
						data_is_loading: 0,
					});

					// 是否还有数据
					_this.setData({
						data_bottom_line_status: _this.data_page > 1 && _this.data_page > _this.data_page_total,
					});
				}, 1500);
				let data = {
					data:[{
						"id": "3",
						"title": "618年中活动",
						"vice_title": "个性推荐",
						"color": "#33CCFF",
						"banner": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/08/15/1692101047167860.png",
						"describe": "厂家直销，价格优惠",
						"cover": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/11/09/1699505222324959.png",
						"keywords": "华为,苹果,戴尔",
						"url": "/pages/plugins/activity/detail/detail?id=3",
						"keywords_arr": ["华为", "苹果", "戴尔"]
					}, {
						"id": "2",
						"title": "电脑数码节开始啦！",
						"vice_title": "福利多多",
						"color": "#000000",
						"banner": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/08/15/1692093549233926.png",
						"describe": "满999减50",
						"cover": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/11/09/1699505222449903.png",
						"keywords": "小米,手机,iphone",
						"url": "/pages/plugins/activity/detail/detail?id=2",
						"keywords_arr": ["小米", "手机", "iphone"]
					}, {
						"id": "1",
						"title": "耐克联名共创",
						"vice_title": "全年最低价、活动优",
						"color": "#996633",
						"banner": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/08/15/1692095015316979.png",
						"describe": "福利满满",
						"cover": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/11/09/1699505222262356.png",
						"keywords": "潮牌,名品",
						"url": "/pages/plugins/activity/detail/detail?id=1",
						"keywords_arr": ["潮牌", "名品"]
					}, {
						"id": "4",
						"title": "奢宠礼遇 怦然心动",
						"vice_title": "七夕精选",
						"color": "",
						"banner": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/08/16/1692151894312306.png",
						"describe": "【七夕礼物】爱要有'礼'才完美,不一样的礼物,送给不一样的她。",
						"cover": "https://d1cdn.shopxo.vip/static/upload/images/plugins_activity/2023/11/09/1699505222667536.png",
						"keywords": "七夕,爆款,直降",
						"url": "/pages/plugins/activity/detail/detail?id=4",
						"keywords_arr": ["七夕", "爆款", "直降"]
					}],
					page_total: 1,
					total: 4
				};
				return;
                uni.request({
                    url: app.globalData.get_request_url('datalist', 'index', 'activity'),
                    method: 'POST',
                    data: {
                        page: this.data_page,
                        category_id: this.nav_active_value || 0,
                    },
                    dataType: 'json',
                    success: (res) => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        if (res.data.code == 0) {
                            var data = res.data.data;
                            if (data.data.length > 0) {
                                if (this.data_page <= 1) {
                                    var temp_data_list = data.data;
                                } else {
                                    var temp_data_list = this.data_list || [];
                                    var temp_data = data.data || [];
                                    for (var i in temp_data) {
                                        temp_data_list.push(temp_data[i]);
                                    }
                                }
                                this.setData({
                                    data_list: temp_data_list,
                                    data_total: data.total,
                                    data_page_total: data.page_total,
                                    data_list_loding_status: 3,
                                    data_page: this.data_page + 1,
                                    data_is_loading: 0,
                                });

                                // 是否还有数据
                                this.setData({
                                    data_bottom_line_status: this.data_page > 1 && this.data_page > this.data_page_total,
                                });
                            } else {
                                this.setData({
                                    data_list_loding_status: 0,
                                    data_is_loading: 0,
                                });
                                if (this.data_page <= 1) {
                                    this.setData({
                                        data_list: [],
                                        data_bottom_line_status: false,
                                    });
                                }
                            }
                        } else {
                            this.setData({
                                data_list_loding_status: 0,
                                data_list_loding_msg: res.data.msg,
                                data_is_loading: 0,
                            });
                            app.globalData.showToast(res.data.msg);
                        }
                    },
                    fail: () => {
                        uni.hideLoading();
                        uni.stopPullDownRefresh();
                        this.setData({
                            data_list_loding_status: 2,
                            data_is_loading: 0,
                        });
                        app.globalData.showToast('网络开小差了哦~');
                    },
                });
            },

            // 滚动加载
            scroll_lower(e) {
                this.get_data_list();
            },

            // 导航事件
            nav_event(e) {
                this.setData({
                    nav_active_value: e.currentTarget.dataset.value || 0,
                    data_page: 1,
                });
                this.get_data_list(1);
            },
        },
    };
</script>
<style>
    @import './index.css';
</style>
