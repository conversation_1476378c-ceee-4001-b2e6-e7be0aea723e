<template>
	<view class="page">
		<view class="header" :style="{ 'lineHeight': navBarHeight + 'rpx' }">
			<newNavBar title="订单优惠" width="100vw" @clickLeftIcon="goBack"></newNavBar>
			<view class="select">
				<u-tabs :list="subsectionList" @click="sectionChange" :current="current" keyName="name" lineWidth="48"
					lineColor="#FF6900"
					:activeStyle="{ color: '#FF6900', fontWeight: 'bold', transform: 'scale(1.05)', fontSize: '38rpx'}"
					:inactiveStyle="{ color: '#333', transform: 'scale(1)', fontSize: '32rpx'}"
					itemStyle="padding: 0 30rpx; height: 38px;"></u-tabs>
			</view>
		</view>
		<view class="orderView" v-if="visible">
			<view class="orderorder" style="padding-top: 250rpx;">
				<view class="SeaProList">
					<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
						:use-page-scroll="true" :auto="false" :auto-show-back-top-top="true">
						<refresh-loading slot="refresher"></refresh-loading>
						<view class="orderList" v-if="orderList.length > 0">
							<view class="orderitem" v-for="(item,index) in orderList" :key="item.orderId">
								<view class="orderdetail">
									<view class="Storeinfo" v-if="item.typeOrder == 0">
										<view class="storeName">
											<view class="name">
												{{item.adContactArea }}
											</view>
										</view>
										<view class="checkbox">
											<u-checkbox-group v-model="item.isSelect" shape="circle" activeColor="#FF6900 "
												@change="checkboxChange(item)">
												<u-checkbox label="." :name="1" size="24rpx" labelSize="24rpx"
													labelColor="#fff"></u-checkbox>
											</u-checkbox-group>
										</view>
									</view>

									<view class="Storeinfo" v-if="item.typeOrder !== 0">
										<view class="storeName">
											<view class="name">
												{{item.mxList[0].productTitle }}
											</view>
										</view>
										<view class="checkbox">
											<u-checkbox-group v-model="item.isSelect" shape="circle" activeColor="#FF6900 "
												@change="checkboxChange(item)">
												<u-checkbox label="." :name="1" size="24rpx" labelSize="24rpx"
													labelColor="#fff"></u-checkbox>
											</u-checkbox-group>
										</view>
									</view>
									<view class="mxList" v-if="item.mxList.length > 0">
										<view class="mxitem" v-for="(items,i) in item.mxList.slice(0, 2)" :key="i"
											@tap="goOrderdetail(item)">
											<view class="mxdetail">
												<view class="">
													<image :src="items.productMainImg" mode=""
														style="width: 150rpx; height: 150rpx;border-radius: 50%;">
													</image>
												</view>
												<view class="right">
													<view class="righttop">
														<view class="proname">
															{{items.productTitle}}
														</view>
													</view>
													<view class="rightcenter">
														<view class="">
															<text>{{items.extendDt1 || '预约的日期数据'}}</text>
														</view>
														<!-- 商品类型的订单显示数量 -->
														<view class="sellQuantity" v-if="item.typeOrder == 1">
															<text>× {{items.sellQuantity}}</text>
														</view>
													</view>
													<view class="skudetail">
														<view class="sku">
															{{items.extendDt2 || '预约的时间数据'}}
														</view>

													</view>
												</view>
											</view>
										</view>
									</view>

									<view class="mxList" v-if="item.mxList.length > 2 && item.isshowMore">
										<view class="mxitem" v-for="(items,i) in item.mxList.slice(2)" :key="i"
											@tap="goOrderdetail(item)">
											<view class="mxdetail">
												<view class="">
													<image :src="items.productMainImg" mode=""
														style="width: 150rpx; height: 150rpx;border-radius: 50%;">
													</image>
												</view>
												<view class="right">
													<view class="righttop">
														<view class="proname">
															{{items.productTitle}}
														</view>
													</view>
													<view class="rightcenter">
														<view class="">
															<text>{{items.extendDt1 || '预约的日期数据'}}</text>
														</view>
														<!-- 商品类型的订单显示数量 -->
														<view class="sellQuantity" v-if="item.typeOrder == 1">
															<text>× {{items.sellQuantity}}</text>
														</view>
													</view>
													<view class="skudetail">
														<view class="sku">
															{{items.extendDt2 || '预约的时间数据'}}
														</view>

													</view>
												</view>
											</view>
										</view>
									</view>

									<view class="showMore" v-if="item.mxList.length > 2" @tap="showMore(index)">
										<text class="showMore-text">{{item.isshowMore ? '收起' : '查看更多'}}</text>
										<image v-if="item.isshowMore"
											src="https://file.36sm.cn/xtjyjt/images/common/detail/up.png" mode=""
											style="width: 35rpx;height: 35rpx;"></image>
										<image v-if="!item.isshowMore"
											src="https://file.36sm.cn/xtjyjt/images/common/detail/down.png" mode=""
											style="width: 30rpx;height: 30rpx;"></image>
									</view>
									<view class="totalPrice" v-if="item.orderStatus == 1">
										<!-- <text class="statusName">{{item.orderStatusName}}:</text> -->
										<text class="pricevalue">￥ {{item.payAmountTrade}}</text>
									</view>

									<view class="totalPrice" v-if="item.typeOrder == 1">
										<!-- <text class="statusName">{{item.orderStatusName}}:</text> -->
										<text class="pricevalue">￥ {{item.payAmountTrade}}</text>
									</view>
									<!-- <view class="zfbtn">
										<view class="lefticon" @click="showTabbar(item,index)">
											<image src="/static/images/threeMore.png" mode=""
												style="width: 30rpx;height: 30rpx;"></image>
										</view>
										<view class="showTab" v-if="item.isshowTab">
											<view class="delOrder" v-if="thisOrderDetail.orderStatus  == 0"
												@click="CancelPay">
												<text>取消支付</text>
											</view>
											<view class="delOrder" @click="OrderDel"
												v-if="thisOrderDetail.orderStatus !==  4 && thisOrderDetail.orderStatus !==  8">
												<text>删除订单</text>
											</view>
											<view class="delOrder" @click="linkkefu">
												<text>联系客服</text>
											</view>
										</view>
										<view class="rightbtn">
											<view class="ljzf" @click="OneMoreOrder(item)" v-if="item.typeOrder == 0">
												<text>再来一单</text>
											</view>
											<view class="tuidan" @click="Chargebacks(item)"
												v-if="item.orderStatus == 4">
												<text>退单</text>
											</view>
										</view>
									</view> -->

								</view>
							</view>
						</view>
						<view class="noOrder" v-if="orderList.length == 0">
							<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode=""
								style="width: 200rpx;height: 200rpx;"></image>
							<text class="notitle">{{$t('accountUser.zwdd')}}</text>
						</view>

						<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
						<component-bottom-line :propStatus="bottom_line_status" :propMsg="$t('accountUser.mysjl')"
							v-if="!loadingStatus && orderList.length > 0"></component-bottom-line>
					</z-paging>
				</view>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>

		<uni-popup ref="infoPopup" type="center" :is-mask-click="false">
			<view class="Popupinfo">
				<view class="PopupCon">
					<view class="topCon">
						<text>{{infotitle}}</text>
					</view>
					<view class="content" v-if="infotitle == '国际物流'">
						<view class="company">
							<view class="title">
								<text>国际物流公司</text>
							</view>
							<view class="conten">
								<text>{{logisticsInfo.intnlLogisticsName}}</text>
								<image src="https://file.36sm.cn/xtjyjt/images/common/copy.png" mode=""
									style="width: 32rpx;height: 32rpx;margin-left: 10rpx;"
									@tap="CopyID(logisticsInfo.intnlLogisticsName)"></image>
							</view>
						</view>
						<view class="orderNum">
							<view class="title">
								<text>国际物流单号</text>
							</view>
							<view class="conten">
								<text>{{logisticsInfo.intnlLogisticsOrder}}</text>
								<image src="https://file.36sm.cn/xtjyjt/images/common/copy.png" mode=""
									style="width: 32rpx;height: 32rpx;margin-left: 10rpx;"
									@tap="CopyID(logisticsInfo.intnlLogisticsOrder)"></image>
							</view>
						</view>

					</view>
					<view class="bottombtn">
						<u-button color="linear-gradient(to right, #FF6900 , #FF6900)" :text="$t('search.wzdl')"
							@tap="closeinfo"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- <u-back-top :scroll-top="scrollTop" top="450" :customStyle="{
						backgroundColor: '#fff',
						color: '#000',
						boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.1)'
					}"></u-back-top> -->
	</view>
</template>

<script>
	import {
		postCancelPaymentOrder,
		getShoppingOrder,
		getTypeOrder
	} from "@/common/request/api/goods.js"
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	import newNavBar from '../../components/newNavBar/newNavBar.vue'

	import api from '@/common/request/index.js'
	import {
		data
	} from "../../uni_modules/uview-ui/libs/mixin/mixin";
	export default {
		data() {
			return {
				current: 0,
				statusBarHeight: 0,
				spProductName: '',
				visible: false,
				orderList: [],
				searchValue: '',
				// 下拉触底加载效果
				loadingStatus: false,
				orderShow: false,
				params: {
					page: 1,
					size: 10
				},
				navBarHeight: 0,
				// 全部
				orderStatus: "100",
				butText: '',
				totalPage: 1,
				status: 'loadmore',
				pages: {
					page: 1,
					size: 10,
				},
				subsectionList: [
					// {
					// 	name: '全部',
					// 	orderStatus: ""
					// }
				],
				TimeData: [],
				bottom_line_status: false,
				isPayloading: false,
				logisticsInfo: {},
				location: uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh',
				thisOrderId: '',
				thisOrderIndex: 0,
				thisOrderDetail: {},
				scrollTop: 0,
				scrollTimer: null,
				orderType: '',
				yhOrderId: '',
				yhType: '',
				isSelect: [],
				hyId: '',
				selectItem: {}
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading,
			componentCopyright,
			newNavBar

		},
		computed: {
			listHeight() {
				if (this.statusBarHeight) {
					let screenHeight = getApp().globalData.screenHeight
					let bottom = uni.getStorageSync('cache_shop_system_info_key').safeAreaInsets.bottom
					return screenHeight - this.statusBarHeight - bottom + 'px'
				}
			},
			// location() {
			// 	return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			// },
		},
		// onPageScroll(e) {
		// 	// console.log(e, '页面滚动监听')
		// 	const top = uni.upx2px(100)
		// 	const {
		// 		scrollTop
		// 	} = e
		// 	let percent = scrollTop / top
		// 	this.scrollTop = scrollTop
		// },
		onLoad(opt) {
			console.log(opt, '路径参数')
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});
			if (opt.orderId) {
				this.yhOrderId = opt.orderId
				this.yhType = opt.type
				this.getOrderType()
			}
			if (opt.index) {
				this.current = Number(opt.index)
			} else {
				this.current = 0
			}
			if (opt.item) {
				let item = JSON.parse(opt.item)
				console.log(item, '路径获取到的参数')
				this.orderStatus = item.orderStatus
				this.orderType = item.orderType
			}
			this.params.page = 1
			console.log(this.orderStatus, '当前tab所处状态')
			this.getStausOList()
		},
		onShow() {
			console.log('onshowonshow')
			this.params.page = 1
			// this.getStausOList()
		},
		methods: {
			checkboxChange(item) {
				console.log(item, 'eee')
				this.selectItem = item
				console.log(this.selectItem, 'selectItem')
				this.hyId = item.yhId
				if (this.isSelect.length == 0) {
					uni.showLoading({
						title: '优惠计算中'
					})
					setTimeout(() => {
						uni.hideLoading()
					}, 400)
					this.getSetyh()
				}
			},
			getSetyh() {
				api({
					url: `goods/yh/set/yh/list?hyId=${this.hyId}&hyType=${this.yhType}&orderId=${this.yhOrderId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data
						console.log(data, 'data')
						data.yhId = this.selectItem.yhId
						setTimeout(() => {
							uni.navigateBack({
								delta: 1,
								success: () => {
									uni.$emit('getdisCount', {
										disCountData: data
									})
								}
							})
						}, 500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			queryList() {
				console.log('2222222222222222222222')
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 500)
				this.params.page = 1
				this.params.size = 10
				return
				this.getStausOList()
			},
			onReachBottom() {
				console.log('上拉触底222')
				if (this.loadingStatus) return
				// clearTimeout(this.scrollTimer)
				// this.scrollTimer = setTimeout(() => {
				this.loadingStatus = true
				setTimeout(() => {
					this.params.page++
					this.getStausOList()
				}, 200)
				// }, 400); // 设置适当的延迟
			},
			// 下拉触底
			onscrollBottom() {},
			showTabbar(item, index) {
				this.thisOrderDetail = item
				this.thisOrderId = item.orderId
				this.thisOrderIndex = index
				item.isshowTab = !item.isshowTab
				// this.orderList[index].isshowTab = !this.orderList[index].isshowTab
				console.log(this.thisOrderId, '当前订单item的id')
			},
			linkkefu() {
				// uni.showLoading({
				// 	title: '功能待添加'
				// })
				uni.showToast({
					title: '功能待添加',
					icon: 'none'
				})
			},
			OrderDel() {
				// 用户删除订单
				uni.showModal({
					title: '温馨提示',
					content: '您是否确定删除此订单? 删除后不可恢复',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					cancelColor: '#333',
					confirmColor: '#FF6900',
					success: (res) => {
						if (res.confirm) {
							this.orderList[this.thisOrderIndex].isshowTab = !this.orderList[this
								.thisOrderIndex].isshowTab
							uni.showLoading({
								title: '加载中'
							})
							this.DelOrder()
							return
						} else if (res.cancel) {
							console.log('用户点击取消');
							this.orderList[this.thisOrderIndex].isshowTab = !this.orderList[this
								.thisOrderIndex].isshowTab
							return
						}

					}
				});
			},
			OrderCel() {
				uni.showModal({
					title: '温馨提示',
					content: '您是否确定取消此订单? 取消后不可恢复',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					cancelColor: '#333',
					confirmColor: '#FF6900',
					success: (res) => {
						if (res.confirm) {
							this.orderList[this.thisOrderIndex].isshowTab = !this.orderList[this
								.thisOrderIndex].isshowTab
							uni.showLoading({
								title: '加载中'
							})
							this.CelOrder()
							return
						} else if (res.cancel) {
							console.log('用户点击取消');
							this.orderList[this.thisOrderIndex].isshowTab = !this.orderList[this
								.thisOrderIndex].isshowTab
							return
						}

					}
				});
			},
			CancelPay() {
				// 用户取消支付
				uni.showModal({
					title: '温馨提示',
					content: '您是否确定取消支付此订单? 取消后不可恢复',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					cancelColor: '#333',
					confirmColor: '#FF6900',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '加载中'
							})
							console.log('取消订单操作')
							this.PayCancel()
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
						this.orderList[this.thisOrderIndex].isshowTab = !this.orderList[this.thisOrderIndex]
							.isshowTab
						return
					}
				});
			},
			CelOrder() {
				const params = {
					orderId: this.thisOrderId
				}
				api({
					url: `car/qx/order/info`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						})
						this.params.page = 1
						this.params.size = 10
						this.getStausOList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					uni.hideLoading()
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			// 会员取消支付
			PayCancel() {
				const params = {
					orderId: this.thisOrderId
				}
				api({
					url: `goods/cance/order/mx`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						})
						this.params.page = 1
						this.params.size = 10
						this.getStausOList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					uni.hideLoading()
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			DelOrder() {
				const params = {
					orderId: this.thisOrderId
				}
				api({
					url: `goods/del/order/mx`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						})
						this.params.page = 1
						this.params.size = 10
						this.getStausOList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					uni.hideLoading()
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
					uni.hideLoading()
				})
			},
			OneMoreOrder(item) {
				console.log(item, '再来一单')
				console.log(item.typeOrder, '订单 1/商品报名订单   0/预约订单')
				if (item.typeOrder == 0) {
					// 进入当前店铺
					uni.navigateTo({
						url: `/pageg/shop_detail/index?placeId=${item.adUserName}`
					})
				}
			},
			CopyID(content) {
				console.log(content, 'content')
				// 复制链接
				// #ifdef APP
				uni.setClipboardData({
					data: content, // 你要复制的内容
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					},
					fail: function() {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
				// #endif
				// #ifndef APP
				uni.showToast({
					title: this.$t('message.gndtj'),
					icon: 'none',
				});
				// #endif
			},
			TimeonChange(time, index) {
				// console.log(time, index)
				this.TimeData[index].minutes = time.minutes
				this.TimeData[index].seconds = time.seconds
				// console.log(this.TimeData, 'TimeData')
			},
			showMore(index) {
				this.orderList[index].isshowMore = !this.orderList[index].isshowMore
			},
			goSearch() {
				uni.navigateTo({
					url: "/pageo/search/search"
				})
			},
			goshopdetail(item) {
				// 店铺详情
				uni.navigateTo({
					url: `/pageu/shopdetail/shopdetail?sid=${item.shopCode}&shopName=${item.shopName}`
				})
			},
			Chargebacks(item) {
				console.log(item, '退单')
				uni.navigateTo({
					// url: `/pageg/charge-back/index?orderDetail=${JSON.stringify(item)}`
					url: `/pageg/charge-back/index?orderDetail=${encodeURIComponent(JSON.stringify(item))}`
				})
			},
			goOrderdetail(item) {
				console.log(item, '订单详情', item.typeOrder)
				return
				// item.typeOrder   1/商品订单   0/预约订单
				// uni.navigateTo({
				// 	url: `/pageg/oder_detail/index?orderDetail=${JSON.stringify(item)}&isDetail=1`
				// })

				uni.navigateTo({
					url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(item))}&isDetail=1`
				});
			},
			AfterSale(item) {
				console.log(item, '售后客服')
			},
			logistics(item) {
				this.infotitle = "国际物流"
				console.log(item, '国际物流')
				this.getlogisticsInfo(item.orderBbsId)
			},
			customsinfo(item) {
				this.infotitle = "报关信息"
				console.log(item, '报关信息')
				this.getlogisticsInfo(item.orderBbsId)
			},
			closeinfo() {
				this.$refs.infoPopup.close()
				this.logisticsInfo = {}
			},
			goBack() {
				// uni.switchTab({
				// 	url: "/pages/account/user"
				// })
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			gjpayOrder(item) {
				console.log(item, 'item')
				const orderBbsIdList = []
				orderBbsIdList.push(item.orderBbsId)
				const params = {
					orderBbsIdList: orderBbsIdList
				}
				if (this.isPayloading) {
					return
				}

				uni.showLoading({
					title: this.$t('search.jiaz')
				});
				console.log(params, '提交参数')
				this.isPayloading = true
				setTimeout(() => {
					this.isPayloading = false
				}, 6000)
				api({
					url: `car/lj/zf/order/list/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						const ConfirmOInfo = res.data.data
						console.log(ConfirmOInfo, 'ConfirmOInfo')
						// 进入支付页面
						uni.navigateTo({
							url: `/pages/digital-avatar/GJConfirmOInfo?ConfirmOInfo=${encodeURIComponent(JSON.stringify(ConfirmOInfo))}`
						})
						uni.hideLoading();
						this.isPayloading = false
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			payOrder(item) {
				if (this.isPayloading) {
					return
				}
				uni.showLoading({
					title: this.$t('search.jiaz')
				});
				console.log(item, 'item')
				const orderBbsIdList = []
				orderBbsIdList.push(item.orderBbsId)
				const params = {
					orderBbsIdList: orderBbsIdList
				}
				console.log(params, '提交参数')
				this.isPayloading = true
				setTimeout(() => {
					this.isPayloading = false
				}, 6000)
				api({
					url: `car/lj/zf/order/list/info?location=${this.location}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						const ConfirmOInfo = res.data.data
						console.log(ConfirmOInfo, 'ConfirmOInfo')
						// 进入支付页面
						uni.navigateTo({
							url: `/pageg/digital-avatar/ConfirmOInfo?ConfirmOInfo=${encodeURIComponent(JSON.stringify(ConfirmOInfo))}`
						})
						uni.hideLoading();
					} else {
						uni.showToast({
							// title: '状态码异常',
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			getlogisticsInfo(id) {
				api({
					url: `goods/get/order/other/info?location=${this.location}&orderBbsId=${id}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						console.log(res, 'res')
						this.logisticsInfo = res.data.data
						this.$refs.infoPopup.open()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			SearchPro() {},
			// 获取订单优惠的类型
			getOrderType() {
				api({
					url: `goods/yh/get/yh/type/list?location=${this.location}&orderId=${this.yhOrderId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data
						console.log(data, '订单状态类型')
						this.subsectionList = [...data]
						// this.current = this.subsectionList.findIndex((item) => item.orderStatus == this.yhType);
						this.subsectionList = [
							...data.map(item => ({
								...item,
								badge: {
									...item.badge,
									value: item.quantity
								}
							}))
						]
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			getStausOList() {
				api({
					url: `goods/yh/get/yh/list?location=${this.location}&hyType=${this.yhType}&page=${this.params.page}&size=${this.params.size}&orderId=${this.yhOrderId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					setTimeout(() => {
						this.visible = true
					}, 1200)

					if (res.code == 200) {
						const data = res.data.data
						console.log(data.list, '订单列表数据')

						data.list.forEach(item => {
							item.isshowTab = false; // 默认为false，表示不显示操作栏
							item.isshowMore = false
						});
						if (this.orderList.length == data.totalCount) {
							if (this.params.page == 1) {
								this.orderList = [...data.list]
							}

							// 数据加载完全
							setTimeout(() => {
								this.loadingStatus = false
								this.bottom_line_status = true
							}, 400)
						} else {
							if (this.params.page == 1) {
								this.orderList = [...data.list]
							} else {
								this.orderList = [...this.orderList, ...data.list]
							}
							// 确保视图刷新
							this.$set(this, 'orderList', this.orderList);
							setTimeout(() => {
								this.loadingStatus = false
							}, 500)
						}
					} else {
						uni.showToast({
							title: res.msg || '请求超时',
							icon: "none"
						});
					}
					console.log(this.orderList, '订单列表')
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			sectionChange(item) {
				console.log(item, 'item')
				this.isSelect = []
				uni.showLoading({
					title: '数据加载中'
				})
				setTimeout(() => {
					uni.hideLoading()
				}, 600)
				this.params.page = 1
				this.current = item.index;
				this.yhType = item.orderStatus
				this.getStausOList()
			},
			searchOrder() {
				console.log('搜索操作')
			},
		},
	}
</script>

<style>
	.page {
		background-color: #FBF5F1;
		/* width: 100vw; */
		height: 100vh;
		/* overflow-x: hidden; */
	}
</style>

<style lang="scss" scoped>
	.sim-text {
		font-size: 20rpx;
		color: #999999;
	}

	::v-deep {
		.u-count-down__text {
			font-size: 24rpx;
			line-height: 24rpx;
			color: red !important;
		}
	}

	.header {
		width: 100%;
		height: 250rpx;
		background-color: #FFE8DA;
		// background-color: #FBF5F1;
		position: fixed;
		z-index: 999;

		.box-bg {
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			line-height: 33rpx;

			.viewtitle {
				margin-left: 25rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 30rpx;
				color: #333333;
				font-style: normal;
				text-transform: none;
			}
		}

		.select {
			u-tabs .u-tabs-item {
				padding-left: 0 !important;
				padding-right: 0 !important;
			}
		}
	}

	.orderView {
		.orderorder {
			.SeaProList {
				width: 100%;

				.orderList {
					.orderitem {
						width: 96%;
						background-color: #fff;
						border-radius: 15rpx;
						margin: 15rpx auto;

						.orderdetail {

							// padding-bottom: 10rpx;
							.Storeinfo {
								width: 94%;
								margin: 0 auto;
								height: 80rpx;
								display: flex;
								align-items: center;
								justify-content: space-between;
								border-bottom: 1rpx solid #ededed;

								.storeName {
									display: flex;
									align-items: center;

									.name {
										color: #000;
										font-weight: 600;
										margin-right: 12rpx;
										max-width: 400rpx;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis
									}
								}

								.orderstatus {
									color: #ccc;
									font-size: 25rpx;
									letter-spacing: 2rpx;
									font-weight: 300;
								}
							}

							.mxList {
								.mxitem {
									// background-color: skyblue;
									width: 94%;
									margin: 0 auto;
									padding: 15rpx 0;

									// border-bottom: 1rpx solid #ededed;
									.mxdetail {
										display: flex;
										justify-content: space-between;

										.right {
											// background-color: skyblue;
											flex: 1;
											margin-left: 15rpx;
											display: flex;
											flex-direction: column;
											justify-content: space-between;

											.righttop {
												font-weight: 600;
												color: #000;
												display: flex;
												justify-content: space-between;
												margin-bottom: 10rpx;

												.proname {
													width: 300rpx;
													white-space: nowrap;
													overflow: hidden;
													text-overflow: ellipsis;
												}
											}

											.rightcenter {
												display: flex;
												justify-content: space-between;
												font-family: PingFang SC, PingFang SC;
												font-weight: 400;
												font-size: 28rpx;
												color: #999999;
												font-style: normal;
												text-transform: none;
											}

											.skudetail {
												display: flex;
												justify-content: space-between;
												margin-top: 10rpx;
												font-family: PingFang SC, PingFang SC;
												font-weight: 500;
												font-size: 28rpx;
												color: #333333;
												font-style: normal;
												text-transform: none;

												.sku {
													font-family: PingFang SC, PingFang SC;
													font-weight: 600;
													font-size: 24rpx;
													color: #555;
													font-style: normal;
													text-transform: none;
													width: 300rpx;
													white-space: nowrap;
													overflow: hidden;
													text-overflow: ellipsis;
												}

											}

											.bottomC {
												color: #ccc;
												font-weight: 300;
											}
										}
									}
								}
							}

							.showMore {
								// background-color: skyblue;
								display: flex;
								align-items: center;
								justify-content: center;
								color: #c1c1c1;
							}

							.totalPrice {
								margin: 0 auto;
								width: 94%;
								display: flex;
								align-items: center;
								justify-content: flex-end;
								padding-top: 5rpx;

								.statusName {
									margin-right: 8rpx;
								}

								.pricevalue {
									font-weight: 600;
									font-family: PingFang SC, PingFang SC;
									font-size: 28rpx;
									color: #333333;
									font-style: normal;
									text-transform: none;
								}
							}

							.zfbtn {
								margin: 0 auto;
								width: 94%;
								display: flex;
								justify-content: space-between;
								align-items: center;
								padding: 10rpx;
								position: relative;

								.lefticon {}

								.showTab {
									position: absolute;
									padding: 15rpx 20rpx;
									border-radius: 10rpx;
									background-color: #fff;
									left: 10rpx;
									bottom: 80rpx;
									box-shadow: 4rpx 4rpx 10rpx rgba(0, 0, 0, 0.2);
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 22rpx;
									color: #999999;
									font-style: normal;
									text-transform: none;

									&::before {
										content: '';
										position: absolute;
										left: 5%;
										bottom: -10rpx;
										/* 距离左下角的距离 */
										transform: translateX(-5%);
										width: 0;
										height: 0;
										border-left: 10rpx solid transparent;
										border-right: 10rpx solid transparent;
										border-top: 10rpx solid #fff;
										/* 三角形的颜色和背景色一致 */
									}

									.delOrder {
										letter-spacing: 2rpx;
										padding: 10rpx;
										text-align: center;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 26rpx;
										color: #000000;
										font-style: normal;
										text-transform: none;
									}
								}

								.rightbtn {
									display: flex;
									align-items: center;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 22rpx;
									font-style: normal;
									text-transform: none;

									.ljzf {
										padding: 8rpx 15rpx;
										color: #FF6900;
										border-radius: 94rpx;
										border: 2rpx solid #FF6900;
										min-width: 80rpx;
										text-align: center;
									}

									.tuidan {
										margin-left: 10rpx;
										padding: 8rpx 15rpx;
										color: #FF6900;
										text-align: center;
										border-radius: 94rpx;
										min-width: 80rpx;
										border: 2rpx solid #FF6900;
									}
								}
							}
						}
					}

				}

				.noOrder {
					height: 1000rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.notitle {
						margin-top: 50rpx;
					}
				}
			}
		}
	}

	.Popupinfo {
		width: 650rpx;
		height: 650rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 999;
		margin-bottom: -80rpx;

		.PopupCon {
			width: 92%;
			margin: 0 auto;

			.topCon {
				height: 100rpx;
				line-height: 100rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.content {
				height: 420rpx;
				overflow-y: auto;
				padding: 0 20rpx;

				// display: flex;
				// flex-direction: column;
				// align-items: center;
				// justify-content: center;
				.company {
					margin-bottom: 30rpx;
				}

				.title {
					font-size: 32rpx;
					color: #000;
					font-weight: 500;
				}

				.conten {
					height: 60rpx;
					line-height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #c1c1c1;
					font-family: PingFang SC, PingFang SC;
					;
				}
			}

			.bottombtn {
				display: flex;
				margin: 30rpx 0;

			}
		}
	}
</style>