<template>
  <div>
    <view class="intro">本项目已包含uni ui组件，无需import和注册，可直接使用。</view>
    <!-- 小程序中使用web-view来加载URL -->
    <web-view v-if="isApp" :src="url" class="webview"></web-view>
  </div>
</template>

<script>
export default {
  data() {
    return {
      url: 'https://img.cdn.aliyun.dcloud.net.cn/guide/uniapp/%E7%AC%AC1%E8%AE%B2%EF%BC%88uni-app%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8D%EF%BC%89-%20DCloud%E5%AE%98%E6%96%B9%E8%A7%86%E9%A2%91%E6%95%99%E7%A8%8B@20181126-lite.m4v',
      isApp: false // 是否为App环境
    };
  },
  mounted() {
    // 检测当前环境
    if (process.env.UNI_PLATFORM === 'app-plus') {
      this.isApp = true;
      let wv;
      // #ifdef APP-PLUS
      wv = plus.webview.create("", "webview", {
        plusrequire: "none", // 禁止远程网页使用plus的API
        top: uni.getSystemInfoSync().statusBarHeight + 293, // 放置在titleNView下方
        height: 300
      });
      wv.loadURL(this.url);
      var currentWebview = this.$parent.$scope.$getAppWebview(); // 获取当前App的webview
      currentWebview.append(wv); // 必须添加到当前页面的webview里
      setTimeout(function() {
        console.log(wv.getStyle());
      }, 1000);
      // #endif
    }
  },
  beforeDestroy() {
    // 如果是APP环境，关闭webview
    if (this.isApp) {
      wv.close();
    }
  }
};
</script>

<style>
.intro {
  color: #007AFF;
}
.webview {
  height: 300px;
  width: 100%;
}
</style>
