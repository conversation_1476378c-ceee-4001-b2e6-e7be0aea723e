import Vue from "vue"
import Vuex from "vuex"
import http from "../common/http/http.js"
import createPersistedstate from 'vuex-persistedstate'
Vue.use(Vuex)
import init from './modules/init.js'
import user from './modules/user.js'
import link from './modules/link.js'
import tabbar from "./modules/tabbar.js"


const store = new Vuex.Store({
	modules: {
		init,
		user,
		link,
		tabbar
	},
	plugins: [
		createPersistedstate({
			key: 'zhiling-vuex-store', // 存数据的key名   自定义的  要有语义化
			paths: ['init','user','link'], // 要把那些模块加入缓存
			storage: {
				getItem: (key) => uni.getStorageSync(key),
				setItem: (key, value) => uni.setStorageSync(key, value),
				removeItem: (key) => uni.removeStorageSync(key)
			}
		})
	]
})

export default store