<template>
	<view class="page">
		<view class="header" :style="{ 'lineHeight': navBarHeight + 'rpx' }">
			<newNavBar title="确认订单" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		</view>
		<view class="HomeView">
			<view class="topPlaceName">
				<view class="title">
					<text>{{ placeName }}</text>
				</view>
				<view class="distance">
					<image src="/static/images/navgo.png" mode="aspectFill"
						style="width: 28rpx;height: 28rpx;margin-right: 4rpx;"></image>
					<view class="km">
						<text>{{ jlInfo }}</text>
					</view>
				</view>
			</view>

			<view class="Unsubscribe">
				<view class="mxList" v-for="(item, index) in orderinfo.mxList" :key="index">
					<view class="mxDetail">
						<view class="left">
							<image :src="item.coverImageUrl" mode="aspectFill" style="width: 120rpx;height: 120rpx;">
							</image>
						</view>
						<view class="rightDetail">
							<view class="topname">
								<text>{{ item.coachName }}</text>
								<!-- <image src="https://file.36sm.cn/jaxinfo/2025/02/28/64e72497a75f4bac91fb8ea5a22748a4.png" mode="aspectFill" @click="delEngineer(index)" v-if="orderinfo.mxList.length > 1"
									style="width: 30rpx;height: 30rpx;">
								</image> -->
							</view>
							<view class="detailTime">
								<text style="margin-right: 10rpx;">{{ formatDate(item.dayInfo) }}</text>
								<text>{{ formatTime(item.startInfo) }}</text> <text style="margin: 0 5rpx;">至</text>
								<text>{{ formatTime(item.endInfo) }}</text>
							</view>
							<view class="bottom">
								<view class="leftPro">
									<text>{{ item.projectName }}</text>
									<text>{{ item.xmSj }}分钟</text>
								</view>
								<view class="rightPrice">
									￥<text>{{ item.xj }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="Chargeback">
				<view class="title">
					<text>退单说明</text>
				</view>
				<view class="chargeContent">
					<view class="list-item">
						<text>{{ orderinfo.tdDescribe[0] }}</text>
					</view>
					<view class="list-item">
						<text>{{ orderinfo.tdDescribe[1] }}</text>
					</view>
				</view>
			</view>
			<!-- 客户备注 -->
			<view class="remark">
				<view class="title">
					<text>客户备注</text>
				</view>
				<!-- <view class="input">
					<input :placeholder="orderinfo.notesPrompt" v-model="memberNotes" class="input-field" border="none"  style="font-size: 22rpx;"/>
				</view> -->
				<view class="input">
					<textarea :placeholder="orderinfo.notesPrompt" v-model="memberNotes" class="input-field"
						style="font-size: 22rpx; border: none;" auto-height></textarea>
				</view>

			</view>
			<!-- 支付方式 -->
			<view class="payment">
				<view class="title">
					<text>支付方式</text>
				</view>
				<view class="bottomcheck">
					<view class="leftwei">
						<image src="/static/images/weixin.png" mode="aspectFill"
							style="width: 40rpx;height: 40rpx;margin-right: 10rpx;"></image>
						<text>微信支付</text>
					</view>
					<view class="checkbox">
						<u-checkbox-group v-model="isSelect" shape="circle" activeColor="#FF6900 "
							@change="checkboxChange">
							<u-checkbox label="." :name="1" size="24rpx" labelSize="24rpx"
								labelColor="#fff"></u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</view>
			<view style="height: 188rpx;">
			</view>
		</view>
		<view class="bottomCon">
			<view class="topTab">
				<view class="bottom_left">
					<view class="totalxjPrice">
						￥<text>{{ orderinfo.totalInfo }}</text>
					</view>
					<view class="totalyj">
						<text>￥{{ totalXJprice }}</text>
						<text class="line"></text>
					</view>
					<view class="Countdown" v-if="orderinfo.dqSecondDate">
						<text>支付倒计时:</text>
						<view class="Timedown">
							<u-count-down :time="orderinfo.dqSecondDate * 1000" format="mm:ss" @change="TimeonChange"
								ref="countdown" autoStart>
								<view class="timeview">
									<view class="minutes">
										<text>{{ timeData.minutes > 10 ? timeData.minutes : '0' + timeData.minutes }}</text>
									</view>
									<text class="time__doc">:</text>
									<view class="second">
										<text>{{ timeData.seconds > 10 ? timeData.seconds : '0' + timeData.seconds }}</text>
									</view>
								</view>
							</u-count-down>
						</view>
					</view>
				</view>
				<view class="bottom_right">
					<u-button color="linear-gradient(to right, #FF6900 , #FF6900)" text="去支付" @tap="toPayorder"
						:custom-style="{ borderRadius: '94rpx', height: '70rpx', width: '200rpx' }"
						:loading="payloading" loadingText="支付中" class="custom-button">
					</u-button>
				</view>
			</view>
			<view style="height: 88rpx;">
			</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import api from '@/common/request/index.js'
	import newNavBar from '../../components/newNavBar/newNavBar.vue'
	export default {
		data() {
			return {
				visible: false,
				// 订单详情
				orderinfo: {
					dqSecondDate: '',
					mxList: [],
					notesPrompt: '',
					orderId: '',
					tdDescribe: [],
					totalInfo: ''
				},
				// 店铺名称
				placeName: '',
				// 订单备注 客户备注
				memberNotes: '',
				isSelect: [1],
				payloading: false,
				timeData: {},
				// 标记倒计时是否开始
				isCountdownStarted: false,
				// 订单支付成功的状态
				isSuccessful: false,
				orderList: {},
				navBarHeight: 0,
				// 位置距离
				jlInfo: ''
			}
		},
		components: {
			newNavBar
		},
		props: {},
		components: {},
		computed: {
			totalXJprice() {
				return this.orderinfo.mxList?.reduce((total, item) => {
					const price = parseFloat(item.xj) || 0;
					return total + price;
				}, 0).toFixed(1);
			}
		},
		mounted() {

		},
		onHide() {
			console.log('销毁计时器')
			this.orderinfo.dqSecondDate = ''

			// 这里后面需要添加一个标识  用来区分 支付成功后 离开这个页面 订单不取消 否则就取消这个订单
			// this.cancelOrder()

		},
		onUnload() {
			console.log('onUnload')
			// 这里后面需要添加一个标识  用来区分 支付成功后 离开这个页面 订单不取消 否则就取消这个订单
			if (!this.isSuccessful) {
				this.cancelOrder()
			}
		},
		onLoad(params) {
			console.log(params, '路由参数')

			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});

			if (params.orderinfo) {
				try {
					this.orderinfo = JSON.parse(decodeURIComponent(params.orderinfo)); // 使用 decodeURIComponent 解码
					console.log(this.orderinfo, '订单数据222');
					console.log(this.orderinfo.mxList, 'mxListmxList')
					this.getStausOList()

				} catch (e) {
					console.error("解析订单数据失败:", e);
					this.orderinfo = {};
				}
			} else {
				console.log("orderinfo 参数为空");
				this.orderinfo = {};
			}
			if (params.placeName) {
				this.placeName = params.placeName
				this.jlInfo = params.jlInfo
			}
		},
		onHide() {
			this.orderinfo.dqSecondDate = ''
		},
		methods: {
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			// 格式化 dayInfo 获取几月几号
			formatDate(dateString) {
				// 将日期字符串中的空格替换为 'T'，保证兼容性
				const formattedDateString = dateString.replace(" ", "T");
				const date = new Date(formattedDateString);
				const month = date.getMonth() + 1; // 月份从0开始
				const day = date.getDate();
				return `${month}月${day}号`;
			},
			// 格式化结束时间
			formatTime(dateString) {
				// 同样处理时间字符串
				const formattedDateString = dateString.replace(" ", "T");
				const date = new Date(formattedDateString);
				const hour = date.getHours();
				const minute = date.getMinutes();
				return `${hour}:${minute < 10 ? '0' + minute : minute}`;
			},
			inputchange(e) {},
			checkboxChange(e) {
				console.log(e, 'eee')
			},
			toPayorder() {
				// if (this.memberNotes == '') {
				// 	uni.showToast({
				// 		title: '客户备注不能为空',
				// 		icon: "none"
				// 	});
				// 	return
				// }
				if (this.isSelect.length == 0) return uni.$u.toast('请选择支付方式')
				if (this.payloading) {
					return
				}
				console.log(this.orderinfo, '提交的参数')
				this.payloading = true
				this.orderinfo.memberNotes = this.memberNotes
				api({
					url: `/car/lj/zf/order/info?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: this.orderinfo
				}).then(res => {
					if (res.code === 200) {
						//  payAmountTrade 为 0 则不需要拉起支付  直接跳转到支付成功的订单详情页面
						if (res.data.data.payAmountTrade == 0) {
							uni.navigateTo({
								url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(this.orderList))}`
								// url: `/pageg/oder_detail/index?orderDetail=${JSON.stringify(this.orderList)}`
							})

							return
						}
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.data.data.timeStamp,
							nonceStr: res.data.data.nonceStr,
							package: res.data.data.packageValue,
							signType: res.data.data.signType,
							paySign: res.data.data.paySign,
							success: (res) => {
								api({
									url: "/car/weixin/order/status/query",
									header: {
										"jarepair-platform": uni.getStorageSync("token")
									},
									method: 'POST',
									data: {
										outTradeNo: this.orderinfo.orderId
									}
								}).then((res) => {
									console.log(res, '支付成功的信息')
									if (res.code == 200) {
										// 支付成功  进入到订单页面
										this.isSuccessful = true
										uni.showToast({
											title: '支付成功',
											icon: "none"
										});
									} else {
										this.isSuccessful = false
										uni.showToast({
											title: res.msg,
											icon: "none"
										});
									}
									// setTimeout(() => {
									this.orderinfo.dqSecondDate = ''
									uni.navigateTo({
										url: `/pageg/oder_detail/index?orderDetail=${encodeURIComponent(JSON.stringify(this.orderList))}`
										// url: `/pageg/oder_detail/index?orderDetail=${JSON.stringify(this.orderList)}`
									})
									// }, 300)
								})
							},
							fail: (res) => {
								console.log(res)
								if (res.errMsg.includes('fail cancel')) {
									uni.showToast({
										title: '取消支付',
										icon: "none"
									});
								} else {
									uni.showToast({
										title: res.errMsg,
										icon: "none"
									});
								}
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.payloading = false
					}, 500)
				}).catch(err => {
					uni.showToast({
						title: err.errMsg || '网络超时, 请重新加载',
						icon: "none"
					});
					setTimeout(() => {
						this.payloading = false
					}, 500)
				});

			},
			// 获取订单列表
			getStausOList() {
				api({
					url: `goods/get/order/list?location=zh&orderStatus=100&page=1&size=1`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
					if (res.code == 200) {
						this.orderList = res.data.data.list[0]
						console.log(this.orderList, '当前最新的订单详情')
					}
				}).catch(err => {
					uni.showToast({
						title: '网络超时,请重试',
						icon: 'none'
					})
				})
			},
			delEngineer(index) {
				uni.showModal({
					title: '温馨提示',
					content: '是否确认取消该推拿师?',
					success: (res) => {
						if (res.confirm) {
							// 取消当前推拿师
							uni.showLoading({
								title: '加载中'
							});
							setTimeout(() => {
								this.orderinfo.mxList.splice(index, 1);
								uni.hideLoading()
							}, 800)
						} else if (res.cancel) {
							console.log('用户点击取消');
							// 如果点击取消，不执行后续操作
						}
					}
				});
			},
			cancelOrder() {
				// 取消订单
				const params = {
					orderId: this.orderinfo.orderId
				}
				console.log(params, '取消订单取消参数')
				api({
					url: `car/qx/order/info?location=zh`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					if (res.code === 200) {
						console.log(res, '接口参数')
						// 订单取消成功
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '网络超时, 请重新加载',
						icon: "none"
					});
				});
			},
			TimeonChange(time) {
				this.timeData = time
				// 倒计时为0
				// 如果倒计时已经开始过
				if (this.isCountdownStarted) {
					// 倒计时为0
					if (this.timeData.minutes == 0 && this.timeData.seconds == 0) {
						// 支付超时了
						uni.showModal({
							title: '温馨提示',
							content: '支付超时了哟,快快重新下单吧',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									console.log('用户点击确定');
									uni.switchTab({
										url: '/pages/home/<USER>'
									});
								}
							}
						});
						this.cancelOrder()
					}
				} else {
					// 如果倒计时未开始，则标记倒计时已开始
					this.isCountdownStarted = true;
				}
			},
		}
	}
</script>
<style lang="scss" scope>
	.page {
		// background-color: #fff;
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;

		.HomeView {
			width: 92%;
			margin: 0 auto;

			.topPlaceName {
				height: 80rpx;
				display: flex;
				align-items: center;

				.title {
					margin-right: 15rpx;

					text {
						font-weight: 600;
						font-family: PingFang SC, PingFang SC;
						font-size: 32rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}
				}

				.distance {
					display: flex;
					align-items: center;

					.km {
						// font-family: 'alimm-font' !important;
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 24rpx;
						color: #999999;
						font-style: normal;
						text-transform: none;
						margin-left: 5rpx;
					}
				}
			}

			.Chargeback,
			.remark,
			.payment {
				border-radius: 20rpx;
				background-color: #fff;
				margin-bottom: 20rpx;
			}

			.Unsubscribe {
				.mxList {
					border-radius: 20rpx;
					background-color: #fff;
					margin-bottom: 20rpx;

					.mxDetail {
						padding: 30rpx 20rpx;
						display: flex;
						align-items: center;

						.left {
							margin-right: 30rpx;

							image {
								border-radius: 50%;
							}
						}

						.rightDetail {
							height: 120rpx;
							flex: 1;

							.topname {
								display: flex;
								align-items: center;
								justify-content: space-between;

								text {
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									font-size: 28rpx;
									color: #000000;
									font-style: normal;
									text-transform: none;
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}
							}

							.detailTime {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 24rpx;
								color: #666;
								font-style: normal;
								text-transform: none;
								margin: 10rpx 0;
							}

							.bottom {
								display: flex;
								align-items: center;
								justify-content: space-between;

								.leftPro {
									font-family: PingFang SC, PingFang SC;
									font-weight: 500;
									font-size: 24rpx;
									color: #333;
									font-style: normal;
									text-transform: none;
									width: 300rpx;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;

								}

								.rightPrice {
									font-family: PingFang SC, PingFang SC;
									font-weight: 600;
									font-size: 28rpx;
									color: #FF6900;
									font-style: normal;
									text-transform: none;
								}
							}
						}
					}
				}
			}

			.Chargeback {
				padding: 30rpx 20rpx;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 28rpx;
					color: #000;
					font-style: normal;
					text-transform: none;
					margin-bottom: 30rpx;
				}

				.chargeContent {
					.list-item {
						display: flex;
						align-items: center;
						margin-bottom: 30rpx;
						/* 控制列表项之间的间距 */
					}

					.list-item::before {
						content: "•";
						/* 使用圆点作为列表项标记 */
						font-size: 28rpx;
						color: #ccc;
						margin-right: 10rpx;
						/* 圆点和文本之间的间距 */
					}

					.list-item text {
						font-size: 24rpx;
						color: #000;
					}
				}
			}

			.remark {
				padding: 30rpx 20rpx;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 28rpx;
					color: #000;
					font-style: normal;
					text-transform: none;
					margin-bottom: 30rpx;
				}

				.input {
					.input-field::placeholder {
						white-space: normal;
						/* 设置换行 */
						word-wrap: break-word;
						/* 强制换行 */
						overflow-wrap: break-word;
						font-size: 20rpx;
					}
				}

			}

			.payment {
				padding: 30rpx 20rpx;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 28rpx;
					color: #000;
					font-style: normal;
					text-transform: none;
					margin-bottom: 30rpx;
				}

				.bottomcheck {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.leftwei {
						display: flex;
						align-items: center;
					}

					.checkbox {}
				}
			}
		}

		.bottomCon {
			position: fixed;
			width: 100%;
			height: 188rpx;
			background: #FFFFFF;
			bottom: 0;

			.topTab {
				width: 98%;
				height: 100rpx;
				line-height: 100rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.bottom_left {
					margin-left: 20rpx;
					display: flex;
					align-items: center;

					.totalxjPrice {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 36rpx;
						color: #FF6900;
						line-height: 40rpx;
						font-style: normal;
						text-transform: none;
						margin-right: 15rpx;
					}

					.totalyj {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 22rpx;
						color: #999999;
						font-style: normal;
						text-transform: none;
						position: relative;
						width: 62rpx;

						.line {
							position: absolute;
							top: 50%;
							left: 0;
							width: 62rpx;
							height: 0rpx;
							border: 1rpx solid #d6d6d6;
							transform: translate(0, -50%);
						}
					}

					.Countdown {
						display: flex;
						align-items: center;
						font-weight: 600;
						font-size: 22rpx;
						margin-left: 28rpx;
						color: #FF6900;

						.Timedown {
							margin-left: 12rpx;

							.timeview {
								display: flex;

								.minutes,
								.second {
									width: 32rpx;
									height: 32rpx;
									line-height: 32rpx;
									border-radius: 4rpx 4rpx 4rpx 4rpx;
									font-family: PingFang SC, PingFang SC;
									text-align: center;
									font-style: normal;
									text-transform: none;
								}

								.time__doc {
									// width: 10rpx;
									height: 32rpx;
									line-height: 32rpx;
									margin: 0 2rpx;
									font-family: PingFang SC, PingFang SC;
									// font-weight: 600;
									color: #FF6900;
									text-align: center;
									font-style: normal;
									text-transform: none;
								}
							}
						}
					}
				}
			}
		}
	}
</style>