<template>
	<view class="page">
		<scroll-view class="scroll-container" :scroll-into-view="currentSection" scroll-y="true" style="height: 100vh;scroll-behavior: smooth;"
			@scroll="onScroll" v-if="visible">
			<view class="headerImg" v-if="showTab == true">
				<image :src="DetailData.coverImageUrl" mode=""></image>
				<view class="header" :style="{ 'padding-top': navBarHeight + 9 + 'px' }">
					<view class="navbar">
						<view class="" @tap="goback()">
							<image src="https://file.36sm.cn/xtjyjt/images/common/whiteback.png" mode="" style="width: 50rpx;height: 50rpx;">
							</image>
						</view>
						<view class="title">
						</view>
						<view class="title"> </view>
					</view>
				</view>
			</view>
			<view class="headerTwo" v-if="showTab == false" :style="{ 'padding-top': navBarHeight + 9 + 'px' }">
				<view class="navbar">
					<view class="" @tap="goback()">
						<image src="https://file.36sm.cn/xtjyjt/images/common/blackback.png" mode="" style="width: 50rpx;height: 50rpx;">
						</image>
					</view>
					<view class="title">
						道具详情
					</view>
					<view class="title1" style="width: 80rpx;"> </view>
				</view>
				<view class="NavTab">
					<view class="itemdetail" v-for="item in navtablist" :key="item.tab">
						<view class="tabitem" @tap="scrollToSection(item.tab)" >{{item.name}}</view>
						<view class="isActive" v-if="currentSection == item.tab">
						</view>
					</view>
				</view>
			</view>

			<view class="" style="margin-bottom: 100rpx;" v-if="visible">
				<view class="headerTitleBox">
					<view class="headerTitle">
						<view class="topName" id="section1">
							<view class="title">
								{{DetailData.propName}}
							</view>
							<view class="tag">
								<view class="imageBox">
									<image src="https://file.36sm.cn/xtjyjt/images/lb.png" mode=""></image>
								</view>
								{{DetailData.typeInfoList[0]}}
							</view>
						</view>
						
						<view class="fleximg">
							<view class="imagBox">
								<image :src="DetailData.coverImageUrl" mode=""
									@tap="preview(DetailData.coverImageUrl)"></image>
							</view>
						</view>
						
						<view class="centertitle">
							<view class="title" v-for="(item, index) in DetailData.typeInfoList" :key="index">
								<text>{{item}}</text>
							</view>
						</view>
						
						<view class="anlicontent">
							<view class="content">
								<text class="title">道具库存</text>
								<text>{{ DetailData.stock }}</text>
							</view>
							
						</view>
						
						<view class="synopsis">
							{{DetailData.introduce}}
						</view>
						
						<view class="synopsis">
							{{DetailData.propRemarks}}
						</view>
						<view class="tagBox">
							<view class="tabItem" v-for="(item,index) in DetailData.labelInfoList" :key="index">
								<image src="https://file.36sm.cn/xtjyjt/images/bq.png" mode=""></image>
								<text>{{item}}</text>
							</view>
						</view>
						
						<!-- <view class="contact" v-if="DetailData.contactsInfoList.length > 0">
							<view class="contactitem" v-for="(item, index) in DetailData.contactsInfoList" :key="index">
								<view class="name">
									联系人: {{item.name}}
								</view>
								<view class="phone">
									电话: {{item.phone}}
								</view>
								<view class="role">
									职务/角色: {{item.role}}
								</view>
							</view>
						</view> -->
					</view>
				</view>
			
				<view class="footer">
					<image src="https://file.36sm.cn/xtjyjt/images/tx.png" mode=""></image>
				</view>
				<view style="height: 170rpx;">
				</view>
			</view>
		</scroll-view>
        <cc-gifLoading  v-if="!visible"
				gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
		<view class="btnbottom" v-if="visible">
			<!-- <view class="btnleft">
				<view class="shouc">
					<image src="https://file.36sm.cn/xtjyjt/images/sc.png" mode=""></image>
					<text>收藏</text>
					
				</view>
				<view class="biaoz">
					<image src="https://file.36sm.cn/xtjyjt/images/bz.png" mode=""></image>
					<text>标注</text>
				</view>
			</view> -->
			<view class="btnright">
				<view class="buton1" @tap="handelTal">
					<image src="https://file.36sm.cn/mttttxxs/2025/01/15/faccc830855340e6bf6687dbd984bb10.png" mode=""></image>
					电话联系
				</view>
				<view class="buton2" @tap="Share()">
					<image src="../../static/zf.png" mode=""></image>
					分享道具
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="bottom">
			<view class="popupBox">
				<view class="lianxiren">
					<view class="title">
						选择联系人
					</view>
					<view class="lianxiren_item" v-for="(item,index) in DetailData.contactsInfoList" :key="index"
						@tap="phoneTal(item.phone)">
						{{item.name}}：{{item.phone}}（{{item.role}}）
					</view>
				</view>
				<u-button :plain="true" text="取消" @tap="handelclose"></u-button>
			</view>
		</uni-popup>
		
	<uni-popup ref="infoPopup" type="center" :is-mask-click="false">
		<view class="InfoPopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view class="title">
							<text></text>
						</view>
						<view class="title">
							<text>二维码详情</text>
						</view>
						<view class="Close" @click="closeinfoPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
						</view>
		   			</view>
					<view class="content">
						<image :src="QRimg" style="width: 450rpx;height: 450rpx;" v-if="QRimg"></image>
						<image src="https://file.36sm.cn/mttttxxs/2025/01/15/99a8a9a165424bbc9bedad16d10f2da8.gif" style="width: 450rpx;height: 450rpx;" v-else></image>
					</view>
					<view class="btn" v-if="QRimg" style="margin-top: 150rpx;">
						<button style="color:#fff;background-color:#2A3A88FF;font-size: 30rpx;" @click="downimg" :loading="downloading">下载</button>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import api from '@/common/request/index.js'
	import { getCurrentInstance } from "vue";
	export default {
		data() {
			return {
				navBarHeight: 0,
				propId: '',
				DetailData: [],
				contentRichList: [],
				scrollTop: 0, // 当前的滚动位置
				showTab: true, // 是否显示Tab页
				lastScrollTime: 0, // 上一次滚动的时间  
				currentSection: '', // 当前需要滚动到的section的id
				visible: false,
				image1: '',
				image2: '',
				imageUrl: '',
				navtablist: [
					{
						name: '足疗信息',
						tab: 'section1'
					}
				],
				timeoutId: null,
				danmuList: [],
				lastKnownSection: null,
				QRimg: '',
				downloading: false
			}
		},
		onLoad(options) {
			this.propId = options.propId
			console.log(this.propId, 'this.propId');
			// 获取系统信息
			uni.getSystemInfo({
				success: (res) => {
					// 获取状态栏高度 
					this.navBarHeight = res.statusBarHeight;
					console.log('状态栏高度:', this.navBarHeight);
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
				}
			});



			//调用数据
			this.getDetailData()
		},
		onShareAppMessage() {
			return {
				title: '道具详情',
				path: `/pageg/item_resources/itemdetails?propId=${this.propId}`,
				content: '道具详情'
			}
		},
		methods: {
			downimg() {
				if(this.downloading) {
					return
				}
				this.downloading = true;
				this.downSaveImage(this.QRimg)
			},
			downSaveImage(imgurl) {
				let that = this;
					uni.showLoading({
					    title: '图片下载中',
					});
				  uni.getSetting({
				    success(res) {
				      if (res.authSetting['scope.writePhotosAlbum']) {
				        // 已授权，直接保存图片
				        uni.downloadFile({
				          url: imgurl,
				          success: (res) => {
				            if (res.statusCode === 200) {
				              uni.saveImageToPhotosAlbum({
				                filePath: res.tempFilePath,
				                success: function () {
				                  uni.showToast({
				                    title: '保存成功',
				                    duration: 2000,
				                  })
								  uni.hideLoading();
								  that.downloading = false
				                },
				                fail: function () {
				                  uni.showToast({
				                    title: '保存失败，请稍后重试',
				                    icon: 'none',
				                  })
								  uni.hideLoading();
								  that.downloading = false
				                },
				              })
				            }
				          },
				        })
				      } else if (res.authSetting['scope.writePhotosAlbum'] === false) {
				        // 用户已拒绝授权，提示用户授权
				        uni.showModal({
				          title: '提示',
				          content: '您未授权保存图片到相册，是否前往设置页面进行授权？',
				          success: function (res) {
				            if (res.confirm) {
				              uni.openSetting({
				                success: function (res) {
				                  if (res.authSetting['scope.writePhotosAlbum']) {
				                    uni.downloadFile({
				                      url: imgurl,
				                      success: (res) => {
				                        if (res.statusCode === 200) {
				                          uni.saveImageToPhotosAlbum({
				                            filePath: res.tempFilePath,
				                            success: function () {
				                              uni.showToast({
				                                title: '保存成功',
				                                duration: 2000,
				                              })
											  uni.hideLoading();
											  that.downloading = false
				                            },
				                            fail: function () {
				                              uni.showToast({
				                                title: '保存失败，请稍后重试',
				                                icon: 'none',
				                              })
											  uni.hideLoading();
											  that.downloading = false
				                            },
				                          })
				                        }
				                      },
				                    })
				                  }
				                },
				              })
				            } else if (res.cancel) {
				              uni.showToast({
				                title: '您取消了授权',
				                icon: 'none',
				                duration: 2000,
				              })
							  uni.hideLoading();
							  that.downloading = false
				            }
				          },
				        })
				      } else {
				        // 用户第一次调用，调用授权接口
				        uni.authorize({
				          scope: 'scope.writePhotosAlbum',
				          success() {
				            uni.downloadFile({
				              url: imgurl,
				              success: (res) => {
				                if (res.statusCode === 200) {
				                  uni.saveImageToPhotosAlbum({
				                    filePath: res.tempFilePath,
				                    success: function () {
				                      uni.showToast({
				                        title: '保存成功',
				                        duration: 2000,
				                      })
									  uni.hideLoading();
									  that.downloading = false
				                    },
				                    fail: function () {
				                      uni.showToast({
				                        title: '保存失败，请稍后重试',
				                        icon: 'none',
				                      })
									  uni.hideLoading();
									  that.downloading = false
				                    },
				                  })
				                }
				              },
				            })
				          },
				          fail() {
				            uni.showToast({
				              title: '授权失败，请稍后重试',
				              icon: 'none',
				              duration: 2000,
				            })
							uni.hideLoading();
							that.downloading = false
				          },
				        })
				      }
				    },
				  })
			},
			moveHandle() {},
			Share() {
				this.$refs.infoPopup.open()
				this.getQRimg()
			},
			closeinfoPopup() {
				this.$refs.infoPopup.close();
				setTimeout(() => {
					this.QRimg = ''
				}, 500)
			},
			getQRimg() {
				const params = {
					path: `/pageg/item_resources/itemdetails?propId=${this.propId}`,
				}
				api({
					url: `dt/details/path/info/code`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: params
				}).then(res => {
					if (res.code == 200) {
						console.log(res, '二维码资源');
						this.QRimg = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			goback() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				})
			},
			videodetail(url) {
				uni.navigateTo({
					url: `/pages/branded-space/videodetail?videourl=${url}`
			    })
			},
			scrollToSection(section) {
				this.currentSection = section; // 改变currentSection来控制滚动
				console.log(this.currentSection, '当前currentSection')
			},

			preview(imageSrc) {
				console.log(imageSrc, '图片路径');
				
				uni.previewImage({
				    urls: [imageSrc],
					current: imageSrc
				});
			},
			onScroll(e) {
				return
				let that = this
				if (that.timeoutId) {
					clearTimeout(that.timeoutId);
				}
				
				that.timeoutId = setTimeout(() => {
					const scrollTop = e.detail.scrollTop;
					that.scrollTop = scrollTop;
									
					// 判断是否超过300px并切换状态
					if (scrollTop > 250) {
						if (that.showTab) {
							that.showTab = false; // 只在状态改变时切换
						}
					} else {
						if (!that.showTab) {
							that.showTab = true; // 只在状态改变时切换
						}
					}
					// this.updateCurrentSection()
				}, 100)
			},
			
			// 获取滚动 定位的元素  
			updateCurrentSection() {
				// getCurrentInstance
			    let currentSection = null;
			          const sections = this.navtablist;
			          const query = wx.createSelectorQuery(); // 创建选择器查询
			          const windowHeight = wx.getWindowInfo().windowHeight; // 获取窗口高度
			     
			      sections.forEach((item, index) => {
			        query.select(`#${item.tab}`).boundingClientRect((rect) => {
						// console.log(rect, 'rect')
			          if (rect) {
			            // 判断当前section是否在可视区域
			            if (rect.top >= 0 && rect.top <= windowHeight) {
			              this.currentSection = item.tab; // 当前section的tab
			            }
			          }
			        }).exec();
			      });
			    
			      // 在所有查询完成后更新currentSection
			      if (currentSection !== this.currentSection) {
			        this.currentSection = currentSection;
			      }
			    },

			//获取详情数据  
			getDetailData() {
				api({
					url: `tx/prop/id/info/${this.propId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
				}).then(res => {
					if (res.code == 200) {
						console.log(res);
						this.DetailData = res.data.data
						// this.contentRichList = res.data.data.contentRichList
						setTimeout(() => {
							this.$data.visible = true
						}, 1200)
						
						// 定义一个函数用于判断并添加导航项
						// const addNavTabIfNeeded = (condition, name, tab) => {
						//   if (condition.length > 0) {
						//     this.navtablist.push({
						//       name: name,
						//       tab: tab
						//     });
						//   }
						// };
						// addNavTabIfNeeded(this.DetailData.productInfoList, '关联产品', 'section4');
						// addNavTabIfNeeded(this.DetailData.annexInfoList, '关联附件', 'section5');
						
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			// 关闭弹窗
			handelclose() {
				this.$refs.popup.close()
			},
			// 拨打电话
			handelTal() {
				this.$refs.popup.open('bottom')
			},

			//跳转到产品页面  
			navToproduct(id) {
				console.log(id);
				uni.navigateTo({
					url: `/pageu/productDetails/productDetails?productId=${id}`
				})
			},

			// 跳转到pdf页面
			navTopdfpage(url) {
				console.log('文件资源路径', url)
				this.showPDf(url)
				return
				uni.navigateTo({
					url: `/pageg/webview/filePreview?url=${url}`
				})
				return

				uni.navigateTo({
					url: '/pageu/pdfFileage/pdfFileage'
				})
			},
			showPDf(PdfUrl) {
				uni.showLoading({
					title: '正在打开...'
				});
				// this.schedule = 0; //载入进度为0
				const downloadTask = uni.downloadFile({
					url: PdfUrl, // 图片或者文件地址
					success: function(res) {
						console.log('下载的res', res);
						var filePath = res.tempFilePath; // 临时文件存储路径
						//  文件打开文件预览
						uni.openDocument({
							filePath: encodeURI(filePath),
							success: function(res) {
								uni.hideLoading();
								console.log('打开文档成功');
							},
							fail: function(err) {
								uni.hideLoading();
								uni.showToast({
									title: '打开失败',
									duration: 1500,
									icon: 'none'
								});
								console.log('打开失败');
							}
						});
					},
					fail: function(err) {
						console.log('下载失败原因', err);
						uni.hideLoading();
						uni.showModal({
							title: '您需要授权相册权限',
							success(res) {
								if (res.confirm) {
									uni.openSetting({
										success(res) {},
										fail(res) {
											console.log(res);
										}
									});
								}
							}
						});
					}
				});
				downloadTask.onProgressUpdate(res => {
					// console.log('下载进度' + res.progress);
					// this.schedule = res.progress
				});
			},
			// 拨打电话
			phoneTal(phoneNum) {
				console.log(phoneNum);
				uni.makePhoneCall({
						phoneNumber: phoneNum
					})
					.catch((e) => {
						// console.log (e)
					});
				//  <a href="tel:10086">h5平台下拨打</a>
			}



		}
	}
</script>

<style scoped lang="scss">
	// .flex {
	// 	display: flex;
	// 	justify-content: space-between;
	// 	// align-items: center;
	// }

	.page {
		width: 100%;
		height: 100%;
		// min-height: 100vh;
		background-color: #f5f5f5;



		.headerImg {
			width: 100%;
			height: 420rpx;
			position: relative;


			image {
				width: 100%;
				height: 100%;

			}

			.header {
				width: 100%;
				// height: 77rpx;
				// background-color: #f8f8f8;
				position: fixed;
				z-index: 999;
				// position: absolute;
				top: 0rpx;

				.navbar {
					height: 48rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 10rpx;
					box-sizing: border-box;

					image {
						margin-left: 35rpx;
					}
				}
			}
		}

		.headerTwo {
			width: 100%;
			height: 130rpx;
			// background-color: #f8f8f8;
			position: fixed;
			z-index: 9999;
			top: 0rpx;
			background-color: #fff;

			.navbar {
				height: 50rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 10rpx;
				box-sizing: border-box;

				image {
					margin-left: 35rpx;
				}

				.title {
					width: 470rpx;
					font-size: 34rpx;
					font-weight: 800;

				}
			}

			.NavTab {
				height: 80rpx;
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.itemdetail{
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					flex: 1;
					position: relative;
					.tabitem{
					}
					.isActive{
						position: absolute;
						  bottom: 0;
						  left: 50%;
						  width: 80rpx;
						  height: 10rpx;
						  border-radius: 20rpx;
						  background-color: #858585;
						  
						  /* 使用 transform 来确保居中 */
						  transform: translateX(-50%);
					}
				}
			}

		}

		.headerTitle {
			background-color: #fff;
			margin: 0 20rpx;
			background-color: #fff;
			border-radius: 10rpx;
			padding:5rpx 20rpx;
			position: relative;
			top: -20rpx;
			left: 0rpx;
			right: 20rpx;
			.topName{
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
					.title {
						font-size: 40rpx;
						font-weight: bold;
						color: #636675;
						letter-spacing: 1rpx;
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
					
				.tag {
					padding: 2rpx 20rpx;
					background-color: #485159;
					border-radius: 8rpx 0rpx 0rpx 8rpx;
					font-size: 24rpx;
					color: #fff;
					font-weight: 500;
					display: flex;
					align-items: center;
					justify-content: center;
					box-sizing: border-box;
				
					.imageBox {
						width: 34rpx;
						height: 100%;
						padding-top: 8rpx;
						margin-right: 8rpx;
				
						image {
							width: 34rpx;
							height: 34rpx;
						}
					}
				}
			}
			.fleximg{
				// margin: 15rpx 0 0 0;
				.imagBox{
					height: 350rpx;
					width: 100%;
					image {
						width: 100%;
						height: 100%;
						border-radius: 20rpx;
					}
				}
			}
			
			.centertitle{
				// background-color: skyblue;
				margin: 10rpx 0;
				height: 70rpx;
				display: flex;
				align-items: center;
				justify-content: space-around;
				flex-wrap: wrap;
				.title{
					min-width: 150rpx;
					color: #ff8c00;
					font-size: 42rpx;
					letter-spacing: 2rpx;
					font-weight: 600;
					text-align: center;
					// margin-bottom: 10rpx;
				}
			}
			.anlicontent{
				background-color: #f5f5f5;
				padding: 20rpx;
				box-sizing: border-box;
				letter-spacing: 2rpx;
				line-height: 1.6; 
				border-radius: 10rpx;
				.content{
					height: 60rpx;
					line-height: 60rpx;
					letter-spacing: 2rpx;
					.title{
						margin-right: 40rpx;
					}
				}
			}
			.synopsis {
				background-color: #f5f5f5;
				padding:0 20rpx 20rpx 20rpx;
				box-sizing: border-box;
				letter-spacing: 2rpx;
				line-height: 1.6; 
				border-radius: 10rpx;
			}

			.tagBox {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;
				margin-top: 15rpx;
				// margin: 30rpx 0 0 0;
				.tabItem {
					padding: 10rpx 30rpx;
					border-radius: 5rpx;
					background-color: #d7d7d7;
					margin: 0 20rpx 15rpx 0;
					display: flex;
					justify-content: center;
					align-items: center;
					
					image {
						width: 29rpx;
						height: 28rpx;
						margin-right: 10rpx;
					}

					text {
						font-size: 22rpx;
						color: #181818;
					}
				}

			}
			.contact{
				margin-top: 20rpx;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				.contactitem{
					min-width: 280rpx;
					background-color: #f5f5f5;
					margin-bottom: 20rpx;
					border-radius: 10rpx;
					padding: 20rpx;
					.name{
						color: #000;
						font-weight: 600;
						height: 50rpx;
						line-height: 50rpx;
					}
					.phone{
						height: 50rpx;
						line-height: 50rpx;
					}
					.role{
						height: 50rpx;
						line-height: 50rpx;
					}
				}
			}

		}

		.contactNumber {
			margin-bottom: 30rpx;

			.phone {
				image {
					width: 25rpx;
					height: 25rpx;
					margin-right: 8rpx;
				}

				text {
					font-size: 22rpx;
					font-weight: bold;
				}
			}

			li view:nth-child(1) {
				width: 8rpx;
				height: 8rpx;
				border-radius: 50%;
				// background-color: black;
			}

		}

		.describe {
			// min-height: 290rpx;
			background-color: #fff;
			// margin-bottom: 20rpx;
			padding: 20rpx 20rpx 20rpx 20rpx;
			margin: 0 20rpx 20rpx 20rpx;
			box-sizing: border-box;

			.title {
				margin-bottom: 20rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				// padding: 20rpx 0 20rpx 0;

				image {
					width: 49rpx;
					height: 30rpx;
				}

				view {
					font-size: 43rpx;
					font-weight: bold;
					// color: #828282;
					color: #3d3d3d;
					margin: 0 30rpx;
				}

			}

			.describeBox {
				display: flex;
				flex-direction: column;

				.titles {
					font-weight: bold;
					margin: 25rpx 0;
				}

				.text {
					background-color: #f5f5f5;
					padding: 30rpx;
				}
			}

		}

		.substance {
			min-height: 0rpx
		}

		.substanceTitle {
			display: flex;
			align-items: center;
			padding:0 0 10px 0;
			box-sizing: border-box;
			image {
				width: 28rpx;
				height: 30rpx;
			}

			view {
				font-size: 32rpx;
				font-weight: bold;
				margin: 0 0 0 20rpx;
			}

		}

		.substanceHtml {
			.html {
				margin-bottom: 20rpx;
			}

		}

		.photo {
			display: flex;
			flex-wrap: wrap;
			align-items: center;

			image {
				width: 315rpx;
				height: 200rpx;
				margin: 11rpx;
			}

			.video {
				width: 700rpx;
				height: 300rpx;
				margin: 0 auto;
				position: relative;
				.videoimg{
					width: 100%;
					height: 100%;
					border-radius: 10rpx;
				}
				.iconbf{
					position: absolute;
					width: 100rpx;
					height: 100rpx;
					top: 50%;
					left: 50%;
					transform: translate(-50%,-50%);
				}
			}
		}
	}

	.products {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 10rpx;

		image {
			width: 150rpx;
			height: 150rpx;
			border-radius: 20rpx;
		}

		.productsText {
			display: flex;
			flex-direction: column;
			margin-left: 32rpx;

			view:nth-child(1) {
				font-weight: bold;
				color: #333;
				font-size: 32rpx;
				margin-bottom: 30rpx;
			}

			view:nth-child(2) {
				width: 470rpx;
				font-size: 22rpx;
				color: #828282;
				flex-wrap: nowrap;
				/* 禁止换行 */
				white-space: nowrap;
				/* 防止文本换行 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
			}
		}
	}

	.attachmentBox {
		.attachment {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-bottom: 10rpx;

			image {
				width: 150rpx;
				height: 150rpx;
				border-radius: 20rpx;
			}
		}

		.attachmentItem {
			display: flex;
			flex-direction: column;
			margin-left: 32rpx;

			view:nth-child(1) {
				font-weight: bold;
				color: #333;
				font-size: 32rpx;
				margin-bottom: 30rpx;
			}

			view:nth-child(2) {
				width: 470rpx;
				font-size: 22rpx;
				color: #828282;
				flex-wrap: nowrap;
				/* 禁止换行 */
				white-space: nowrap;
				/* 防止文本换行 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
			}

		}

	}

	.address {
		display: flex;

		image {
			width: 43rpx;
			height: 44rpx;
		}

		.addressImg {
			width: 100rpx;
			height: 100rpx;
			background-color: #f5f5f5;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 51rpx;
				height: 44rpx;
			}
		}

		.addressItem {
			width: 460rpx;
			height: 100rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: flex-start;
			margin: 0px 30rpx;
			// box-sizing: border-box;
			// padding: 10rpx 0;

			view:nth-child(1) {
				font-weight: bold;
				color: #333;
			}
		}



	}






	.footer {
		width: 100%;
		height: 100rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
		align-items: center;
		opacity: 0.7;
		image {
			width: 192rpx;
			height: 56rpx;
		}
	}

	.btnbottom {
			height: 140rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;
			right: 0;
			left: 0;
			padding-bottom: 20rpx;
			.btnright{
				width: 95%;
				margin: 0 auto;
				display: flex;
				align-items: center;
				justify-content: space-around;
				// margin-right: 30rpx;
				image {
					width: 32rpx;
					height: 30rpx;
					margin-right: 10rpx;
				}
				color: #fff;
				font-size: 24rpx;
				font-weight: 500;
				letter-spacing: 4rpx;
				.buton1{
					width: 280rpx;
					padding: 18rpx  30rpx;
					border-radius: 15rpx;
					background-color: #fb8c00;
					display: flex;
					align-items: center;
					justify-content: center;
				}
				.buton2{
					width: 280rpx;
					margin-left: 10rpx;
					padding: 18rpx  30rpx;
					border-radius: 15rpx;
					background-color: #07c160;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

	.popupBox {
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;
		padding-bottom: 80rpx;
		margin-bottom: -80rpx;

		.lianxiren {  
			width: 100%;
			min-height: 0rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: center;

			.title {
				height: 100rpx;
				line-height: 100rpx;
				font-size: 34rpx;
				font-weight: bold;
			}

			.lianxiren_item {
				width: 100%;
				height: 80rpx;
				background-color: #f5f5f5;
				text-align: center;
				line-height: 80rpx;
				margin-bottom: 5rpx;
			}

		}
	}
.InfoPopup{
		width: 600rpx;
		min-height: 700rpx;
		background-color: #fff;
		border-radius: 30rpx;
		z-index: 99999;
		margin-bottom: -60rpx;
		padding-bottom: 20rpx;
		.PopupCon{
			width: 92%;
			margin: 0 auto;
			.topCon{
				display: flex;
				justify-content: space-between;
				height: 100rpx;
				align-items: center;
				.Close{
					color: #c1c1c1;
					font-size: 28rpx;
				}
				.title{
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #333333;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}
			.content{
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	.scroll-container {
		flex: 1;
		// background-color: #f0f0f0;
		overflow: scroll;
	}

</style>
