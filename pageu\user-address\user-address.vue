<template>
	<view>
		<newNavBar :title="title" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<no-data :propStatus="propStatus" v-if="isShow"></no-data>
		<view v-if="!isShow">
			<view class="flex flex-center u-p-20 u-m-b-10" v-for="(item,index) in addressList" :key="item.addressId	" style="background-color: #ffffff;">
				<view class="flex-1 u-m-r-40" @tap="selectAddress(item)">
					<u--text :text="item.adAddress1 + item.adAddress2 + item.adAddress3 + item.adAddress5 + item.adAddressDetails" :lines="2"></u--text>
					<view class="flex justify-between align-center u-m-t-10">
						<text style="color: #c1c1c1;">{{item.adUserName}}</text>
						<view class="flex flex-center">
							<text class="u-m-r-20" style="color: #c1c1c1;">+{{item.adContactArea}} {{item.adContactInfo}}</text>
							<text :class="item.sortNumber==0 ? 'is-default' : 'no-default'" @tap.stop="setDefault(item)">{{item.sortNumber==0 ? $t('accountUser.mrdz') : $t('accountUser.swmr')}}</text>
						</view>
					</view>
				</view>
				<view class="flex flex-col justify-between align-center">
					<view class="u-m-b-20" >
						<u-icon name="edit-pen" size="18" @click="editAddress(item)"></u-icon>
					</view>
					<view>
						<u-icon name="trash" size="18" @click="deleteAddress(item)"></u-icon>
					</view>
				</view>
			</view>
			<view style="height: 160rpx;"></view>
		</view>
		<view class="bot-button">
			<u-button color="linear-gradient(to right,#2A3A88FF , #3c9cff)" text="新增收货地址" @tap="addAddress()"></u-button>
		</view>
		<u-modal 
		:show="deleteShow" 
		:title="$t('accountUser.ts')" 
		:content="$t('accountUser.scdzm')" 
		showCancelButton 
		closeOnClickOverlay
		@confirm="confirmDeleteAddress" 
		@cancel="()=>{deleteShow = false}"
		@close="()=>{deleteShow = false}"></u-modal>
	</view>
</template>

<script>
	import {getAddress,postUpdateIsDefault,deletedAddress} from '@/common/request/api/user.js'
  import store from '@/store'
  import api from '@/common/request/index.js'
	export default{
		data(){
			return {
				pageNum:1,
				addressList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				deleteShow:false,
				type:0,
				title: this.$t('accountUser.shdz')
			}
		},
		onLoad(params) {
			if(params.type){
				this.type = params.type
			}
		},
		onShow() {
			this.isShow = true
			this.getAddress()
		},
		onReachBottom() {
			console.log('下拉刷新')
		},
		methods:{
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			getAddress(){
				// this.addressList = []
				api({
					url: 'mb/address/get/list',
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '用户地址数据')
						if(res.data.data.list.length){
							this.addressList = [...res.data.data.list]
							console.log(this.addressList, 'addressList')
							this.isShow = false
						}else{
							this.propStatus = 0
						}
					}
				})
			},
			setDefault(value){
				if(value.sortNumber == 0) return
				api({
					url: `mb/updateIsDefault/address/${value.addressId}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res=>{
					if(res.code == 200){
						uni.showToast({
							title: this.$t('message.szcg'),
							icon:'none'
						})
						// this.addressList = []
						this.getAddress()
					} else {
						uni.showToast({
							title: res.msg,
							icon:'none'
						})
					}
				})
			},
			editAddress(item){
				// 编辑地址
				uni.navigateTo({
					url: `/pageu/user-address-save/user-address-save?address=${JSON.stringify(item)}&type=1`,
				});
			},
			addAddress() {
				// 新增地址
				uni.navigateTo({
					url: `/pageu/user-address-save/user-address-save?type=0`,
				});
			},
			deleteAddress(value){
				console.log(value)
				if(value.sortNumber == 0){
					uni.showToast({
						// title:'默认地址禁止删除',
						title:this.$t('message.mrdzjzsc'),
						icon:'none'
					})
					return
				}
				this.deleteId = value.addressId
				this.deleteShow = true
			},
			confirmDeleteAddress(){
				this.isShow = true
				api({
					url: `mb/deleted/address/${this.deleteId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
				}).then(res=>{
					if(res.code == 200){
						this.deleteShow = false
						this.getAddress()
						uni.showToast({
							// title: '删除成功',
							title:this.$t('message.SCcg'),
							icon:'none'
						})
					} else {
						uni.showToast({
							// title: '删除失败',
							title:this.$t('message.scsb'),
							icon:'none'
						})
					}
				})
			},
			selectAddress(item){
				console.log(item)
				if(this.type == 'dz') {
						uni.navigateBack({
							delta:1,
							success: () => {
								uni.$emit('getaddressId',{
									current:item.addressId
								})
							}
						})

				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.is-default{
		color: #ffffff;
		background-color: #000000;
		padding: 4rpx 10rpx;
		font-size: 20rpx;
	}
	.no-default{
		color: #000000;
		background-color: #ffffff;
		padding: 4rpx 10rpx;
		border: 1rpx solid #000000;
		font-size: 20rpx;
	}
</style>