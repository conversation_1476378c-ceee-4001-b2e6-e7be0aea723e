<template>
	<view class="page">
		<view class="u-p-10 formData" style="background-color: #fff;margin: 10rpx 20rpx;border-radius: 20rpx;padding: 0 20rpx;" >
			<u--form labelPosition="left" :model="formData" :rules="rules" ref="uForm">
				<u-form-item borderBottom prop="supplierName" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 供应商名称</text>
					  </view>
					 </template>
					 <u--input placeholder="供应商名称" v-model="formData.supplierName" maxlength="20"></u--input>
				</u-form-item>
				<u-form-item borderBottom prop="typeInfoList" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 供应商分类</text>
					  </view>
					 </template>
					<uni-data-select v-model="typeInfo" :localdata="gysfl"  placeholder="供应商分类" multiple   @change="changegysfl"></uni-data-select>
				</u-form-item>
				
				<u-form-item borderBottom prop="cooperateLevel" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 合作等级</text>
					  </view>
					 </template>
					<uni-data-select v-model="formData.cooperateLevel" :localdata="hzdj"  placeholder="合作等级" multiple></uni-data-select>
				</u-form-item>
				
				<u-form-item borderBottom prop="adDetails" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 所属区域</text>
					  </view>
					 </template>
					 <!-- <u--input placeholder="所属区域" v-model="formData.adDetails" maxlength="20"></u--input> -->
					 <view class="flex justify-between align-center" @tap="()=>{pickerShow = true}">
					 	<view class="u-m-r-20">
					 		<u--text v-if="formData.adDetails" :text="formData.adDetails" :lines="1"></u--text>
					 		<u--text v-else text="所属区域" color="#c0c4cc"></u--text>
					 	</view>
					 	<u-icon name="arrow-right" color="#c0c4cc"></u-icon>
					 </view>
				</u-form-item>
				
				<u-form-item borderBottom label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 联系方式</text>
					  </view>
					 </template>
					<view class="">
						<!-- <view class="top" style="display: flex;justify-content: center;"> -->
						<view class="top">
							<view class="btn" style="width: 180rpx; padding: 10rpx  30rpx; border-radius: 15rpx; background-color: #2A3A88FF; display: flex; justify-content: center;color: #fff;" @click="addCon">
							   <text>添加联系人</text>
							</view>
						</view>
						<view class="bottom" style="display: flex;flex-wrap: wrap;margin-top: 10rpx;">
							<view v-for="(item,index) in formData.contactsInfoList" :key="index" @tap="showPopup(item, index)">
								<view class="item" style="margin:0 15rpx 15rpx 0;">
									<view class="name" style="margin-bottom: 10rpx;">
										{{item.name}}
									</view>
									<view class="">
										<text style="margin-right: 10rpx;">{{item.phone}}</text>
										<text>{{item.role}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</u-form-item>
				<u-form-item borderBottom prop="supplierRemarks" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;margin-bottom: 10rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;"></text> <text>供应商介绍</text>
					  </view>
					 </template>
					 <u--textarea placeholder="供应商介绍(400字符以内)" height="250rpx" v-model="formData.supplierRemarks" maxlength="400" placeholder-style="font-size: 28rpx;color: #c0c4cc;letter-spacing: 2rpx;"></u--textarea>
				</u-form-item>
				<u-form-item borderBottom prop="sortNumber" label-width="170rpx">
					<template #label>
					  <view style="display: flex;align-items: center;width: 170rpx;">
					  	   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 排序</text>
					  </view>
					 </template>
					 <u--input placeholder="排序" v-model="formData.sortNumber" maxlength="20"></u--input>
					 <view class="text" style="color:red;font-size: 22rpx;margin: 10rpx 0 0 10rpx;">
					 	<text>数字小的在前面</text>
					 </view>
				</u-form-item>
				
			</u--form>
			<view class="u-p-10">
				<view class="u-p-20 bottombtn">
					<view class="stepbtn saveSub">
						<button style="color:#fff;background-color:#2A3A88FF;font-size: 30rpx;" @tap="submitNext" :loading="subloading">保存</button>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="contactPopup" type="center" :is-mask-click="true">
			<view class="languagePopup" @touchmove.stop.prevent="moveHandle">
				<view class="PopupCon">
					<view class="topCon">
						<view style="color: #fff;">
							1
						</view>
						<view class="title" style="color: #000;">
							{{Context}}
						</view>
						<view class="topClose" @click="closeConPopup">
							<image src="https://file.36sm.cn/xtjyjt/images/common/payfor/first.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
						</view>
					</view>
					<view class="Concontent">
						<view class="name" style="display: flex; align-items: center;margin-bottom: 20rpx;">
							<view style="display: flex;align-items: center;width: 170rpx;">
								   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 联系人姓名</text>
							</view>
							<u--input placeholder="联系人姓名" v-model="contactInfo.name" maxlength="20"></u--input>
						</view>
						<view class="name" style="display: flex; align-items: center;margin-bottom: 20rpx;">
							<view style="display: flex;align-items: center;width: 170rpx;">
								   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 联系电话</text>
							</view>
							<u--input placeholder="联系电话" v-model="contactInfo.phone" maxlength="13" type="number"></u--input>
						</view>
						<view class="name" style="display: flex; align-items: center;margin-bottom: 20rpx;">
							<view style="display: flex;align-items: center;width: 170rpx;">
								   <text style="color: red; font-size: 16px;margin-right: 10rpx;">*</text> <text> 职务/角色</text>
							</view>
							<u--input placeholder="职务/角色" v-model="contactInfo.role" maxlength="10"></u--input>
						</view>
						
					</view>
					
					<view class="confirmbtn">
						<u-button color="linear-gradient(to right, #2A3A88FF , #2A3A88FF)" text="保存" @tap="changeconfirm" :loading="Cloading"></u-button>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<u-picker :show="pickerShow" ref="uPicker" :columns="columns" keyName="name" :itemHeight="50" closeOnClickOverlay
			@cancel="()=>{pickerShow = false}" @close="()=>{pickerShow = false}" @change="changeEvent" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
	import {
		getAreaList
	} from '@/common/request/api/map.js'
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				formData: {
					supplierName: "",
					typeInfoList: [],
					cooperateLevel: "",
					contactsInfoList: [],
					supplierRemarks: "",
					sortNumber: "",
					adCity: '',
					adDistrict: '',
					adProvince: '',
					adDetails: ""
				},
				pickerShow: false,
				columns: [
					[],
					[],
					[]
				],
				Context: '新增联系人',
				Cloading: false,
				contactInfo: {
					name: '',
					phone: '',
					role: ''
				},
				subloading: false,
				contactindex: '',
				typeInfo: '',
				rules: {
					supplierName: {
						required: true,
						message: '供应商名称不能为空',
						trigger: ['blur', 'change']
					},
					cooperateLevel: {
						required: true,
						message: '合作等级不能为空',
						trigger: ['blur', 'change']
					},
					adDetails: {
						required: true,
						message: '所属区域不能为空',
						trigger: ['blur', 'change']
					},
					sortNumber: {
						required: true,
						message: '排序不能为空',
						trigger: ['blur', 'change']
					},
				},
				// 一级父类ID列表
				parentCodeIdList: ['1864193512367476736', '1864197314856374272'],
				gysfl: [],
				hzdj: [],
			}
		},
		onLoad() {
			this.schemeList()
			this.getAreaList()
		},
		methods: {
			upload_img() {
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						console.log(tempFilePath, '2222')
						uni.uploadFile({
							url: 'https://cos.jiananxiujiao.com/jaxWebBs/oss/hteditor/upload',
							filePath: tempFilePath,
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								const Data = JSON.parse(res.data)
								console.log(Data, '上传接口返回的数据')
								this.formData.coverImageUrl = Data.data.data.fileUrl
								if (Data.code == 200) {
									uni.showToast({
										title: "上传成功",
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			changeEvent(e) {
				const {
					columnIndex,
					value,
					values,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex < 2) {
					this.getAreaList(value[columnIndex].areaId,columnIndex+1)
				}
			},
			confirm(e) {
				console.log('confirm', e)
				const {value} = e
				this.formData.adDetails = value.filter(a=>a != undefined).map(a=>a.name).join("")
				const province = value[0]?.name
				const city = value[1]?.name
				const district = value[2]?.name
				this.formData.adProvince = value[0]?.name
				this.formData.adCity = value[1]?.name
				this.formData.adDistrict = value[2]?.name
				
				console.log(province, city, district, '省 市 区')
				console.log(this.formData, 'formData')
				this.pickerShow = false
			},
			async getAreaList(id=0,i=0) {
				let params = {
					parentId:id
				}
				let res1 = await getAreaList(params)
				if(res1.code == 200){
					console.log(res1, '地区')
					if(i==0){
						this.$set(this.columns,0,res1.data.data)
						params = {parentId:this.columns[0][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,1,res2.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res3 = await getAreaList(params)
						this.$set(this.columns,2,res3.data.data)
					}else if(i==1){
						this.$set(this.columns,1,res1.data.data)
						params = {parentId:this.columns[1][0].areaId}
						let res2 = await getAreaList(params)
						this.$set(this.columns,2,res2.data.data)
					}else if(i==2){
						this.$set(this.columns,2,res1.data.data)
					}
				}
			},
			moveHandle() {},
			schemeList() {
				const dataList = {
					parentCodeIdList: this.parentCodeIdList
				}
				console.log(dataList, '接口参数');
				api({
					url: `tx/code/get/sub/list/map`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post',
					data: dataList
				}).then(res => {
					console.log(res, '分类列表')
					if (res.code == 200) {
						console.log(res);
						const data = res.data.data
						console.log(data, 'data')
						this.gysfl = data.A1864197314856374272.map(item => ({
						  // value: item.codeId,
						  value: item.name,
						  text: item.name
						}));
						this.hzdj = data.A1864193512367476736.map(item => ({
						  value: item.name, 
						  text: item.name
						}));
					} else {
						uni.showToast({
							title: res.msg,  
							icon: "none"
						})
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg,
						icon: "none"
					})
				})
			},
			addCon() {
				this.Context = '新增联系人'
				this.$refs.contactPopup.open();
			},
			closeConPopup() {
				this.$refs.contactPopup.close();
				this.contactInfo.name = ''
				this.contactInfo.phone = ''
				this.contactInfo.role = ''
				this.contactindex = ''
			},
			showPopup(item, index) {
				this.Context = '编辑联系人'
				this.contactInfo.name = item.name
				this.contactInfo.phone = item.phone
				this.contactInfo.role = item.role
				this.contactindex = index
				this.$refs.contactPopup.open();
			},
			changegysfl(e) {
				console.log(e, 'eeee')
				this.formData.typeInfoList[0] = e
			},
			changeconfirm() {
				if(this.contactInfo.name == '') {
					uni.showToast({
						title: '姓名不能为空',
						icon: "none"
					})
					return
				}
				if(this.contactInfo.phone == '') {
					uni.showToast({
						title: '联系电话不能为空',
						icon: "none"
					})
					return
				}
				if(this.contactInfo.role == '') {
					uni.showToast({
						title: '角色不能为空',
						icon: "none"
					})
					return
				}
				console.log(this.contactindex , 'contactindex')
				if(this.contactindex == '' && this.contactindex !== 0) {
					// 新增
					this.formData.contactsInfoList.push({ ...this.contactInfo })
				} else {
					// 编辑
					
					this.formData.contactsInfoList[this.contactindex] = { ...this.contactInfo }
					// this.$set(this.formData.contactsInfoList, this.contactindex, { ...this.contactInfo });
				}
				this.closeConPopup()
			},
			submitNext() {
				this.$refs.uForm.validate().then(res => {
					if(res) {
						if(this.typeInfo == '') {
							uni.showToast({
								title: '供应商分类不能为空',
								icon: "none"
							})
							return
						}
						
						if(this.formData.contactsInfoList.length == 0) {
							uni.showToast({
								title: '联系方式不能为空',
								icon: "none"
							})
							return
						}
						if(this.subloading) {
							return
						}
						this.subloading = true
						console.log(this.formData, '提交的参数')
						api({
							url: `tx/supplier/hteditor/save`,
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							method: 'post',
							data: this.formData
						}).then(res=>{
							if(res.code == 200){
								console.log(res, 'resres')
								uni.showToast({
									title: '保存成功',
									icon:'none'
								})
								setTimeout(() => {
									this.subloading = false
									uni.navigateTo({
										url: '/pageg/supplier_resources/index'
									})
								}, 500)
							} else {
								this.subloading = false
								uni.showToast({
									title: res.msg,
									icon: "none"
								})
							}
						})
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page{
		width: 100vw;
		// height: 100vh;
		overflow-x: hidden;
		background-color: #F0F1F4FF;
		overflow-y: hidden;
		.formData{
			
			.custom-datetime-picker .uni-date__x-input{
				margin-left: 20rpx;
			}
		/deep/ input::placeholder {
			// #c0c4cc
			  color: red;  /* 设置为灰色，可以根据需求调整 */
			}
			
			
			.bottombtn{
				display: flex;
				align-items: center;
				justify-content: center;
				color: #2A3A88FF;
				.step{
					font-weight: 600;
				}
				.stepbtn{
					width: 600rpx;
					height: 70rpx;
				}
			}
			
			.actionbtn{
				position: fixed;
				bottom: 160rpx;
				left: 0;
				right: 0;
				margin: 0 auto;
				width: 100%;
				z-index: 9;
				background-color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				.step{
					font-weight: 600;
				}
				.stepbtn{
					width: 600rpx;
					height: 70rpx;
				}
			}
		}
		
		
		.remarksinfo{
			width: 100%;
			height: 1200rpx;
			background-color: #fff;
			border-radius: 30rpx;
			z-index: 999;
			margin-bottom: -80rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				.topCon{
					display: flex;
					justify-content: space-between;
					height: 100rpx;
					align-items: center;
					.Close{
						color: #c1c1c1;
						font-size: 28rpx;
					}
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 32rpx;
						color: #333333;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
					.input{
						display: flex;
						align-items: center;
						text{
							margin-left: 20rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 32rpx;
							color: #333333;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}
				}
				.Ccontent{
					.toptabbar{
						height: 60rpx;
						display: flex;
						align-items: center;
						overflow-x: auto;
						white-space: nowrap;
						.cate-item{
							  width: 120rpx;
							  display: inline-block;
							  text-align: center;
							  padding: 10rpx;
							  margin-right: 20rpx;
						}
						.isactive{
							border-bottom: 4rpx solid #2A3A88FF;
						}
					}
					.contentList{
						margin-top: 10rpx;
						height: 880rpx;
						overflow-y: auto;
						// background-color: skyblue;
						.locationlist{
							border: 1rpx solid #000;
							margin-bottom: 10rpx;
							padding:8rpx 15rpx;
							display: flex;
							align-items: center;
							border-radius: 15rpx;
							justify-content: flex-start;
							.right{
								margin-left: 30rpx;
								.top{
									font-family: PingFang SC, PingFang SC;
									font-weight: bold;
									font-size: 32rpx;
									color: #333333;
									font-style: normal;
									text-transform: none;
								}
								.bottom{
									margin-top: 15rpx;
									color: #c1c1c1;
									font-size: 24rpx;
									display: -webkit-box;
									-webkit-line-clamp: 2; 
									  -webkit-box-orient: vertical;
									  overflow: hidden;
									  text-overflow: ellipsis; 
								}
							}
						}
						.isActive{
							border: 1rpx solid #2A3A88FF;
							background-color: rgba(167, 210, 255, 0.2);
						}
					}
					
					
					.no-list{
						margin-top: 15rpx;
						height: 880rpx;
						.noData {
							width: 100%;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							height: 100%;
							image {
								width: 150rpx;
								height: 150rpx;
							}
						
							.name {
								margin: 10rpx 0;
								color: #ccc;
								letter-spacing: 4rpx;
							}
						}
					}
				}
				/* #ifdef APP */
				.confirmbtn{
					height: 150rpx;
					// display: flex;
					// align-items: center;
				}
				/* #endif */
				
				/* #ifndef APP */
				.confirmbtn{
					height: 100rpx;
					display: flex;
					align-items: center;
				}
				/* #endif */
				
			}
		}
		
		.InfoPopup{
			width: 600rpx;
			min-height: 450rpx;
			background-color: #fff;
			border-radius: 30rpx;
			z-index: 99999;
			margin-bottom: -60rpx;
			padding-bottom: 20rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				.topCon{
					height: 80rpx;
					line-height: 80rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
					display: flex;
					justify-content: space-between;
					align-items: center;
					
				}
				.content{
				}
			}
			.bottombtn{
				width: 92%;
				margin: 0 auto;
				margin-top: 20rpx;
				margin-bottom: 20rpx;
			}
		}
		
		.languagePopup{
			width: 580rpx;
			min-height: 520rpx;
			background-color: #fff;
			border-radius: 30rpx;
			.PopupCon{
				width: 92%;
				margin: 0 auto;
				padding-top: 30rpx;
				.topCon{
					display: flex;
					justify-content: space-between;
					margin-bottom: 60rpx;
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 32rpx;
						color: #000000;
						line-height: 38rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
				.Concontent{
					width: 90%;
					min-height: 240rpx;
					margin: 0 auto;
				}
				.confirmbtn{
					width: 80%;
					margin: 0 auto;
					margin-top: 30rpx;
				}
			}
		}
		
		
	}
</style>