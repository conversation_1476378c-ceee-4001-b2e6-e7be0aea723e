<template>
	<view v-if="visible">
		<!-- 身份证信息 -->
		<view class="idcard-container border-radius-main bg-white oh">
			<view class="form-gorup" style="width: 95%;margin: auto;margin-top: 10rpx;">
				<!-- <view class="flex-row align-c br-b-f5 padding-vertical-sm">
					<view class="form-gorup-title">身份证姓名<text class="form-group-tips-must">*</text></view>
					<input type="text" name="idcard_name" :value="address_data.idcard_name || ''" maxlength="16"
						placeholder-class="cr-grey-9" class="cr-base flex-1 flex-width" placeholder="请填写真实姓名" />
				</view>
				<view class="flex-row align-c br-b-f5 padding-vertical-sm">
					<view class="form-gorup-title">身份证号码<text class="form-group-tips-must">*</text></view>
					<input type="idcard" name="idcard_number" :value="address_data.idcard_number || ''" maxlength="18"
						placeholder-class="cr-grey-9" class="cr-base flex-1 flex-width" placeholder="请填写身份证号" />
				</view> -->
				<view class="flex-row align-c padding-vertical-sm margin-bottom-lg">
					<view class="form-gorup-title">身份证照片<text class="form-group-tips-must">*</text></view>
					<view class="form-upload-data cr-grey-9">请填使用身份证原件进行拍摄，图片清晰</view>
				</view>
				<view class="flex-row align-c padding-vertical-sm margin-bottom-lg">
					<view class="form-gorup-title">实名状态:<text class="form-group-tips-must"></text></view>
					<view class="form-upload-data cr-grey-9" style="color: #000000;">{{title}}</view>
				</view>
				<view class="flex-row align-c">
					<view class="flex-width-half oh padding-right-main pr">
						<view v-if="(sfzInfo.sfzFrontUrl || null) != null"
							class="pa top-0 right-0 z-i padding-right-main" @tap="upload_delete_event"
							:data-value="sfzInfo.sfzFrontUrl">
							<iconfont name="icon-bjdz-guanbi" size="28rpx" color="cr-grey-d"></iconfont>
						</view>
						<image class="wh-auto"
							:src="(sfzInfo.sfzFrontUrl || null) != null ? sfzInfo.sfzFrontUrl : common_static_url + 'idcard-front.jpg'"
							data-value="sfzFrontUrl" mode="widthFix" @tap="file_upload_event"></image>
					</view>
					<view class="flex-width-half oh padding-left-main pr">
						<view v-if="(sfzInfo.sfzBackUrl || null) != null" class="pa top-0 right-0 z-i"
							@tap="upload_delete_event" data-value="sfzBackUrl">
							<iconfont name="icon-bjdz-guanbi" size="28rpx" color="cr-grey-d"></iconfont>
						</view>
						<image class="wh-auto"
							:src="(sfzInfo.sfzBackUrl || null) != null ? sfzInfo.sfzBackUrl : common_static_url + 'idcard-back.jpg'"
							data-value="sfzBackUrl" mode="widthFix" @tap="file_upload_back"></image>
					</view>
				</view>
			</view>
			<button @click="upIdentify" class="identify">提交审核</button>
		</view>
	</view>
	<showLoading v-else></showLoading>
</template>

<script>
	const app = getApp();
	import componentNoData from '@/components/no-data/no-data';
	import componentRegionPicker from '@/components/region-picker/region-picker';

	var common_static_url = app.globalData.get_static_url('common');
	export default {
		data() {
			return {
				common_static_url: common_static_url,
				address_data: {},
				sfzInfo: {
					sfzFrontUrl: "",
					sfzBackUrl: ""
				},
				imgSrc: "",
				visible: false,
				title:''
			}
		},
		onLoad() {
			this.get_invite_link()
			setTimeout(() => {
				this.$data.visible = true
			}, 1200)
		},
		methods: {
			
			get_invite_link() {
				this.$HTTP({
					url: "v1/mb/member/invite/code"
				}).then(res => {
					console.log(res);
					this.title=res.msg
					// uni.showToast({
					// 	title: res.data.data
					// })
					// if (res.code == 200) {
					// 	this.invite_link = res.data.data
					// } else {
					// 	this.title=res.data.data
					// 	uni.showToast({
					// 		title: res.data.data
					// 	})
					// }
				}).catch(err => {
					console.log(err, "err")
				})
			},
			
			
			file_upload_event() {
				console.log('身份证正面')
				// uni.chooseImage({
				// 	count: 1, //默认9
				// 	sizeType: ['original'], //可以指定是原图还是压缩图，默认二者都有
				// 	sourceType: ['album'], //从相册选择
				// 	success: function (res) {
				// 		console.log(JSON.stringify(res.tempFilePaths));
				// 	}
				// });
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						this.sfzInfo.sfzFrontUrl = JSON.stringify(res.tempFilePaths).slice(7, -2)
						console.log(this.sfzInfo.sfzFrontUrl, 'url')
						const tempFilePath = res.tempFilePaths;
						console.log(tempFilePath[0], '2222')
						uni.uploadFile({
							url: 'http://**************:8080/ertWeb/oss/hteditor/upload',
							filePath: tempFilePath[0],
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								console.log(res, 'res')
								this.sfzInfo.sfzFrontUrl = JSON.parse(res.data).data
									.data.fileUrl;
								if (JSON.parse(res.data).code == 200) {
									uni.showToast({
										title: "上传成功",
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			file_upload_back() {
				console.log('身份证反面')
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (res) => {
						this.sfzInfo.sfzBackUrl = JSON.stringify(res.tempFilePaths).slice(7, -2)
						const tempFilePath = res.tempFilePaths;
						uni.uploadFile({
							url: 'http://**************:8080/ertWeb/oss/hteditor/upload',
							filePath: tempFilePath[0],
							name: "multipartFile",
							header: {
								"jarepair-platform": uni.getStorageSync("token")
							},
							success: (res) => {
								this.sfzInfo.sfzBackUrl = JSON.parse(res.data).data
									.data.fileUrl
								if (JSON.parse(res.data).code == 200) {
									uni.showToast({
										title: "上传成功",
										icon: 'success'
									})
								}
							}
						})
					},
				})
			},
			upIdentify() {
				if (this.sfzInfo.sfzBackUrl == "" || this.sfzInfo.sfzFrontUrl == "") {
					uni.showToast({
						title: "请上传完整证件",
						icon: "error"
					})
				} else {
					this.$HTTP({
						url: 'v1/mb/member/realNameAuthentication', //请求具体地址
						data: this.sfzInfo
					}).then((res) => {
						if (res.code == 200) {
							uni.showToast({
								title: "您已发送审核",
								icon: "success"
							});
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						}
					}).catch(() => {
						uni.hideLoading();
						console.log("验证失败")
					})
				}
			},
		}
	}
</script>
<style scoped>
	@import './user-identify.css';

	.identify {
		margin-top: 80rpx;
		width: 95%;
		background: linear-gradient(270deg, #2294FE 0%, #324DFD 100%);
		box-shadow: 0rpx 2rpx 5rpx 0rpx rgba(0, 0, 0, 0.1);
		height: 90rpx;
		font-size: 33rpx;
		font-family: Source Han Sans, Source Han Sans;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 90rpx;
		letter-spacing: 2px;
	}
</style>