<template>
	<view class="waterfall">
		<view class="loadingLayout" v-if="!RecommendList.length && !noData">
			 <uni-load-more status="'loading'"></uni-load-more>
		</view>
		<view class="waterfallCon" v-if="RecommendList.length > 0">
			<view class="waterfallitem" v-for="(item,index) in RecommendList" :key="index">
				<view class="titletop">
					<view class="title">
						<text>{{item.tcTitle}}</text>
					</view>
					<view class="rightnum">
						<image src="https://file.36sm.cn/xtjyjt/images/common/tcbg.png" mode=""></image>
						<view class="value">
							提成<text class="num">{{item.actualAmount}}</text>元
						</view>
					</view>
				</view>
				<view class="centercontent">
					<view class="toptitle">
						<u--text :lines="2" text="项目基本情况介绍XXXXX项目基本情况介绍XXXXX项目基本情况介绍XXXXX 项目基本情况介绍XXXXX项目基本情况介绍XXXXX"
						  size="26rpx" color="#c1c1c1"></u--text>
					</view>
					<view class="centerProcess" v-if="item.tcType !== 3">
						<view class="xqdj content">
							<view class="title">
								<text>需求提交</text>
							</view>
							<view class="name">
								<text>{{item.submitterName}}</text>
							</view>
						</view>
						<view class="ywdj content">
							<view class="title">
								<text>业务对接</text>
							</view>
							<view class="name">
								<text>{{item.liaisonName}}</text>
							</view>
						</view>
						<view class="xmzx content">
							<view class="title">
								<text>项目执行</text>
							</view>
							<view class="name">
								<text>{{item.executorName}}</text>
							</view>
						</view>
					</view>
					
					<view class="total">
						<view class="price">
							<text>总价: </text>
							<text class="value">￥{{item.quotationAmount}}</text>
						</view>
						<view class="time">
							<image src="https://file.36sm.cn/xtjyjt/images/common/naoz.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
							<text>{{item.nextFkTime.slice(0, 10)}}</text>
						</view>
						
						<!-- 资源类型 付款状态不显示 -->
						<view class="button" v-if="item.tcType !== 3">
							<u-button :text="item.ztStatusName" size="mini" color="linear-gradient(to right, #63C86EFF, #63C8C14D)"></u-button>
						</view>
					</view>
				</view>
				<view class="bottom" v-if="item.departmentName !== ''|| item.departmentName !== undefined">
					<text>{{item.departmentName}}</text>
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
			onLoad,
			onShow,
			onReachBottom
		} from "@dcloudio/uni-app"
		
	export default {
		data() {
			return {
				noData: false,
				bottomList: [
				        '团前会议',
				        '资料打印',
				        '开始出团',
				        '客户签到',
				        '出团结束',
				        '客户评价',
				        '物料归还',
				        '提交结算',
				        '项目复盘'
				    ],
					deflautWidth: 20
			}
		},
		components: {
		},
		mounted() {
		},
		props: {
			RecommendList: {
				type: Array
			},
			CanClick: {
				type:Boolean,
				default: true
			},
			ishome: {
				type:Boolean,
				default: false
			},
			platformType: {
				type: String || Number,
				default: '0'
			}
		},
		methods: {
		}
	}
</script>

<style lang="scss" scoped>
	.waterfall{
		.waterfallCon{
			.waterfallitem{
				background-color: #fff;
				border-radius: 16rpx;
				margin-bottom: 15rpx;
				padding: 15rpx 25rpx;
				
				.titletop{
					display: flex;
					align-items: center;
					justify-content: space-between;
					// margin-top: 10rpx;
					.title{
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 32rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
					}
					
					.rightnum {
						  position: relative;
						  image {
							width: 150rpx;
							height: 42rpx;
							position: relative;
						  }
						  .value {
							height: 40rpx;
							line-height: 40rpx;
							width: 150rpx;
							text-align: center;
						    position: absolute;
						    top: 0;
						    left: 50%;
						    transform: translate(-50%, 0);
						    font-family: PingFang SC, PingFang SC;
						    font-weight: 400;
						    font-size: 18rpx;
						    color: #222222;
						    font-style: normal;
						    text-transform: none;
						    letter-spacing: 1rpx;
							.num {
							   font-size: 30rpx;
							   font-weight: 700;
							}
						  }
					}
				}
				
				.centercontent{
					.toptitle{
						width: 654rpx;
						height: 60rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #b6b6b6;
						line-height: 28rpx;
						font-style: normal;
						text-transform: none;
						letter-spacing: 2rpx;
						margin: 10rpx 0;
					}
					.centerProcess{
						display: flex;
						justify-content: space-between;
						.content{
							width: 218rpx;
							height: 108rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							.title{
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 26rpx;
								color: #666666FF;
								line-height: 30rpx;
								font-style: normal;
								text-transform: none;
								margin-bottom: 15rpx;
							}
							.name{
								font-family: PingFang SC, PingFang SC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333FF;
								line-height: 33rpx;
								text-align: center;
								font-style: normal;
								text-transform: none;
							}
						}
						.xqdj{
							width: 33.3%;
							background-color: #F4F5FFFF;
						}
						.ywdj{
							width: 33.3%;
							background-color: #FF763633;
						}
						.xmzx{
							width: 33.3%;
							background-color: #FFEB5533;
						}
					}
					
					.total{
						display: flex;
						justify-content: space-between;
						align-items: center;
						height: 50rpx;
						padding: 10rpx 0;
						margin: 5rpx 0;
						border-bottom: 1rpx solid #f3f3f3;
						font-family: PingFang SC, PingFang SC;
						font-style: normal;
						text-transform: none;
						.price{
							.value{
								margin-left: 10rpx;
								font-weight: 500;
								font-size: 32rpx;
								color: #333333;
							}
						}
						.time{
							font-weight: 500;
							font-size: 24rpx;
							color: #999999;
							text{
								margin-left: 5rpx;
							}
						}
					}
				}
				.bottom{
					padding: 10rpx 0 5rpx 0;
					display: flex;
					justify-content: flex-end;
					align-items: center;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #bcbcbc;
					letter-spacing: 2rpx;
					letter-spacing: 2rpx;
					// height: 34rpx;
					// line-height: 34rpx;
				}
			}
		}
	}
</style>