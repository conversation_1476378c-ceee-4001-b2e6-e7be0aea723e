/*
* 导航
*/
.nav-base {
	margin-left: 5rpx;
	width: 100vw;
	overflow-x: scroll;
	display: flex;
	justify-content: center;
	align-items: center;
}

.nav-base .item {
	width: auto;
	padding: 2rpx 14rpx;
	margin: 0 10rpx 0 0;
	height: 56rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: Source <PERSON>, Source <PERSON>;
	font-weight: 700;
	font-size: 29rpx;
	color: #3D3D3D;
	line-height: 33rpx;
	letter-spacing: 1px;
	text-align: center;
	font-style: normal;
	text-transform: none;
}

.order-scroll {
	height: calc(100vh - 80rpx);
}

.active-status {
	background-color: #0C71FD;
	border-radius: 7rpx;
	color: #FFFFFF !important;
}

/*
* 列表
*/
.dis-inline-block {
	font-family: Source <PERSON>, Source <PERSON>;
	font-weight: 500;
	font-size: 29rpx;
	color: #3D3D3D;
	line-height: 36rpx;
	letter-spacing: 1px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.goods-base {
	margin-left: 140rpx;
	width: 70%;
}

.goods-name {
	width: 65vw;
	overflow: hidden;
	height: 90rpx;
	display: flex;
	font-family: Source <PERSON>, Source <PERSON>s;
	font-weight: 500;
	font-size: 30rpx;
	color: #3D3D3D;
	line-height: 47rpx;
	letter-spacing: 1px;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.goods-amount {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 22rpx;
	color: #7D7D7D;
	line-height: 36rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.goods-image {
	width: 127rpx;
	height: 127rpx;
}

.atention {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.count-down {
	width: 640rpx;
	height: 55rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 8rpx;
	background-color: #F7F7F7;
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 400;
	font-size: 22rpx;
	letter-spacing: 2rpx;
	color: #3D3D3D;
	line-height: 36rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}
.count-down .count-image {
	width: 27rpx;
	height: 27rpx;
	margin-right: 20rpx;
}
.last-time {
	color: #FE6952;
}
.price {
	font-family: Source Han Sans, Source Han Sans;
	font-weight: 500;
	font-size: 22rpx;
	color: #3D3D3D !important;
	line-height: 36rpx;
	font-style: normal;
	text-transform: none;
}
.sale-price {
	font-size: 29rpx;
}
.warehouse-group-icon {
	width: 30rpx;
	height: 30rpx;
}

.orderaftersale-btn-text {
	right: 0;
	bottom: 0;
	z-index: 1;
}

/**
* 支付方式
*/
.payment-list .item {
	width: 50%;
}

.payment-list .item-content {
	padding: 20rpx 10rpx;
}

.payment-list .item-content image {
	width: 50rpx;
	height: 50rpx !important;
}

/**
* 合并支付按钮
*/
.pay-merge-submit {
	width: 100%;
	height: 88rpx !important;
	line-height: 88rpx !important;
}

/* 修改样式 */
.cr-red {
	color: #FE6952 !important;
}

.cr-yellow {
	color: #3D3D3D !important;
}

.br-yellow {
	border: 1px solid #7D7D7D !important;
}

.round {
	border-radius: 240rpx !important;
}

.cr-green {
	color: #3D3D3D !important;
}

.br-green {
	border: 1px solid #7D7D7D !important;
}

.cr-blue {
	color: #0C71FD !important;
}

.br-blue {
	border: 1px solid #0C71FD !important;
}

.multi-text {
	color: #8B8B8B;
}