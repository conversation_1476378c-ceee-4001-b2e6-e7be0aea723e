<template>
	<view>
		<newNavBar :title="$t('search.xxxq')" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		<!-- <no-data :propStatus="propStatus" v-if="isShow"></no-data> -->
		<view v-if="visible">
			<view class="messageInfo">
				<view class="messagedetail">
					<view class="usename">
						<text>{{messageInfo.msName}}</text>
					</view>
					<view class="Mescenter">
						<view class="labelTitleList" v-for="(item, i) in messageInfo.labelTitleList" :key="i">
							<view class="item">
								{{item}}
							</view>
						</view>
						
						<view class="time">
							{{messageInfo.createTime}}
						</view>
					</view>
					
					<view class="content">
						<u-parse :content="messageInfo.basicInfo"></u-parse>
					</view>
				</view>
			</view>
		</view>
		<cc-gifLoading v-else
			gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading>
	</view>
</template>

<script>
  import store from '@/store'
  import api from '@/common/request/index.js'
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
  
	export default{
		data(){
			return {
				pageNum:1,
				isShow:true,
				propStatus:1,
				deleteId:'',
				type:0,
				// 下拉触底加载效果
				loadingStatus: 	true,
				messageInfo: {},
				checkStatus: "",
				mxId: "",
				messageId: "",
				visible: false
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad(params) {
			if(params.mxId){
				this.mxId = params.mxId
			}
			if(params.messageId){
				this.messageId = params.messageId
			}
			this.checkStatus = params.checkStatus
			this.getMessagedetail()
			setTimeout(() => {
				this.$data.visible = true
			}, 1500)
		},
		onReachBottom() {
			
		},
		onShow() {
			if(this.mxId) {
				this.getMessagedetail()
			}
			
			if(this.checkStatus == 0) {
				// checkStatus  为0 表示未读状态
				this.changeCheckStatus()
			}
		},
		computed: {
		},
		methods:{
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			// 获取消息详情
			getMessagedetail(){
				api({
					url: `mb/message/details/${this.messageId}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
				    if (res.code === 200) {
						this.messageInfo = res.data.data
						console.log(this.messageInfo, '消息详情')
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
				
			},
			// 标记为已读
			changeCheckStatus() {
				api({
					url: `mb/message/mark/${this.mxId}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'post'
				}).then(res => {
				    if (res.code === 200) {
						console.log('状态修改成功')
				    } else {
				        uni.showToast({
				            title: res.msg,
				            icon: "none"
				        });
				    }
				}).catch(err => {
				    uni.showToast({
				        title: err.msg || '请求失败',
				        icon: "none"
				    });
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.messageInfo{
	border-top-left-radius: 50rpx;
	border-top-right-radius: 50rpx;
	background-color: #fff;
	width: 100%;
	min-height: 1400rpx;
	.messagedetail{
		padding: 60rpx  50rpx 0 50rpx;
		.usename{
			color: #000;
			font-weight: 600;
			font-size: 35rpx;
			letter-spacing: 2rpx;
		}
		.Mescenter{
			display: flex;
			margin:  60rpx 0 80rpx 0;
			align-items: center;
			justify-content: space-between;
			
			.labelTitleList{
				display: flex;
				align-items: center;
				.item{
					font-size: 20rpx;
					color: #af7e29;
					min-width: 110rpx;
					height: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #ffeddb;
				}
			}
			
			.time{
			}
		}
	}
	
}
</style>