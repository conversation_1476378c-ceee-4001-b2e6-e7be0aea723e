## 2.0.0（2023-11-14）
1、新增切换拦截返回当前点击index
2、新增自动读取主页面配置功能
3、优化部分代码结构实现
注意：代码重构优化，2.0.0版本不支持老版本兼容
## 1.3.3（2023-02-27）
修复openType类型为navigateTo时，页面返回，重新点击不响应问题
## 1.3.2（2023-02-27）
修改占位空间点击事件遮挡问题
## 1.3.1（2023-02-27）
1、新增页面打开方式,可以自定义每个tabbarItem的点击打开方式
2、新增tabbarHeight参数,方便你直接使用参数控制tabbar高度
## 1.3.0（2023-02-21）
新增ref方法若干，优化使用说明文档
## 1.2.9（2023-02-21）
修改safe计算，使用原生css，抛弃原始计算耗时能力
## 1.2.8（2023-02-20）
修正文档使用说明
## 1.2.7（2023-02-20）
1、新增角标显示
2、新增setTabBarBadge，reLoad方法
## 1.2.6（2023-02-13）
修复判断safebottom底部安全距离数值写死问题
## 1.2.5（2023-01-12）
1、修复默认初始高度闪烁问题，不在使用计算，直接默认
2、修复上个版本遗留current选中问题
3、修复safeBottom底部判断错误问题
4、更新使用说明文档
## 1.2.4（2023-01-09）
新增文档说明，增加示例项目页面
## 1.2.3（2022-12-14）
修复外部不跳转页面修改current，组件不响应bug
## 1.2.2（2022-12-14）
新增click事件，click事件会优先于任何事件
## 1.2.1（2022-12-09）
修改文档使用说明，路由拦截有平台兼容性，暂时无法解决，平台原生不支持
## 1.2.0（2022-12-09）
新增插槽，可以自定义凸起导航，优化文档使用说明
## 1.1.9（2022-12-06）
修复iconPath图片路径为网络路径判断错误问题
## 1.1.7（2022-12-01）
优化部分机器计算错误问题
## 1.1.6（2022-12-01）
修改高度计算错误问题
## 1.1.5（2022-12-01）
修复各别设备上高度计算有误问题
## 1.1.4（2022-11-25）
修复vue2版本在微信小程序上不支持:style传入参数bug，优化参数错误逻辑
## 1.1.3（2022-11-24）
修复native模式borderStyle样式丢失问题
## 1.1.2（2022-11-24）
修改描述文档说明，修改插件描述信息
## 1.1.1（2022-11-24）
优化使用说明文档
## 1.1.0（2022-11-24）
极度简化native模式，页面只需要引入组件即可，任何操作都不需要
## 1.0.9（2022-11-24）
修复native模式下，fill忘记计算高度.
## 1.0.8（2022-11-24）
优化native模式，简化参数数量，使用更简单
## 1.0.7（2022-11-24）
新增native配置，可以兼容原生tabbar，新增beforeChange，可自行根据要求自己兼容路由守卫
## 1.0.6（2022-11-23）
修改文档描述错误
## 1.0.5（2022-11-23）
修复fill高度遗漏安全距离问题，文档使用说明优化更新
## 1.0.4（2022-11-23）
优化配置选项，提取当前选中项，新增fixed配置
## 1.0.3（2022-11-14）
添加上阴影效果，修复由于去除了上线条，造成如果内容如果是白色，tabbar会和内容高度重合的问题
## 1.0.2（2022-11-14）
修改说明文档，更加详细备注说明
## 1.0.1（2022-11-14）
新增当前选中项class名，方便用户直接样式覆盖
## 1.0.0（2022-11-14）
第一个自定义tabbar版本
