import api from '../index.js'


const location = uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh';


// 获取商品详情
export const getGoodsDetails = params =>
	api({
		url: 'sp/product/get/product',
		params
	})
// 获取商品详情轮播图
export const getGoodsImage = params =>
	api({
		url: 'sp/image/product/get/list',
		params
	})
// 生成订单
export const postShoppingOrder = data =>
	api({
		url: 'mb/save/shoppingOrder',
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '生成订单中...'
	})
// 获取订单详情
export const getShoppingOrder = orderId =>
	api({
		url: `mb/shoppingOrder/get/${orderId}`
	})
// 购买
export const getShoppingOrderTransaction = data =>
	api({
		url: `mb/save/shoppingOrder/transaction/${data.orderId}/${data.payType}`
	})
// 获取订单列表
export const getShoppingOrderList = params =>
	api({
		url: 'mb/shoppingOrder/get/list',
		params
	})
// 取消订单
export const postCancelPaymentOrder = data =>
	api({
		url: `mb/cancelPayment/${data.orderId}`,
		method: 'post',
		data
	})
// 获取数字人订单列表
export const getCloneList = () =>
	api({
		url: 'mb/get/cloneList',
	})
// 保存平台数字人克隆订单
export const postCloneOrder = data =>
	api({
		url: 'mb/cloneOrder/save',
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '生成订单中...'
	})
// 获取数字人克隆订单的列表数据
export const getCloneOrderList = params =>
	api({
		url: 'mb/cloneOrder/get/list',
		params
	})
// 获取衍生品-内容的列表数据
export const getDerivativeList = params =>
	api({
		url: 'dt/derivative/get/list',
		params
	})
// 获取衍生品详情信息
export const getDerivative = derivativeId =>
	api({
		url: `dt/derivative/get/${derivativeId}`
	})
// 获取幂等性令牌
export const getOrderToken = () =>
	api({
		url: 'pay/getOrderToken'
	})
// 生成衍生品订单
export const postDerivativeOrderSaveOrder = (data, orderToken) =>
	api({
		url: `dt/derivativeOrder/saveOrder/${orderToken}`,
		method: 'post',
		data
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在生成订单'
	})
// 购买衍生品
export const postDerivativeOrderPayOrder = (data) =>
	api({
		url: `dt/derivativeOrder/payOrder/${data.orderId}/${data.orderToken}/${data.payType}`,
		method: 'post',
	})
// 获取衍生品订单列表
export const getDerivativeOrderList = (params) =>
	api({
		url: 'dt/derivativeOrder/get/list',
		params
	})
// 获取衍生品订单详情
export const getDerivativeOrder = (orderId) =>
	api({
		url: `dt/derivativeOrder/get/${orderId}`,
	})
// 生成衍生品订单
export const postDerivativeOrderCancelPayment = (orderId) =>
	api({
		url: `dt/derivativeOrder/cancelPayment/${orderId}`,
		method: 'post',
	}, {
		auth: false,
		isLoading: true,
		loadingMsg: '正在取消订单'
	})
	
	
	
export const getTypeOrder = data => {
	api({
		url: `goods/get/order/type/list?location=${location}`,
		method: 'post',
		header: {
			"jarepair-platform": uni.getStorageSync("token")
		},
		data
	})
}