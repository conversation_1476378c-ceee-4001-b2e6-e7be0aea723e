<template>
	<view class="page">
		<!-- <newNavBar title="邀请记录" width="100vw" @clickLeftIcon="goBack"></newNavBar> -->
		<newNavBar :title="$t('message.yqjl')" width="100vw" @clickLeftIcon="goBack"></newNavBar>
		
		<view class="TopTabs">
			<view class="tab_item" @click="IndexTab(item)" v-for="(item,index) in FiledList" :key="index" :class="tabName == item.name ? 'tabActive' : ''" >
				<text >{{item.name}}</text>
				<view :class="tabName == item.name ? 'line' : ''"></view>
			</view>
		</view>
		<no-data :propStatus="propStatus" v-if="isShow"></no-data>
		<view v-if="!isShow">
			<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="false"
				:use-page-scroll="true">
				<refresh-loading slot="refresher"></refresh-loading>
				<view class="flex flex-center u-p-20 u-m-b-10" v-for="(item,index) in addressList" :key="item.addressId	" style="background-color: #ffffff;">
					<view class="flex-1 u-m-r-40" >
						<!-- <u--text :text="item.adAddress1 + item.adAddress2 + item.adAddress3 + item.adAddressDetails" :lines="2"></u--text> -->
						<u--text :text="item.nickName" :lines="2"></u--text>
						<view class="flex justify-between align-center u-m-t-10">
							<text>{{item.phone}}</text>
							<view class="flex flex-center">
								<text class="u-m-r-20" style="color: #ccc;">{{item.createTime}}</text>
								<!-- <text :class="item.sortNumber==0 ? 'is-default' : 'no-default'" @tap.stop="setDefault(item)">{{item.sortNumber==0 ? '默认地址' : '设为默认'}}</text> -->
							</view>
						</view>
					</view>
					<!-- <view class="flex flex-col justify-between align-center">
						<view class="u-m-b-20" >
							<u-icon name="edit-pen" size="18" @click="editAddress(item)"></u-icon>
						</view>
						<view>
							<u-icon name="trash" size="18" @click="deleteAddress(item)"></u-icon>
						</view>
					</view> -->
				</view>
				
				<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
				<component-bottom-line :propMsg="$t('accountUser.mysjl')" v-else></component-bottom-line>
			</z-paging>
			<!-- <view style="height: 160rpx;"></view>/ -->
		</view>
		<!-- <view class="bot-button">
			<u-button color="linear-gradient(to right,#2A3A88FF , #3c9cff)" text="新增收获地址+" @click="editAddress"></u-button>
		</view> -->
	</view>
</template>

<script>
  import {getAddress,postUpdateIsDefault,deletedAddress} from '@/common/request/api/user.js'
  import store from '@/store'
  import api from '@/common/request/index.js'
  
  import componentBottomLine from '../../components/bottom-line/bottom-line';
  import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
  
	export default{
		data(){
			return {
				pageNum:1,
				addressList:[],
				isShow:true,
				propStatus:1,
				deleteId:'',
				deleteShow:false,
				type:0,
				params: {
					page: 1,
					size: 15
				},
				// 下拉触底加载效果
				loadingStatus: 	true,
				FiledList: [
					{
						name: this.$t('message.yqlb'),
						index: 1
					},
					{
						name: this.$t('message.yjpm'),
						index: 2
					}
				],
				tabName: this.$t('message.yqlb')
				
			}
		},
		components: {
			componentBottomLine,
			componentBottomLoading
		},
		onLoad(opt) {
			if(opt.type){
				this.type = opt.type
			}
		},
		onShow() {
			if(this.tabName == '邀请列表') {
				this.getAddress()
			} else {
				this.getinvitelist()
			}
		},
		onReachBottom() {
			console.log('触底刷新')
			this.loadingStatus = true
			this.params.page ++
			if(this.tabName == '邀请列表') {
				this.getAddress()
			} else {
				this.getinvitelist()
			}
			
		},
		computed: {
			location() {
				return uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh'
			},
		},
		methods:{
			IndexTab(item) {
				this.params.page = 1
				this.params.size = 15
				this.tabName = item.name
				this.addressList = []
				if(this.tabName == '邀请列表') {
					this.getAddress()
				} else {
					this.getinvitelist()
				}
			},
			queryList() {
				// this.addressList = []
				this.params.page = 1
				this.params.size = 15
				if(this.tabName == '邀请列表') {
					this.getAddress()
				} else {
					this.getinvitelist()
				}
				
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url:"/pages/home/<USER>"
						})
					}
				}) 
			},
			// 邀请列表
			getAddress(){
				api({
					url: `mb/member/invite/list?location=${this.location}&page=${this.params.page}&size=${this.params.size}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '邀请记录')
						if(res.data.data.list.length){
							if(this.addressList.length == res.data.data.totalCount) {
								// 已获取到全部数据
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							} else {
								if(this.params.page == 1) {
									this.addressList = [...res.data.data.list]
								} else {
								    this.addressList = [...this.addressList, ...res.data.data.list]
								}
								
								console.log(this.addressList, 'addressList')
								this.isShow = false
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							}
						}else{
							this.propStatus = 0
						}
					}
				})
			},
			
			// 业绩排名
			getinvitelist() {
				api({
					url: `mb/member/invite/pm/list?location=${this.location}&page=${this.params.page}&size=${this.params.size}`,
					header: {
					    "jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res=>{
					if(res.code == 200){
						console.log(res, '会员邀请排名')
						if(res.data.data.list.length){
							if(this.addressList.length == res.data.data.totalCount) {
								// 已获取到全部数据
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							} else {
								if(this.params.page == 1) {
									this.addressList = [...res.data.data.list]
								} else {
								    this.addressList = [...this.addressList, ...res.data.data.list]
								}
								
								console.log(this.addressList, 'addressList')
								this.isShow = false
								setTimeout(() => {
									this.loadingStatus = false
								}, 1500)
							}
						}else{
							this.propStatus = 0
						}
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #F2F6FA;
	}
	.TopTabs{
		background-color: #fff;
		width: 100%;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 50rpx;
		margin-bottom: 10rpx;
		.tab_item{
			font-family: PingFang SC, PingFang SC;
			font-weight: blod;
			font-size: 29rpx;
			min-width: 200rpx;
			color: #333333;
			font-style: normal;
			text-align: center;
			margin-right: 50rpx;
			.line{
				// background: linear-gradient(to right, #2A3A88FF, #3c9cff);
				background:#3c9cff;
				width:  70%;
				height: 12rpx;
				border-radius: 20rpx;
				margin: 0 auto;
				margin-top: -10rpx;
				z-index: -99;
			}
		}
		.tabActive{
			font-family: PingFang SC, PingFang SC;
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
			height: 46rpx;
			line-height: 46rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
	}
	
	
	.is-default{
		color: #ffffff;
		background-color: #000000;
		padding: 4rpx 10rpx;
		font-size: 20rpx;
	}
	.no-default{
		color: #000000;
		background-color: #ffffff;
		padding: 4rpx 10rpx;
		border: 1rpx solid #000000;
		font-size: 20rpx;
	}
</style>