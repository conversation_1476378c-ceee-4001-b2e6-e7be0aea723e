<template>
	<!-- 自定义导航组件 -->
	<view class="tabbar-container">
		<view class="tabbar_item" v-for="(item,index) in loginTabbar" :key="item.id" @tap="changeItem(item)">
			<view class="item_top">
				<image :src="path == item.path ? item.selecticon : item.icon" mode="scaleToFill"></image>
			</view>
			<view class="item_title" :class="path == item.path ? 'isActive' : '' ">
				{{item.text}}
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from "vuex"
	import api from '@/common/request/index.js'
	export default {
		data() {
			return {
				currentItem: 0,
				loginTabbar: [{
						id: 0,
						path: '/pages/home/<USER>',
						icon: '../../static/images/tabbar/shouye.png',
						selecticon: '../../static/images/tabbar/shouye1.png',
						text: "首页",
						centerItem: false
					},
					{
						id: 1,
						path: '/pages/digital-avatar/index',
						icon: '../../static/images/tabbar/chanp.png',
						selecticon: '../../static/images/tabbar/chanp1.png',
						text: "产品",
						centerItem: false

					},
					{
						id: 3,
						path: '/pages/branded-space/index',
						icon: '../../static/images/tabbar/chutuan.png',
						selecticon: '../../static/images/tabbar/chutuan1.png',
						text: "足疗",
						centerItem: false
					},
					{
						id: 4,
						path: '/pages/account/user',
						icon: '../../static/images/tabbar/wode.png',
						selecticon: '../../static/images/tabbar/wode1.png',
						text: "我的",
						centerItem: false
					}
				],
				unloginTabbar: [
					{
							id: 0,
							path: '/pages/home/<USER>',
							icon: '../../static/images/tabbar/shouye.png',
							selecticon: '../../static/images/tabbar/shouye1.png',
							text: "首页",
							centerItem: false
						},
						{
							id: 1,
							path: '/pages/digital-avatar/index',
							icon: '../../static/images/tabbar/chanp.png',
							selecticon: '../../static/images/tabbar/chanp1.png',
							text: "产品",
							centerItem: false
					
						},
						{
							id: 3,
							path: '/pages/message/index',
							icon: '../../static/images/tabbar/message.png',
							selecticon: '../../static/images/tabbar/message1.png',
							text: "消息",
							centerItem: false
						},
						{
							id: 4,
							path: '/pages/account/user',
							icon: '../../static/images/tabbar/wode.png',
							selecticon: '../../static/images/tabbar/wode1.png',
							text: "我的",
							centerItem: false
						}
				],
				// 购物车上商品的数量
				CartNum: 0,
			}
		},
		props: {
			path: {
				type: String,
				default: 'pages/home/<USER>'
			}
		},
		destroyed() {
		},
		mounted() {
			uni.hideTabBar();
		},
		onLoad() {},
		methods: {
			changeItem(item) {
				this.currentItem = item.id
				uni.switchTab({
					url: item.path
				});
				// 切换页面滚动到 每个页面的顶部
				uni.pageScrollTo({
					scrollTop:0,
						duration: 0
					})
			},
			
		},
	}
</script>

<style lang="scss" scoped>
	.tabbar-container {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 140rpx;
		line-height: 140rpx;
		display: flex;
		align-items: center;
		z-index: 888;
		background-color: #fff;

		.tabbar_item {
			width: 20%;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			text-align: center;
			position: relative;
			.item_top {
				width: 52rpx;
				height: 52rpx;
				image {
					width: 40rpx;
					height: 40rpx;
					margin-bottom: 30rpx;
				}
			}

			// .item_center {
			// 	height: 85rpx;
			// 	width: 85rpx;
			// 	position: absolute;
			// 	top: -28rpx;

			// 	image {
			// 		width: 90rpx;
			// 		height: 90rpx;
			// 	}
			// }

			.item_title {
				margin-top: -15rpx;
				width: 60rpx;
				height: 40rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 20rpx;
				color: #A4A8B8;
				line-height: 40rpx;
				font-style: normal;
				text-transform: none;
			}

			.isActive {
				color: #2A3A88;
			}
		}
	}
</style>