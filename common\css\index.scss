// page {
// 	-webkit-overflow-scrolling: touch; //ios滑动不流畅
// 	font-size: 28rpx;
// 	word-break: break-all; //英文文本不换行
// 	color: #333333;
// 	box-sizing: border-box;
// }
// input{
// 	font-size: 28rpx;
// }
// img{
// 	display: block
// }
// image {
// 	display: block;
// }
// button::after {
// 	border: none;
// }

// view,
// scroll-view,
// swiper,
// button,
// input,
// textarea,
// label,
// navigator,
// image {
// 	box-sizing: border-box;
// }

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

// flex
.flex{
	display: flex;
}
.align-initial {
	align-items: initial !important;
}
.align-end {
	align-items: flex-end !important;
}
.align-stretch {
	align-items: stretch !important;
	/*flex-wrap布局下使每一列内元素高度保持一致*/
}
.flex-col {
	flex-direction: column !important;
}
.flex-row {
	flex-direction: row !important;
}
.flex-1 {
	flex: 1;
}
.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.align-center {
  align-items: center;
  /* 上下对齐 */
}

.justify-between {
  justify-content: space-between;
  /* 左右对齐 */
}

.justify-end {
  justify-content: flex-end;
  /* 居右对齐 */
}

.justify-center {
  justify-content: center;
  /* 居中对齐 */
}

// 升级uView2.x版本后被去除的内置样式
// 定位
.u-rela {
	position: relative;
}
.u-abso {
	position: absolute;
}

// 文本单行溢出
.line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 文本多行溢出
.line-2 {
    word-break: break-all;
    display: -webkit-box!important;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical!important;
}
.bot-button {
	box-sizing: border-box;
	width: 100%;
	background-color: #ffffff;
	position: fixed;
	bottom: 0;
	left: 0;
	padding: 10rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
// 定义内外边距，历遍1-80
@for $i from 0 through 80 {
	// 只要双数和能被5除尽的数
	@if $i % 2 == 0 or $i % 5 == 0 {
		// 得出：u-margin-30或者u-m-30
		.u-m-#{$i} {
			margin: $i + rpx!important;
		}
		
		// 得出：u-padding-30或者u-p-30
		.u-p-#{$i} {
			padding: $i + rpx!important;
		}
		
		.u-px-#{$i} {
			padding-left: $i + rpx!important;
			padding-right: $i + rpx!important;
		}
		
		.u-py-#{$i} {
			padding-top: $i + rpx!important;
			padding-bottom: $i + rpx!important;
		}
		
		@each $short, $long in l left, t top, r right, b bottom {
			// 缩写版，结果如： u-m-l-30
			// 定义外边距
			.u-m-#{$short}-#{$i} {
				margin-#{$long}: $i + rpx!important;
			}
			
			// 定义内边距
			.u-p-#{$short}-#{$i} {
				padding-#{$long}: $i + rpx!important;
			}
			
		}
	}
}