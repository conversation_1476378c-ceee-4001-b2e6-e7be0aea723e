<template>
	<view class="page">
		<image src="https://file.36sm.cn/xtjyjt/images/common/bjbg.png" mode="" class="background-image"></image>
		<view class="top-status-bar" id="topStatusBar" :style="[{height: statusBarHeight + 'px'},statusStyle]">
			<view class="slot-view" :style="[{paddingTop: slotMarginTop},statusStyle]">
				<view class="topTab u-px-20 flex justify-between align-center status-content" :style="[{width : slotWidth,height:titleHeight}]">
					<view class="tabLeft">
						<view class="home_tab" :class="tabIndex === 1 ? 'tabActive' : ''" @tap="IndexTab(1)">
							<text v-if="title">{{title}}</text>
							<view :class="tabIndex === 1 ? 'line' : ''"></view>
						</view>
						<view class="home_tab" :class="tabIndex === 2 ? 'tabActive' : ''" @tap="IndexTab(2)">
							<text v-if="title1">{{title1}}</text>
						    <view :class="tabIndex === 2 ? 'line' : ''"></view>
						</view>
					</view>
				</view>
				<slot></slot>
			</view>
			<view class="sliderView">
				<swiper class="banner" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000" :circular="true" indicator-color="transparent" indicator-active-color="#ffc47c">
					<swiper-item v-for="(item, i) in BannerList" :key="item.bannerId">
						<image :src="item.imageUrl" style="width: ;" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
			</view>
			
		</view>
		<view :style="[{height: statusBarHeight + 'px'},statusStyle]">
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import {
		getJyInfo,
		getBannerList,
		getOneList
	} from "@/common/request/api/home.js"
	import componentBannerList from '../../components/slider-list/slider';
	import api from '@/common/request/index.js'
	export default {
		name: "top-status-bar",
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			},
			isrefresh:{
				type:Boolean,
				default: false
			},
			width:{
				type: String,
				default: ''
			},
			iconLeft: {
				type: String,
				default: 'arrow-left'
			},
			iconRight: {
				type: String,
				default: ''
			},
			imageSrc:{
				type: String,
				default: ''
			},
			imageWidth:{
				type: String | Number,
				default: '55rpx'
			},
			imageHeight:{
				type: String | Number,
				default: '55rpx'
			},
			title:{
				type: String | Number,
				default: '消费区'
			},
			title1:{
				type: String | Number,
				default: '兑换区'
			},
			iconSize:{
				type: String | Number,
				default: '50rpx'
			},
			textSize:{
				type: String | Number,
				default: '36rpx'
			},
			textColor:{
				type: String,
				default: '#3D3D3D'
			},
			iconColor:{
				type: String,
				default: '#3D3D3D'
			},
			showSlider:{
				type:Boolean,
				default: false
			},
		},
		data() {
			return {
				slotObserver: null,
				slotHeight: 0,
				systemInfo:uni.getStorageSync('cache_shop_system_info_key'),
				JyData: {},
				tabIndex: 1,
				BannerList: []
			}
		},
		components: {
			componentBannerList
		},
		computed: {
			...mapState({
				topStatusBarData: state => state.init.topStatusBarData
			}),
			statusBarHeight() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.statusBarHeight
				// }
				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight',h)
				return h
			},
			slotMarginTop() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.slotMarginTop
				// }
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.slotWidth
				// }
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				// #endif
				return sw
			},
			titleHeight(){
				// if (this.topStatusBarData.isLoad && !this.isrefresh) {
				// 	return this.topStatusBarData.titleHeight
				// }
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			}
		},
		created() {
		},
		mounted() {
			// if (this.topStatusBarData.isLoad && !this.isrefresh) {
			// 	return
			// }
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
			// this.getJyInfo()
			console.log('banner')
			this.getBanner()
		},
		destroyed() {
			// if (this.topStatusBarData.isLoad) {
			// 	return
			// }
			// 取消节点监听
			this.slotObserver?.disconnect()
			let data = {
					statusBarHeight: this.statusBarHeight,
					slotWidth: this.slotWidth,
					slotMarginTop: this.slotMarginTop,
					titleHeight:this.titleHeight,
					isLoad: true
				}
			this.setGlobalData(data)
		},
		methods: {
			...mapMutations(['setTopStatusBarData']),
			setGlobalData(data) {
				this.setTopStatusBarData(data)
			},
			clickLeftIcon(){
				this.$emit('clickLeftIcon')
			},
			clickRightIcon(){
				this.$emit('clickRightIcon')
			},
			IndexTab(index) {
				
				this.tabIndex = index
				console.log(this.tabIndex, 'tabIndex')
				this.$emit('tabChange', index)
			},
			async getJyInfo() {
				const res = await getJyInfo()
				if(res.code == 200) {
					this.JyData = res.data.data
					console.log(this.JyData, '交易信息数据')
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
			},
			// 获取首页轮播图数据
			async getBanner() {
				const res = await getBannerList()
				if (res.code == 200) {
						this.BannerList = res.data.data.list
						console.log(this.BannerList, 'BannerList')
					}else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				
			},
		}
	}
</script>

<style lang="scss" scoped>
	view{
		box-sizing: border-box;
	}
	.page{
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 999;
		/* #ifdef APP */
		height: 400rpx;
		/* #endif */
		
		/* #ifdef MP-WEIXIN */
		  height: 500rpx;
		/* #endif */
		/* #ifdef H5 */
		height: 450rpx;
		/* #endif */
	}
	.background-image {
	    width: 100%;
		height: 400rpx;
	    object-fit: cover; /* 保持宽高比，覆盖整个背景 */
	    z-index: 99; /* 确保背景图在其他内容下 */
		position: absolute;
		top: 0;
		left: 0;
		opacity: 1;
	}
	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		z-index: 999;
		background-color: transparent;
		.slot-view{
			margin-bottom: 20rpx;
			.topTab{
				.tabLeft{
					width: 240rpx;
					display: flex;
					align-items: center;
					justify-content: space-around;
					margin-left: 80rpx;
					.home_tab{
						padding: 0 8rpx -5rpx 8rpx;
						min-width: 140rpx;
						display: flex;
						justify-content: space-between;
						height: 42rpx;
						line-height: 42rpx;
						justify-content: center;
						font-size: 32rpx;
						font-family: PingFang SC, PingFang SC;
						color: #000000;
						font-weight: 650;
						letter-spacing: 2rpx;
						margin: 0 15rpx;
						position: relative;
					}
					.tabActive{
						font-size: 44rpx;
					}
					.line{
						background: linear-gradient(to right, #2A3A88FF, #3c9cff);
						position: absolute;
						width: 100%;
						height: 15rpx;
						border-radius: 20rpx;
						left: 0;
						bottom: -2rpx;
						z-index: -999;
					}
				}
				.tabRight{
					display: flex;
					align-items: center;
					flex: 1;
					margin-left: 160rpx;
					.rate{
						font-size: 22rpx;
						font-weight:blod;
						letter-spacing: 2rpx;
						margin-left: 10rpx;
					}
				}
			}
		}
		
		.sliderView{
			width: 96%;
			margin: 0 auto;
			.banner{
				width: 100% !important;
				height: 330rpx !important;
				border-radius: 10rpx !important;
			}
			.banner image{
				width: 100% !important;
				height: 330rpx !important;
				border-radius: 10rpx !important;
			}
		}
	
	}
</style>